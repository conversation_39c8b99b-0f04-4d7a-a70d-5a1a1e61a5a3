from .base_tool import BaseTool
import os
import shutil

class CopyTool(BaseTool):
    def __init__(self, config: dict):
        self.config = config

    @property
    def name(self) -> str:
        return "copy"

    @property
    def description(self) -> str:
        return "Copy file or directory from source to destination."

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "source": {
                    "type": "string",
                    "description": "The source file or directory path to copy"
                },
                "destination": {
                    "type": "string",
                    "description": "The destination path where the file/directory should be copied"
                },
                "overwrite": {
                    "type": "boolean",
                    "description": "Whether to overwrite destination if it exists (default: False)",
                    "default": False
                },
                "preserve_metadata": {
                    "type": "boolean",
                    "description": "Whether to preserve file metadata like timestamps (default: True)",
                    "default": True
                }
            },
            "required": ["source", "destination"]
        }

    def execute(self, source: str, destination: str, overwrite: bool = False, preserve_metadata: bool = True) -> dict:
        try:
            # Get absolute paths
            abs_source = os.path.abspath(source)
            abs_destination = os.path.abspath(destination)

            # Check if source exists
            if not os.path.exists(abs_source):
                return {"error": f"Source path does not exist: {source}"}

            # Check if destination already exists
            if os.path.exists(abs_destination):
                if not overwrite:
                    return {"error": f"Destination already exists: {destination} (use overwrite=True to replace)"}
                else:
                    # Remove destination if overwriting
                    if os.path.isdir(abs_destination):
                        shutil.rmtree(abs_destination)
                    else:
                        os.remove(abs_destination)

            # Create parent directory of destination if it doesn't exist
            dest_parent = os.path.dirname(abs_destination)
            if dest_parent and not os.path.exists(dest_parent):
                os.makedirs(dest_parent, exist_ok=True)

            # Determine what we're copying
            source_is_file = os.path.isfile(abs_source)
            source_is_dir = os.path.isdir(abs_source)

            if source_is_file:
                # Copy file
                if preserve_metadata:
                    shutil.copy2(abs_source, abs_destination)  # Preserves metadata
                else:
                    shutil.copy(abs_source, abs_destination)   # Just copies content

                copied_size = os.path.getsize(abs_destination)

            elif source_is_dir:
                # Copy directory tree
                if preserve_metadata:
                    shutil.copytree(abs_source, abs_destination, dirs_exist_ok=overwrite)
                else:
                    shutil.copytree(abs_source, abs_destination, dirs_exist_ok=overwrite, copy_function=shutil.copy)

                # Calculate total size
                copied_size = sum(os.path.getsize(os.path.join(dirpath, filename))
                                for dirpath, dirnames, filenames in os.walk(abs_destination)
                                for filename in filenames)
            else:
                return {"error": f"Source is neither a file nor a directory: {source}"}

            item_type = "file" if source_is_file else "directory"

            return {
                "source": abs_source,
                "destination": abs_destination,
                "success": True,
                "message": f"Successfully copied {item_type} from {source} to {destination}",
                "type": item_type,
                "size_bytes": copied_size,
                "preserve_metadata": preserve_metadata,
                "overwrite_used": overwrite
            }

        except PermissionError:
            return {"error": f"Permission denied copying from {source} to {destination}"}
        except OSError as e:
            return {"error": f"OS error copying: {str(e)}"}
        except Exception as e:
            return {"error": f"Failed to copy: {str(e)}"}