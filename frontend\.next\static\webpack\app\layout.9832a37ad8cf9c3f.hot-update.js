"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d702d8f0d7fc\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDcwMmQ4ZjBkN2ZjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./mcp-library-tab.tsx":
/*!*****************************!*\
  !*** ./mcp-library-tab.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpLibraryTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst getInitialMcpServers = ()=>[\n        {\n            id: \"make-it-heavy\",\n            name: \"Make It Heavy Agent\",\n            description: \"Our comprehensive agent system with file operations, terminal commands, web search, calculations, and system management capabilities.\",\n            author: \"Make It Heavy\",\n            version: \"1.0.0\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 59,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"Calculations\"\n            ],\n            status: \"installed\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"playwright\",\n            name: \"Playwright MCP\",\n            description: \"Professional web automation and scraping with Playwright. Navigate websites, take screenshots, extract content, and automate browser interactions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 75,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 61200,\n            tags: [\n                \"Playwright\",\n                \"Web Automation\",\n                \"Screenshots\",\n                \"Browser\"\n            ],\n            status: \"installed\",\n            gradient: \"from-green-500 to-emerald-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"filesystem\",\n            name: \"Filesystem MCP\",\n            description: \"Secure file operations with configurable access controls. Read, write, create, delete, and manage files and directories with advanced permissions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 45300,\n            tags: [\n                \"Files\",\n                \"Directories\",\n                \"Security\",\n                \"Permissions\"\n            ],\n            status: \"available\",\n            gradient: \"from-purple-500 to-pink-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"git\",\n            name: \"Git MCP\",\n            description: \"Tools to read, search, and manipulate Git repositories. Manage commits, branches, and repository operations through MCP.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 107,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 32100,\n            tags: [\n                \"Git\",\n                \"Version Control\",\n                \"Repository\",\n                \"Commits\"\n            ],\n            status: \"available\",\n            gradient: \"from-orange-500 to-red-500\",\n            lastUpdated: \"3 days ago\"\n        },\n        {\n            id: \"memory\",\n            name: \"Memory MCP\",\n            description: \"Knowledge graph-based persistent memory system. Store and retrieve information across conversations with intelligent context management.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"AI\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 123,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.7,\n            downloads: 28900,\n            tags: [\n                \"Memory\",\n                \"Knowledge Graph\",\n                \"Context\",\n                \"AI\"\n            ],\n            status: \"available\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"fetch\",\n            name: \"Fetch MCP\",\n            description: \"Web content fetching and conversion for efficient LLM usage. Convert web pages to markdown and extract structured content.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 139,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.6,\n            downloads: 19800,\n            tags: [\n                \"Web Fetch\",\n                \"Markdown\",\n                \"Content\",\n                \"Conversion\"\n            ],\n            status: \"available\",\n            gradient: \"from-indigo-500 to-purple-500\",\n            lastUpdated: \"2 days ago\"\n        },\n        {\n            id: \"time\",\n            name: \"Time MCP\",\n            description: \"Time and timezone conversion capabilities. Handle dates, times, and timezone operations with precision.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Utility\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 155,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.5,\n            downloads: 15600,\n            tags: [\n                \"Time\",\n                \"Timezone\",\n                \"Date\",\n                \"Conversion\"\n            ],\n            status: \"available\",\n            gradient: \"from-cyan-500 to-blue-500\",\n            lastUpdated: \"1 week ago\"\n        }\n    ];\nconst categories = [\n    \"All\",\n    \"Development\",\n    \"Web\",\n    \"AI\",\n    \"Utility\",\n    \"Database\",\n    \"Communication\"\n];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"bg-green-500\";\n        case \"updating\":\n            return \"bg-yellow-500\";\n        case \"available\":\n            return \"bg-gray-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"Installed\";\n        case \"updating\":\n            return \"Updating\";\n        case \"available\":\n            return \"Available\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction McpLibraryTab() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [mcpServers, setMcpServers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialMcpServers());\n    const [mcpInfo, setMcpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [addServerDialog, setAddServerDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newServer, setNewServer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        command: \"\",\n        args: \"\",\n        description: \"\",\n        category: \"Development\"\n    });\n    const filteredServers = mcpServers.filter((server)=>{\n        const matchesSearch = server.name.toLowerCase().includes(searchQuery.toLowerCase()) || server.description.toLowerCase().includes(searchQuery.toLowerCase()) || server.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"All\" || server.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleInstall = (serverId, serverName)=>{\n        console.log(\"Installing MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for installation logic\n    };\n    const handleUninstall = (serverId, serverName)=>{\n        console.log(\"Uninstalling MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for uninstallation logic\n    };\n    const handleServerSettings = (serverId, serverName)=>{\n        console.log(\"Opening settings for MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for settings logic\n    };\n    const getStatusColorNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"bg-green-500\";\n            case \"updating\":\n                return \"bg-yellow-500\";\n            case \"available\":\n                return \"bg-neutral-500\";\n            default:\n                return \"bg-neutral-500\";\n        }\n    };\n    const getStatusTextNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"Installed\";\n            case \"updating\":\n                return \"Updating\";\n            case \"available\":\n                return \"Available\";\n            default:\n                return \"Unknown\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"MCP Library\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400\",\n                                        children: \"Model Context Protocol servers and integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"secondary\",\n                                className: \"bg-gray-800 text-gray-300\",\n                                children: [\n                                    filteredServers.length,\n                                    \" of \",\n                                    mcpServers.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search MCP servers...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"\".concat(selectedCategory === category ? \"border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300\" : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                        children: filteredServers.map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200\",\n                                children: [\n                                    server.status === \"installed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            handleServerSettings(server.id, server.name);\n                                        },\n                                        className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                        title: \"Settings for \".concat(server.name),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(server.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                                children: server.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0 pr-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white truncate\",\n                                                                children: server.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(getStatusColorNew(server.status))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-neutral-400\",\n                                                                        children: getStatusTextNew(server.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-neutral-400\",\n                                                                children: server.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"v\",\n                                                                    server.version\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"by \",\n                                                                    server.author\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.downloads.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Updated \",\n                                                                    server.lastUpdated\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                        children: server.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: server.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: server.status === \"installed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleUninstall(server.id, server.name),\n                                            variant: \"outline\",\n                                            className: \"flex-1 border-neutral-700 text-neutral-300 hover:bg-neutral-800 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Installed\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this) : server.status === \"updating\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            disabled: true,\n                                            className: \"flex-1 bg-yellow-600/20 text-yellow-400 border border-yellow-600/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleInstall(server.id, server.name),\n                                            className: \"flex-1 bg-blue-gradient-hover text-white shadow-lg border-0 transition-all duration-200 group-hover:shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Install\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, server.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this),\n                    filteredServers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-400 mb-2\",\n                                    children: \"No MCP servers found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Try adjusting your search or filter criteria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(McpLibraryTab, \"lrhpCeUGRoSmWlUUpRv0OkVKiAM=\");\n_c = McpLibraryTab;\nvar _c;\n$RefreshReg$(_c, \"McpLibraryTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL21jcC1saWJyYXJ5LXRhYi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRzJDO0FBQ0k7QUFDUztBQUNYO0FBQ0E7QUF1QnhCO0FBbUJyQixNQUFNZ0IsdUJBQXVCLElBQW1CO1FBQzlDO1lBQ0VDLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxhQUNFO1lBQ0ZDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLG9CQUFNLDhEQUFDVCwySkFBS0E7Z0JBQUNVLFdBQVU7Ozs7OztZQUN2QkMsUUFBUTtZQUNSQyxXQUFXO1lBQ1hDLE1BQU07Z0JBQUM7Z0JBQW1CO2dCQUFZO2dCQUFjO2FBQWU7WUFDbkVDLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFYixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsYUFDRTtZQUNGQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxvQkFBTSw4REFBQ2xCLDJKQUFLQTtnQkFBQ21CLFdBQVU7Ozs7OztZQUN2QkMsUUFBUTtZQUNSQyxXQUFXO1lBQ1hDLE1BQU07Z0JBQUM7Z0JBQWM7Z0JBQWtCO2dCQUFlO2FBQVU7WUFDaEVDLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFYixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsYUFDRTtZQUNGQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxvQkFBTSw4REFBQ2pCLDJKQUFRQTtnQkFBQ2tCLFdBQVU7Ozs7OztZQUMxQkMsUUFBUTtZQUNSQyxXQUFXO1lBQ1hDLE1BQU07Z0JBQUM7Z0JBQVM7Z0JBQWU7Z0JBQVk7YUFBYztZQUN6REMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLGFBQWE7UUFDZjtRQUNBO1lBQ0ViLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxhQUNFO1lBQ0ZDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLG9CQUFNLDhEQUFDaEIsMkpBQUlBO2dCQUFDaUIsV0FBVTs7Ozs7O1lBQ3RCQyxRQUFRO1lBQ1JDLFdBQVc7WUFDWEMsTUFBTTtnQkFBQztnQkFBTztnQkFBbUI7Z0JBQWM7YUFBVTtZQUN6REMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLGFBQWE7UUFDZjtRQUNBO1lBQ0ViLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxhQUNFO1lBQ0ZDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLG9CQUFNLDhEQUFDVCwySkFBS0E7Z0JBQUNVLFdBQVU7Ozs7OztZQUN2QkMsUUFBUTtZQUNSQyxXQUFXO1lBQ1hDLE1BQU07Z0JBQUM7Z0JBQVU7Z0JBQW1CO2dCQUFXO2FBQUs7WUFDcERDLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFYixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsYUFDRTtZQUNGQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxvQkFBTSw4REFBQ2QsNEpBQVFBO2dCQUFDZSxXQUFVOzs7Ozs7WUFDMUJDLFFBQVE7WUFDUkMsV0FBVztZQUNYQyxNQUFNO2dCQUFDO2dCQUFhO2dCQUFZO2dCQUFXO2FBQWE7WUFDeERDLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFYixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsYUFDRTtZQUNGQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxvQkFBTSw4REFBQ1IsNEpBQUdBO2dCQUFDUyxXQUFVOzs7Ozs7WUFDckJDLFFBQVE7WUFDUkMsV0FBVztZQUNYQyxNQUFNO2dCQUFDO2dCQUFRO2dCQUFZO2dCQUFRO2FBQWE7WUFDaERDLFFBQVE7WUFDUkMsVUFBVTtZQUNWQyxhQUFhO1FBQ2Y7S0FDRDtBQUVELE1BQU1DLGFBQWE7SUFBQztJQUFPO0lBQWU7SUFBTztJQUFNO0lBQVc7SUFBWTtDQUFnQjtBQUU5RixNQUFNQyxpQkFBaUIsQ0FBQ0o7SUFDdEIsT0FBUUE7UUFDTixLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVDtZQUNFLE9BQU87SUFDWDtBQUNGO0FBRUEsTUFBTUssZ0JBQWdCLENBQUNMO0lBQ3JCLE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBQ1Q7WUFDRSxPQUFPO0lBQ1g7QUFDRjtBQUVlLFNBQVNNOztJQUN0QixNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3FDLGtCQUFrQkMsb0JBQW9CLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUN1QyxZQUFZQyxjQUFjLEdBQUd4QywrQ0FBUUEsQ0FBY2dCO0lBQzFELE1BQU0sQ0FBQ3lCLFNBQVNDLFdBQVcsR0FBRzFDLCtDQUFRQSxDQUFpQjtJQUN2RCxNQUFNLENBQUMyQyxTQUFTQyxXQUFXLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM2QyxpQkFBaUJDLG1CQUFtQixHQUFHOUMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDK0MsV0FBV0MsYUFBYSxHQUFHaEQsK0NBQVFBLENBQUM7UUFDekNrQixNQUFNO1FBQ04rQixTQUFTO1FBQ1RDLE1BQU07UUFDTi9CLGFBQWE7UUFDYkcsVUFBVTtJQUNaO0lBRUEsTUFBTTZCLGtCQUFrQlosV0FBV2EsTUFBTSxDQUFDLENBQUNDO1FBQ3pDLE1BQU1DLGdCQUNKRCxPQUFPbkMsSUFBSSxDQUFDcUMsV0FBVyxHQUFHQyxRQUFRLENBQUNyQixZQUFZb0IsV0FBVyxPQUMxREYsT0FBT2xDLFdBQVcsQ0FBQ29DLFdBQVcsR0FBR0MsUUFBUSxDQUFDckIsWUFBWW9CLFdBQVcsT0FDakVGLE9BQU8xQixJQUFJLENBQUM4QixJQUFJLENBQUMsQ0FBQ0MsTUFBUUEsSUFBSUgsV0FBVyxHQUFHQyxRQUFRLENBQUNyQixZQUFZb0IsV0FBVztRQUU5RSxNQUFNSSxrQkFBa0J0QixxQkFBcUIsU0FBU2dCLE9BQU8vQixRQUFRLEtBQUtlO1FBRTFFLE9BQU9pQixpQkFBaUJLO0lBQzFCO0lBRUEsTUFBTUMsZ0JBQWdCLENBQUNDLFVBQWtCQztRQUN2Q0MsUUFBUUMsR0FBRyxDQUFDLDBCQUE2Q0gsT0FBbkJDLFlBQVcsVUFBaUIsT0FBVEQsVUFBUztJQUNsRSxxQ0FBcUM7SUFDdkM7SUFFQSxNQUFNSSxrQkFBa0IsQ0FBQ0osVUFBa0JDO1FBQ3pDQyxRQUFRQyxHQUFHLENBQUMsNEJBQStDSCxPQUFuQkMsWUFBVyxVQUFpQixPQUFURCxVQUFTO0lBQ3BFLHVDQUF1QztJQUN6QztJQUVBLE1BQU1LLHVCQUF1QixDQUFDTCxVQUFrQkM7UUFDOUNDLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBdURILE9BQW5CQyxZQUFXLFVBQWlCLE9BQVRELFVBQVM7SUFDNUUsaUNBQWlDO0lBQ25DO0lBRUEsTUFBTU0sb0JBQW9CLENBQUN2QztRQUN6QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTXdDLG1CQUFtQixDQUFDeEM7UUFDeEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDeUM7UUFBSTdDLFdBQVU7OzBCQUViLDhEQUFDNkM7Z0JBQUk3QyxXQUFVOztrQ0FDYiw4REFBQzZDO3dCQUFJN0MsV0FBVTs7MENBQ2IsOERBQUM2Qzs7a0RBQ0MsOERBQUNDO3dDQUFHOUMsV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUMrQzt3Q0FBRS9DLFdBQVU7a0RBQTJCOzs7Ozs7Ozs7Ozs7MENBRTFDLDhEQUFDckIsdURBQUtBO2dDQUFDcUUsU0FBUTtnQ0FBWWhELFdBQVU7O29DQUNsQzJCLGdCQUFnQnNCLE1BQU07b0NBQUM7b0NBQUtsQyxXQUFXa0MsTUFBTTs7Ozs7Ozs7Ozs7OztrQ0FLbEQsOERBQUNKO3dCQUFJN0MsV0FBVTs7MENBQ2IsOERBQUM2QztnQ0FBSTdDLFdBQVU7O2tEQUNiLDhEQUFDaEIsNEpBQU1BO3dDQUFDZ0IsV0FBVTs7Ozs7O2tEQUNsQiw4REFBQ3BCLHVEQUFLQTt3Q0FDSnNFLGFBQVk7d0NBQ1pDLE9BQU94Qzt3Q0FDUHlDLFVBQVUsQ0FBQ0MsSUFBTXpDLGVBQWV5QyxFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQzlDbkQsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUlkLDhEQUFDNkM7Z0NBQUk3QyxXQUFVOzBDQUNaTyxXQUFXZ0QsR0FBRyxDQUFDLENBQUN6RCx5QkFDZiw4REFBQ3JCLHlEQUFNQTt3Q0FFTHVFLFNBQVE7d0NBQ1JRLE1BQUs7d0NBQ0xDLFNBQVMsSUFBTTNDLG9CQUFvQmhCO3dDQUNuQ0UsV0FBVyxHQUlWLE9BSENhLHFCQUFxQmYsV0FDakIsOEVBQ0E7a0RBR0xBO3VDQVZJQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFrQmYsOERBQUNwQixrRUFBVUE7Z0JBQUNzQixXQUFVOztrQ0FDcEIsOERBQUM2Qzt3QkFBSTdDLFdBQVU7a0NBQ1oyQixnQkFBZ0I0QixHQUFHLENBQUMsQ0FBQzFCLHVCQUNwQiw4REFBQ2dCO2dDQUVDN0MsV0FBVTs7b0NBR1Q2QixPQUFPekIsTUFBTSxLQUFLLDZCQUNqQiw4REFBQ3NEO3dDQUNDRCxTQUFTLENBQUNKOzRDQUNSQSxFQUFFTSxlQUFlOzRDQUNqQmpCLHFCQUFxQmIsT0FBT3BDLEVBQUUsRUFBRW9DLE9BQU9uQyxJQUFJO3dDQUM3Qzt3Q0FDQU0sV0FBVTt3Q0FDVjRELE9BQU8sZ0JBQTRCLE9BQVovQixPQUFPbkMsSUFBSTtrREFFbEMsNEVBQUNOLDRKQUFRQTs0Q0FBQ1ksV0FBVTs7Ozs7Ozs7Ozs7a0RBS3hCLDhEQUFDNkM7d0NBQUk3QyxXQUFVOzswREFDYiw4REFBQzZDO2dEQUNDN0MsV0FBVywwQ0FBMEQsT0FBaEI2QixPQUFPeEIsUUFBUSxFQUFDOzBEQUVwRXdCLE9BQU85QixJQUFJOzs7Ozs7MERBR2QsOERBQUM4QztnREFBSTdDLFdBQVU7O2tFQUNiLDhEQUFDNkM7d0RBQUk3QyxXQUFVOzswRUFDYiw4REFBQzZEO2dFQUFHN0QsV0FBVTswRUFBcUM2QixPQUFPbkMsSUFBSTs7Ozs7OzBFQUM5RCw4REFBQ21EO2dFQUFJN0MsV0FBVTs7a0ZBQ2IsOERBQUM2Qzt3RUFBSTdDLFdBQVcsd0JBQXlELE9BQWpDMkMsa0JBQWtCZCxPQUFPekIsTUFBTTs7Ozs7O2tGQUN2RSw4REFBQzBEO3dFQUFLOUQsV0FBVTtrRkFBNEI0QyxpQkFBaUJmLE9BQU96QixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSTlFLDhEQUFDeUM7d0RBQUk3QyxXQUFVOzswRUFDYiw4REFBQytDO2dFQUFFL0MsV0FBVTswRUFBNEI2QixPQUFPL0IsUUFBUTs7Ozs7OzBFQUN4RCw4REFBQ2dFO2dFQUFLOUQsV0FBVTswRUFBd0I7Ozs7OzswRUFDeEMsOERBQUMrQztnRUFBRS9DLFdBQVU7O29FQUF3QjtvRUFBRTZCLE9BQU9oQyxPQUFPOzs7Ozs7OzBFQUNyRCw4REFBQ2lFO2dFQUFLOUQsV0FBVTswRUFBd0I7Ozs7OzswRUFDeEMsOERBQUMrQztnRUFBRS9DLFdBQVU7O29FQUF3QjtvRUFBSTZCLE9BQU9qQyxNQUFNOzs7Ozs7Ozs7Ozs7O2tFQUd4RCw4REFBQ2lEO3dEQUFJN0MsV0FBVTs7MEVBQ2IsOERBQUM2QztnRUFBSTdDLFdBQVU7O2tGQUNiLDhEQUFDYiw0SkFBSUE7d0VBQUNhLFdBQVU7Ozs7OztrRkFDaEIsOERBQUM4RDtrRkFBTWpDLE9BQU81QixNQUFNOzs7Ozs7Ozs7Ozs7MEVBRXRCLDhEQUFDNEM7Z0VBQUk3QyxXQUFVOztrRkFDYiw4REFBQ2YsNEpBQVFBO3dFQUFDZSxXQUFVOzs7Ozs7a0ZBQ3BCLDhEQUFDOEQ7a0ZBQU1qQyxPQUFPM0IsU0FBUyxDQUFDNkQsY0FBYzs7Ozs7Ozs7Ozs7OzBFQUV4Qyw4REFBQ0Q7O29FQUFLO29FQUFTakMsT0FBT3ZCLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTXZDLDhEQUFDeUM7d0NBQUUvQyxXQUFVO2tEQUFpRDZCLE9BQU9sQyxXQUFXOzs7Ozs7a0RBR2hGLDhEQUFDa0Q7d0NBQUk3QyxXQUFVO2tEQUNaNkIsT0FBTzFCLElBQUksQ0FBQ29ELEdBQUcsQ0FBQyxDQUFDckIsb0JBQ2hCLDhEQUFDdkQsdURBQUtBO2dEQUVKcUUsU0FBUTtnREFDUmhELFdBQVU7MERBRVRrQzsrQ0FKSUE7Ozs7Ozs7Ozs7a0RBVVgsOERBQUNXO3dDQUFJN0MsV0FBVTtrREFDWjZCLE9BQU96QixNQUFNLEtBQUssNEJBQ2pCLDhEQUFDM0IseURBQU1BOzRDQUNMZ0YsU0FBUyxJQUFNaEIsZ0JBQWdCWixPQUFPcEMsRUFBRSxFQUFFb0MsT0FBT25DLElBQUk7NENBQ3JEc0QsU0FBUTs0Q0FDUmhELFdBQVU7OzhEQUVWLDhEQUFDZCw0SkFBV0E7b0RBQUNjLFdBQVU7Ozs7OztnREFBaUI7Ozs7OzttREFHeEM2QixPQUFPekIsTUFBTSxLQUFLLDJCQUNwQiw4REFBQzNCLHlEQUFNQTs0Q0FBQ3VGLFFBQVE7NENBQUNoRSxXQUFVOzs4REFDekIsOERBQUNYLDRKQUFPQTtvREFBQ1csV0FBVTs7Ozs7O2dEQUE4Qjs7Ozs7O2lFQUluRCw4REFBQ3ZCLHlEQUFNQTs0Q0FDTGdGLFNBQVMsSUFBTXJCLGNBQWNQLE9BQU9wQyxFQUFFLEVBQUVvQyxPQUFPbkMsSUFBSTs0Q0FDbkRNLFdBQVk7OzhEQUVaLDhEQUFDZiw0SkFBUUE7b0RBQUNlLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7Ozs7OztrREFPM0MsOERBQUM2Qzt3Q0FBSTdDLFdBQVU7Ozs7Ozs7K0JBcEdWNkIsT0FBT3BDLEVBQUU7Ozs7Ozs7Ozs7b0JBeUduQmtDLGdCQUFnQnNCLE1BQU0sS0FBSyxtQkFDMUIsOERBQUNKO3dCQUFJN0MsV0FBVTtrQ0FDYiw0RUFBQzZDOzRCQUFJN0MsV0FBVTs7OENBQ2IsOERBQUNYLDRKQUFPQTtvQ0FBQ1csV0FBVTs7Ozs7OzhDQUNuQiw4REFBQzZEO29DQUFHN0QsV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUMrQztvQ0FBRS9DLFdBQVU7OENBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9uRDtHQTNPd0JVO0tBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEd1ZXJyXFxEb3dubG9hZHNcXG1ha2UtaXQtaGVhdnktbWFpblxcbWFrZS1pdC1oZWF2eS1tYWluXFxmcm9udGVuZFxcbWNwLWxpYnJhcnktdGFiLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgdHlwZSBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBTY3JvbGxBcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zY3JvbGwtYXJlYVwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCJcbmltcG9ydCB7IERpYWxvZywgRGlhbG9nQ29udGVudCwgRGlhbG9nRGVzY3JpcHRpb24sIERpYWxvZ0hlYWRlciwgRGlhbG9nVGl0bGUsIERpYWxvZ1RyaWdnZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2RpYWxvZ1wiXG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGV4dGFyZWFcIlxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCJcbmltcG9ydCB7XG4gIERhdGFiYXNlLFxuICBHbG9iZSxcbiAgRmlsZVRleHQsXG4gIENhbGVuZGFyLFxuICBNYWlsLFxuICBDb2RlLFxuICBTZWFyY2gsXG4gIERvd25sb2FkLFxuICBDaGVja0NpcmNsZSxcbiAgU3RhcixcbiAgU2V0dGluZ3MsXG4gIFBhY2thZ2UsXG4gIFBsdXMsXG4gIFRlcm1pbmFsLFxuICBCcmFpbixcbiAgWmFwLFxuICBFeHRlcm5hbExpbmssXG4gIExvYWRlcjJcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyBhcGlDbGllbnQsIHR5cGUgTUNQSW5mbyB9IGZyb20gXCJAL2xpYi9hcGlcIlxuXG5pbnRlcmZhY2UgTWNwU2VydmVyIHtcbiAgaWQ6IHN0cmluZ1xuICBuYW1lOiBzdHJpbmdcbiAgZGVzY3JpcHRpb246IHN0cmluZ1xuICBhdXRob3I6IHN0cmluZ1xuICB2ZXJzaW9uOiBzdHJpbmdcbiAgY2F0ZWdvcnk6IHN0cmluZ1xuICBpY29uOiBSZWFjdC5SZWFjdE5vZGVcbiAgcmF0aW5nOiBudW1iZXJcbiAgZG93bmxvYWRzOiBudW1iZXJcbiAgdGFnczogc3RyaW5nW11cbiAgc3RhdHVzOiBcImluc3RhbGxlZFwiIHwgXCJhdmFpbGFibGVcIiB8IFwidXBkYXRpbmdcIlxuICBncmFkaWVudDogc3RyaW5nXG4gIGxhc3RVcGRhdGVkOiBzdHJpbmdcbn1cblxuY29uc3QgZ2V0SW5pdGlhbE1jcFNlcnZlcnMgPSAoKTogTWNwU2VydmVyW10gPT4gW1xuICB7XG4gICAgaWQ6IFwibWFrZS1pdC1oZWF2eVwiLFxuICAgIG5hbWU6IFwiTWFrZSBJdCBIZWF2eSBBZ2VudFwiLFxuICAgIGRlc2NyaXB0aW9uOlxuICAgICAgXCJPdXIgY29tcHJlaGVuc2l2ZSBhZ2VudCBzeXN0ZW0gd2l0aCBmaWxlIG9wZXJhdGlvbnMsIHRlcm1pbmFsIGNvbW1hbmRzLCB3ZWIgc2VhcmNoLCBjYWxjdWxhdGlvbnMsIGFuZCBzeXN0ZW0gbWFuYWdlbWVudCBjYXBhYmlsaXRpZXMuXCIsXG4gICAgYXV0aG9yOiBcIk1ha2UgSXQgSGVhdnlcIixcbiAgICB2ZXJzaW9uOiBcIjEuMC4wXCIsXG4gICAgY2F0ZWdvcnk6IFwiRGV2ZWxvcG1lbnRcIixcbiAgICBpY29uOiA8QnJhaW4gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+LFxuICAgIHJhdGluZzogNC45LFxuICAgIGRvd25sb2FkczogMSxcbiAgICB0YWdzOiBbXCJGaWxlIE9wZXJhdGlvbnNcIiwgXCJUZXJtaW5hbFwiLCBcIldlYiBTZWFyY2hcIiwgXCJDYWxjdWxhdGlvbnNcIl0sXG4gICAgc3RhdHVzOiBcImluc3RhbGxlZFwiLFxuICAgIGdyYWRpZW50OiBcImZyb20tYmx1ZS01MDAgdG8tY3lhbi01MDBcIixcbiAgICBsYXN0VXBkYXRlZDogXCJBY3RpdmVcIixcbiAgfSxcbiAge1xuICAgIGlkOiBcInBsYXl3cmlnaHRcIixcbiAgICBuYW1lOiBcIlBsYXl3cmlnaHQgTUNQXCIsXG4gICAgZGVzY3JpcHRpb246XG4gICAgICBcIlByb2Zlc3Npb25hbCB3ZWIgYXV0b21hdGlvbiBhbmQgc2NyYXBpbmcgd2l0aCBQbGF5d3JpZ2h0LiBOYXZpZ2F0ZSB3ZWJzaXRlcywgdGFrZSBzY3JlZW5zaG90cywgZXh0cmFjdCBjb250ZW50LCBhbmQgYXV0b21hdGUgYnJvd3NlciBpbnRlcmFjdGlvbnMuXCIsXG4gICAgYXV0aG9yOiBcIkFudGhyb3BpY1wiLFxuICAgIHZlcnNpb246IFwibGF0ZXN0XCIsXG4gICAgY2F0ZWdvcnk6IFwiV2ViXCIsXG4gICAgaWNvbjogPEdsb2JlIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPixcbiAgICByYXRpbmc6IDQuOCxcbiAgICBkb3dubG9hZHM6IDYxMjAwLFxuICAgIHRhZ3M6IFtcIlBsYXl3cmlnaHRcIiwgXCJXZWIgQXV0b21hdGlvblwiLCBcIlNjcmVlbnNob3RzXCIsIFwiQnJvd3NlclwiXSxcbiAgICBzdGF0dXM6IFwiaW5zdGFsbGVkXCIsXG4gICAgZ3JhZGllbnQ6IFwiZnJvbS1ncmVlbi01MDAgdG8tZW1lcmFsZC01MDBcIixcbiAgICBsYXN0VXBkYXRlZDogXCJBY3RpdmVcIixcbiAgfSxcbiAge1xuICAgIGlkOiBcImZpbGVzeXN0ZW1cIixcbiAgICBuYW1lOiBcIkZpbGVzeXN0ZW0gTUNQXCIsXG4gICAgZGVzY3JpcHRpb246XG4gICAgICBcIlNlY3VyZSBmaWxlIG9wZXJhdGlvbnMgd2l0aCBjb25maWd1cmFibGUgYWNjZXNzIGNvbnRyb2xzLiBSZWFkLCB3cml0ZSwgY3JlYXRlLCBkZWxldGUsIGFuZCBtYW5hZ2UgZmlsZXMgYW5kIGRpcmVjdG9yaWVzIHdpdGggYWR2YW5jZWQgcGVybWlzc2lvbnMuXCIsXG4gICAgYXV0aG9yOiBcIkFudGhyb3BpY1wiLFxuICAgIHZlcnNpb246IFwibGF0ZXN0XCIsXG4gICAgY2F0ZWdvcnk6IFwiRGV2ZWxvcG1lbnRcIixcbiAgICBpY29uOiA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+LFxuICAgIHJhdGluZzogNC45LFxuICAgIGRvd25sb2FkczogNDUzMDAsXG4gICAgdGFnczogW1wiRmlsZXNcIiwgXCJEaXJlY3Rvcmllc1wiLCBcIlNlY3VyaXR5XCIsIFwiUGVybWlzc2lvbnNcIl0sXG4gICAgc3RhdHVzOiBcImF2YWlsYWJsZVwiLFxuICAgIGdyYWRpZW50OiBcImZyb20tcHVycGxlLTUwMCB0by1waW5rLTUwMFwiLFxuICAgIGxhc3RVcGRhdGVkOiBcIjEgd2VlayBhZ29cIixcbiAgfSxcbiAge1xuICAgIGlkOiBcImdpdFwiLFxuICAgIG5hbWU6IFwiR2l0IE1DUFwiLFxuICAgIGRlc2NyaXB0aW9uOlxuICAgICAgXCJUb29scyB0byByZWFkLCBzZWFyY2gsIGFuZCBtYW5pcHVsYXRlIEdpdCByZXBvc2l0b3JpZXMuIE1hbmFnZSBjb21taXRzLCBicmFuY2hlcywgYW5kIHJlcG9zaXRvcnkgb3BlcmF0aW9ucyB0aHJvdWdoIE1DUC5cIixcbiAgICBhdXRob3I6IFwiQW50aHJvcGljXCIsXG4gICAgdmVyc2lvbjogXCJsYXRlc3RcIixcbiAgICBjYXRlZ29yeTogXCJEZXZlbG9wbWVudFwiLFxuICAgIGljb246IDxDb2RlIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPixcbiAgICByYXRpbmc6IDQuOCxcbiAgICBkb3dubG9hZHM6IDMyMTAwLFxuICAgIHRhZ3M6IFtcIkdpdFwiLCBcIlZlcnNpb24gQ29udHJvbFwiLCBcIlJlcG9zaXRvcnlcIiwgXCJDb21taXRzXCJdLFxuICAgIHN0YXR1czogXCJhdmFpbGFibGVcIixcbiAgICBncmFkaWVudDogXCJmcm9tLW9yYW5nZS01MDAgdG8tcmVkLTUwMFwiLFxuICAgIGxhc3RVcGRhdGVkOiBcIjMgZGF5cyBhZ29cIixcbiAgfSxcbiAge1xuICAgIGlkOiBcIm1lbW9yeVwiLFxuICAgIG5hbWU6IFwiTWVtb3J5IE1DUFwiLFxuICAgIGRlc2NyaXB0aW9uOlxuICAgICAgXCJLbm93bGVkZ2UgZ3JhcGgtYmFzZWQgcGVyc2lzdGVudCBtZW1vcnkgc3lzdGVtLiBTdG9yZSBhbmQgcmV0cmlldmUgaW5mb3JtYXRpb24gYWNyb3NzIGNvbnZlcnNhdGlvbnMgd2l0aCBpbnRlbGxpZ2VudCBjb250ZXh0IG1hbmFnZW1lbnQuXCIsXG4gICAgYXV0aG9yOiBcIkFudGhyb3BpY1wiLFxuICAgIHZlcnNpb246IFwibGF0ZXN0XCIsXG4gICAgY2F0ZWdvcnk6IFwiQUlcIixcbiAgICBpY29uOiA8QnJhaW4gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+LFxuICAgIHJhdGluZzogNC43LFxuICAgIGRvd25sb2FkczogMjg5MDAsXG4gICAgdGFnczogW1wiTWVtb3J5XCIsIFwiS25vd2xlZGdlIEdyYXBoXCIsIFwiQ29udGV4dFwiLCBcIkFJXCJdLFxuICAgIHN0YXR1czogXCJhdmFpbGFibGVcIixcbiAgICBncmFkaWVudDogXCJmcm9tLXllbGxvdy01MDAgdG8tb3JhbmdlLTUwMFwiLFxuICAgIGxhc3RVcGRhdGVkOiBcIjEgd2VlayBhZ29cIixcbiAgfSxcbiAge1xuICAgIGlkOiBcImZldGNoXCIsXG4gICAgbmFtZTogXCJGZXRjaCBNQ1BcIixcbiAgICBkZXNjcmlwdGlvbjpcbiAgICAgIFwiV2ViIGNvbnRlbnQgZmV0Y2hpbmcgYW5kIGNvbnZlcnNpb24gZm9yIGVmZmljaWVudCBMTE0gdXNhZ2UuIENvbnZlcnQgd2ViIHBhZ2VzIHRvIG1hcmtkb3duIGFuZCBleHRyYWN0IHN0cnVjdHVyZWQgY29udGVudC5cIixcbiAgICBhdXRob3I6IFwiQW50aHJvcGljXCIsXG4gICAgdmVyc2lvbjogXCJsYXRlc3RcIixcbiAgICBjYXRlZ29yeTogXCJXZWJcIixcbiAgICBpY29uOiA8RG93bmxvYWQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+LFxuICAgIHJhdGluZzogNC42LFxuICAgIGRvd25sb2FkczogMTk4MDAsXG4gICAgdGFnczogW1wiV2ViIEZldGNoXCIsIFwiTWFya2Rvd25cIiwgXCJDb250ZW50XCIsIFwiQ29udmVyc2lvblwiXSxcbiAgICBzdGF0dXM6IFwiYXZhaWxhYmxlXCIsXG4gICAgZ3JhZGllbnQ6IFwiZnJvbS1pbmRpZ28tNTAwIHRvLXB1cnBsZS01MDBcIixcbiAgICBsYXN0VXBkYXRlZDogXCIyIGRheXMgYWdvXCIsXG4gIH0sXG4gIHtcbiAgICBpZDogXCJ0aW1lXCIsXG4gICAgbmFtZTogXCJUaW1lIE1DUFwiLFxuICAgIGRlc2NyaXB0aW9uOlxuICAgICAgXCJUaW1lIGFuZCB0aW1lem9uZSBjb252ZXJzaW9uIGNhcGFiaWxpdGllcy4gSGFuZGxlIGRhdGVzLCB0aW1lcywgYW5kIHRpbWV6b25lIG9wZXJhdGlvbnMgd2l0aCBwcmVjaXNpb24uXCIsXG4gICAgYXV0aG9yOiBcIkFudGhyb3BpY1wiLFxuICAgIHZlcnNpb246IFwibGF0ZXN0XCIsXG4gICAgY2F0ZWdvcnk6IFwiVXRpbGl0eVwiLFxuICAgIGljb246IDxaYXAgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+LFxuICAgIHJhdGluZzogNC41LFxuICAgIGRvd25sb2FkczogMTU2MDAsXG4gICAgdGFnczogW1wiVGltZVwiLCBcIlRpbWV6b25lXCIsIFwiRGF0ZVwiLCBcIkNvbnZlcnNpb25cIl0sXG4gICAgc3RhdHVzOiBcImF2YWlsYWJsZVwiLFxuICAgIGdyYWRpZW50OiBcImZyb20tY3lhbi01MDAgdG8tYmx1ZS01MDBcIixcbiAgICBsYXN0VXBkYXRlZDogXCIxIHdlZWsgYWdvXCIsXG4gIH1cbl1cblxuY29uc3QgY2F0ZWdvcmllcyA9IFtcIkFsbFwiLCBcIkRldmVsb3BtZW50XCIsIFwiV2ViXCIsIFwiQUlcIiwgXCJVdGlsaXR5XCIsIFwiRGF0YWJhc2VcIiwgXCJDb21tdW5pY2F0aW9uXCJdXG5cbmNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgY2FzZSBcImluc3RhbGxlZFwiOlxuICAgICAgcmV0dXJuIFwiYmctZ3JlZW4tNTAwXCJcbiAgICBjYXNlIFwidXBkYXRpbmdcIjpcbiAgICAgIHJldHVybiBcImJnLXllbGxvdy01MDBcIlxuICAgIGNhc2UgXCJhdmFpbGFibGVcIjpcbiAgICAgIHJldHVybiBcImJnLWdyYXktNTAwXCJcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIFwiYmctZ3JheS01MDBcIlxuICB9XG59XG5cbmNvbnN0IGdldFN0YXR1c1RleHQgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICBjYXNlIFwiaW5zdGFsbGVkXCI6XG4gICAgICByZXR1cm4gXCJJbnN0YWxsZWRcIlxuICAgIGNhc2UgXCJ1cGRhdGluZ1wiOlxuICAgICAgcmV0dXJuIFwiVXBkYXRpbmdcIlxuICAgIGNhc2UgXCJhdmFpbGFibGVcIjpcbiAgICAgIHJldHVybiBcIkF2YWlsYWJsZVwiXG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiBcIlVua25vd25cIlxuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1jcExpYnJhcnlUYWIoKSB7XG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoXCJcIilcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGUoXCJBbGxcIilcbiAgY29uc3QgW21jcFNlcnZlcnMsIHNldE1jcFNlcnZlcnNdID0gdXNlU3RhdGU8TWNwU2VydmVyW10+KGdldEluaXRpYWxNY3BTZXJ2ZXJzKCkpXG4gIGNvbnN0IFttY3BJbmZvLCBzZXRNY3BJbmZvXSA9IHVzZVN0YXRlPE1DUEluZm8gfCBudWxsPihudWxsKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbYWRkU2VydmVyRGlhbG9nLCBzZXRBZGRTZXJ2ZXJEaWFsb2ddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtuZXdTZXJ2ZXIsIHNldE5ld1NlcnZlcl0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogXCJcIixcbiAgICBjb21tYW5kOiBcIlwiLFxuICAgIGFyZ3M6IFwiXCIsXG4gICAgZGVzY3JpcHRpb246IFwiXCIsXG4gICAgY2F0ZWdvcnk6IFwiRGV2ZWxvcG1lbnRcIlxuICB9KVxuXG4gIGNvbnN0IGZpbHRlcmVkU2VydmVycyA9IG1jcFNlcnZlcnMuZmlsdGVyKChzZXJ2ZXIpID0+IHtcbiAgICBjb25zdCBtYXRjaGVzU2VhcmNoID1cbiAgICAgIHNlcnZlci5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgIHNlcnZlci5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICBzZXJ2ZXIudGFncy5zb21lKCh0YWcpID0+IHRhZy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpKVxuXG4gICAgY29uc3QgbWF0Y2hlc0NhdGVnb3J5ID0gc2VsZWN0ZWRDYXRlZ29yeSA9PT0gXCJBbGxcIiB8fCBzZXJ2ZXIuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnlcblxuICAgIHJldHVybiBtYXRjaGVzU2VhcmNoICYmIG1hdGNoZXNDYXRlZ29yeVxuICB9KVxuXG4gIGNvbnN0IGhhbmRsZUluc3RhbGwgPSAoc2VydmVySWQ6IHN0cmluZywgc2VydmVyTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coYEluc3RhbGxpbmcgTUNQIHNlcnZlcjogJHtzZXJ2ZXJOYW1lfSAoSUQ6ICR7c2VydmVySWR9KWApXG4gICAgLy8gUGxhY2Vob2xkZXIgZm9yIGluc3RhbGxhdGlvbiBsb2dpY1xuICB9XG5cbiAgY29uc3QgaGFuZGxlVW5pbnN0YWxsID0gKHNlcnZlcklkOiBzdHJpbmcsIHNlcnZlck5hbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKGBVbmluc3RhbGxpbmcgTUNQIHNlcnZlcjogJHtzZXJ2ZXJOYW1lfSAoSUQ6ICR7c2VydmVySWR9KWApXG4gICAgLy8gUGxhY2Vob2xkZXIgZm9yIHVuaW5zdGFsbGF0aW9uIGxvZ2ljXG4gIH1cblxuICBjb25zdCBoYW5kbGVTZXJ2ZXJTZXR0aW5ncyA9IChzZXJ2ZXJJZDogc3RyaW5nLCBzZXJ2ZXJOYW1lOiBzdHJpbmcpID0+IHtcbiAgICBjb25zb2xlLmxvZyhgT3BlbmluZyBzZXR0aW5ncyBmb3IgTUNQIHNlcnZlcjogJHtzZXJ2ZXJOYW1lfSAoSUQ6ICR7c2VydmVySWR9KWApXG4gICAgLy8gUGxhY2Vob2xkZXIgZm9yIHNldHRpbmdzIGxvZ2ljXG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNDb2xvck5ldyA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlIFwiaW5zdGFsbGVkXCI6XG4gICAgICAgIHJldHVybiBcImJnLWdyZWVuLTUwMFwiXG4gICAgICBjYXNlIFwidXBkYXRpbmdcIjpcbiAgICAgICAgcmV0dXJuIFwiYmcteWVsbG93LTUwMFwiXG4gICAgICBjYXNlIFwiYXZhaWxhYmxlXCI6XG4gICAgICAgIHJldHVybiBcImJnLW5ldXRyYWwtNTAwXCJcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBcImJnLW5ldXRyYWwtNTAwXCJcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNUZXh0TmV3ID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgXCJpbnN0YWxsZWRcIjpcbiAgICAgICAgcmV0dXJuIFwiSW5zdGFsbGVkXCJcbiAgICAgIGNhc2UgXCJ1cGRhdGluZ1wiOlxuICAgICAgICByZXR1cm4gXCJVcGRhdGluZ1wiXG4gICAgICBjYXNlIFwiYXZhaWxhYmxlXCI6XG4gICAgICAgIHJldHVybiBcIkF2YWlsYWJsZVwiXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gXCJVbmtub3duXCJcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGxcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktNCBib3JkZXItYiBib3JkZXItbmV1dHJhbC04MDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5NQ1AgTGlicmFyeTwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbmV1dHJhbC00MDBcIj5Nb2RlbCBDb250ZXh0IFByb3RvY29sIHNlcnZlcnMgYW5kIGludGVncmF0aW9uczwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cInNlY29uZGFyeVwiIGNsYXNzTmFtZT1cImJnLWdyYXktODAwIHRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgIHtmaWx0ZXJlZFNlcnZlcnMubGVuZ3RofSBvZiB7bWNwU2VydmVycy5sZW5ndGh9XG4gICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFNlYXJjaCBhbmQgRmlsdGVycyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBNQ1Agc2VydmVycy4uLlwiXG4gICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hRdWVyeX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwIGJnLW5ldXRyYWwtOTAwIGJvcmRlci1uZXV0cmFsLTgwMCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyOnRleHQtbmV1dHJhbC01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwLzQwXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgIHtjYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnkpID0+IChcbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIGtleT17Y2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkQ2F0ZWdvcnkoY2F0ZWdvcnkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7XG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZENhdGVnb3J5ID09PSBjYXRlZ29yeVxuICAgICAgICAgICAgICAgICAgICA/IFwiYm9yZGVyLWJsdWUtNTAwLzIwIHRleHQtYmx1ZS00MDAgaG92ZXI6YmctYmx1ZS01MDAvMTAgaG92ZXI6dGV4dC1ibHVlLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6YmctZ3JheS04MDAvNTBcIlxuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2NhdGVnb3J5fVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTUNQIFNlcnZlcnMgR3JpZCAqL31cbiAgICAgIDxTY3JvbGxBcmVhIGNsYXNzTmFtZT1cImZsZXgtMSBwLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAge2ZpbHRlcmVkU2VydmVycy5tYXAoKHNlcnZlcikgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e3NlcnZlci5pZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgYmctbmV1dHJhbC05MDAgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTgwMCByb3VuZGVkLXhsIGNhcmQtaG92ZXItYmx1ZSBwLTUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIFNldHRpbmdzIEdlYXIgSWNvbiAqL31cbiAgICAgICAgICAgICAge3NlcnZlci5zdGF0dXMgPT09IFwiaW5zdGFsbGVkXCIgJiYgKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2VydmVyU2V0dGluZ3Moc2VydmVyLmlkLCBzZXJ2ZXIubmFtZSlcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNCByaWdodC00IHctNyBoLTcgcm91bmRlZC1sZyBiZy1uZXV0cmFsLTkwMC83MCBob3ZlcjpiZy1uZXV0cmFsLTgwMC83MCBib3JkZXIgYm9yZGVyLW5ldXRyYWwtNzAwLzUwIGhvdmVyOmJvcmRlci1uZXV0cmFsLTYwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHotMTBcIlxuICAgICAgICAgICAgICAgICAgdGl0bGU9e2BTZXR0aW5ncyBmb3IgJHtzZXJ2ZXIubmFtZX1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJoLTMuNSB3LTMuNSB0ZXh0LW5ldXRyYWwtNDAwIGhvdmVyOnRleHQtbmV1dHJhbC0zMDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHsvKiBTZXJ2ZXIgSGVhZGVyICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTQgbWItNFwiPlxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctMTIgaC0xMiByb3VuZGVkLXhsIGJnLWdyYWRpZW50LXRvLWJyICR7c2VydmVyLmdyYWRpZW50fSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlIGZsZXgtc2hyaW5rLTBgfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtzZXJ2ZXIuaWNvbn1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTAgcHItOFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtd2hpdGUgdHJ1bmNhdGVcIj57c2VydmVyLm5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0yIGgtMiByb3VuZGVkLWZ1bGwgJHtnZXRTdGF0dXNDb2xvck5ldyhzZXJ2ZXIuc3RhdHVzKX1gfSAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1uZXV0cmFsLTQwMFwiPntnZXRTdGF0dXNUZXh0TmV3KHNlcnZlci5zdGF0dXMpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1uZXV0cmFsLTQwMFwiPntzZXJ2ZXIuY2F0ZWdvcnl9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPnZ7c2VydmVyLnZlcnNpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPmJ5IHtzZXJ2ZXIuYXV0aG9yfTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00IHRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFN0YXIgY2xhc3NOYW1lPVwiaC0zIHctMyBmaWxsLXllbGxvdy01MDAgdGV4dC15ZWxsb3ctNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57c2VydmVyLnJhdGluZ308L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntzZXJ2ZXIuZG93bmxvYWRzLnRvTG9jYWxlU3RyaW5nKCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+VXBkYXRlZCB7c2VydmVyLmxhc3RVcGRhdGVkfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogRGVzY3JpcHRpb24gKi99XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1uZXV0cmFsLTQwMCBtYi00IGxlYWRpbmctcmVsYXhlZFwiPntzZXJ2ZXIuZGVzY3JpcHRpb259PC9wPlxuXG4gICAgICAgICAgICAgIHsvKiBUYWdzICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIG1iLTRcIj5cbiAgICAgICAgICAgICAgICB7c2VydmVyLnRhZ3MubWFwKCh0YWcpID0+IChcbiAgICAgICAgICAgICAgICAgIDxCYWRnZVxuICAgICAgICAgICAgICAgICAgICBrZXk9e3RhZ31cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInNlY29uZGFyeVwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLW5ldXRyYWwtODAwIHRleHQtbmV1dHJhbC00MDAgYm9yZGVyLW5ldXRyYWwtNzAwIHRleHQteHMgcHgtMiBweS0xIGhvdmVyOmJnLWdyYXktNzAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3RhZ31cbiAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBBY3Rpb24gQnV0dG9uICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICB7c2VydmVyLnN0YXR1cyA9PT0gXCJpbnN0YWxsZWRcIiA/IChcbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVW5pbnN0YWxsKHNlcnZlci5pZCwgc2VydmVyLm5hbWUpfVxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBib3JkZXItbmV1dHJhbC03MDAgdGV4dC1uZXV0cmFsLTMwMCBob3ZlcjpiZy1uZXV0cmFsLTgwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIEluc3RhbGxlZFxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgKSA6IHNlcnZlci5zdGF0dXMgPT09IFwidXBkYXRpbmdcIiA/IChcbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gZGlzYWJsZWQgY2xhc3NOYW1lPVwiZmxleC0xIGJnLXllbGxvdy02MDAvMjAgdGV4dC15ZWxsb3ctNDAwIGJvcmRlciBib3JkZXIteWVsbG93LTYwMC8zMFwiPlxuICAgICAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTIgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgVXBkYXRpbmcuLi5cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUluc3RhbGwoc2VydmVyLmlkLCBzZXJ2ZXIubmFtZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBiZy1ibHVlLWdyYWRpZW50LWhvdmVyIHRleHQtd2hpdGUgc2hhZG93LWxnIGJvcmRlci0wIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBncm91cC1ob3ZlcjpzaGFkb3ctbGdgfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgSW5zdGFsbFxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEhvdmVyIEVmZmVjdCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHJvdW5kZWQteGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS13aGl0ZS81IHRvLXRyYW5zcGFyZW50IG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwIHBvaW50ZXItZXZlbnRzLW5vbmVcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHtmaWx0ZXJlZFNlcnZlcnMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIHRleHQtZ3JheS01MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTQwMCBtYi0yXCI+Tm8gTUNQIHNlcnZlcnMgZm91bmQ8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5UcnkgYWRqdXN0aW5nIHlvdXIgc2VhcmNoIG9yIGZpbHRlciBjcml0ZXJpYTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9TY3JvbGxBcmVhPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJCdXR0b24iLCJTY3JvbGxBcmVhIiwiQmFkZ2UiLCJJbnB1dCIsIkdsb2JlIiwiRmlsZVRleHQiLCJDb2RlIiwiU2VhcmNoIiwiRG93bmxvYWQiLCJDaGVja0NpcmNsZSIsIlN0YXIiLCJTZXR0aW5ncyIsIlBhY2thZ2UiLCJCcmFpbiIsIlphcCIsImdldEluaXRpYWxNY3BTZXJ2ZXJzIiwiaWQiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJhdXRob3IiLCJ2ZXJzaW9uIiwiY2F0ZWdvcnkiLCJpY29uIiwiY2xhc3NOYW1lIiwicmF0aW5nIiwiZG93bmxvYWRzIiwidGFncyIsInN0YXR1cyIsImdyYWRpZW50IiwibGFzdFVwZGF0ZWQiLCJjYXRlZ29yaWVzIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRTdGF0dXNUZXh0IiwiTWNwTGlicmFyeVRhYiIsInNlYXJjaFF1ZXJ5Iiwic2V0U2VhcmNoUXVlcnkiLCJzZWxlY3RlZENhdGVnb3J5Iiwic2V0U2VsZWN0ZWRDYXRlZ29yeSIsIm1jcFNlcnZlcnMiLCJzZXRNY3BTZXJ2ZXJzIiwibWNwSW5mbyIsInNldE1jcEluZm8iLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFkZFNlcnZlckRpYWxvZyIsInNldEFkZFNlcnZlckRpYWxvZyIsIm5ld1NlcnZlciIsInNldE5ld1NlcnZlciIsImNvbW1hbmQiLCJhcmdzIiwiZmlsdGVyZWRTZXJ2ZXJzIiwiZmlsdGVyIiwic2VydmVyIiwibWF0Y2hlc1NlYXJjaCIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJzb21lIiwidGFnIiwibWF0Y2hlc0NhdGVnb3J5IiwiaGFuZGxlSW5zdGFsbCIsInNlcnZlcklkIiwic2VydmVyTmFtZSIsImNvbnNvbGUiLCJsb2ciLCJoYW5kbGVVbmluc3RhbGwiLCJoYW5kbGVTZXJ2ZXJTZXR0aW5ncyIsImdldFN0YXR1c0NvbG9yTmV3IiwiZ2V0U3RhdHVzVGV4dE5ldyIsImRpdiIsImgxIiwicCIsInZhcmlhbnQiLCJsZW5ndGgiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwibWFwIiwic2l6ZSIsIm9uQ2xpY2siLCJidXR0b24iLCJzdG9wUHJvcGFnYXRpb24iLCJ0aXRsZSIsImgzIiwic3BhbiIsInRvTG9jYWxlU3RyaW5nIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./mcp-library-tab.tsx\n"));

/***/ })

});