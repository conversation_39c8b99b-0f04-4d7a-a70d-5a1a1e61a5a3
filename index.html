<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connected Page Example</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Meta tags for proper mobile behavior -->
    <meta name="description" content="Example page with properly connected JavaScript">
</head>
<body>
    <header>
        <h1>My Connected Web Page</h1>
        <p>This page has properly connected JavaScript files</p>
    </header>

    <main>
        <section>
            <h2>Interactive Elements</h2>
            <button id="myButton">Click Me</button>
            <p id="output">Click the button above to see JavaScript in action!</p>
        </section>

        <section>
            <h2>Form Example</h2>
            <form id="myForm">
                <label for="userInput">Enter your name:</label>
                <input type="text" id="userInput" placeholder="Your name here" required>
                <button type="submit">Submit</button>
            </form>
            <div id="greeting"></div>
        </section>
    </main>

    <!-- External JavaScript file - placed before closing </body> tag for faster page load -->
    <script src="script.js"></script>
    
    <!-- Alternative placement in <head> with defer attribute (modern approach) -->
    <!-- <script src="script.js" defer></script> -->
</body>
</html>