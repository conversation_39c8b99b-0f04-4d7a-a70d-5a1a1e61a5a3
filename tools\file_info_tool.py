from .base_tool import BaseTool
import os
import stat
import mimetypes
from datetime import datetime
import hashlib

class FileInfoTool(BaseTool):
    def __init__(self, config: dict):
        self.config = config

    @property
    def name(self) -> str:
        return "file_info"

    @property
    def description(self) -> str:
        return "Get file/directory info: size, permissions, timestamps, type."

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "The file or directory path to get information about"
                },
                "include_hash": {
                    "type": "boolean",
                    "description": "Whether to calculate file hash (MD5) for files (default: False)",
                    "default": False
                }
            },
            "required": ["path"]
        }

    def execute(self, path: str, include_hash: bool = False) -> dict:
        try:
            # Get absolute path
            abs_path = os.path.abspath(path)

            # Check if path exists
            if not os.path.exists(abs_path):
                return {"error": f"Path does not exist: {path}"}

            # Get basic file stats
            stat_info = os.stat(abs_path)
            is_file = os.path.isfile(abs_path)
            is_dir = os.path.isdir(abs_path)
            is_link = os.path.islink(abs_path)

            def format_size(size_bytes):
                """Format file size in human readable format"""
                for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
                    if size_bytes < 1024.0:
                        return f"{size_bytes:.1f} {unit}"
                    size_bytes /= 1024.0
                return f"{size_bytes:.1f} PB"

            def format_permissions(mode):
                """Format permissions in human readable format"""
                return stat.filemode(mode)

            def get_file_type(file_path):
                """Get file type information"""
                if is_dir:
                    return "directory"
                elif is_link:
                    return "symbolic link"
                else:
                    # Try to determine MIME type
                    mime_type, encoding = mimetypes.guess_type(file_path)
                    if mime_type:
                        return f"file ({mime_type})"
                    else:
                        # Fallback to extension
                        _, ext = os.path.splitext(file_path)
                        if ext:
                            return f"file ({ext.lower()} file)"
                        else:
                            return "file (unknown type)"

            # Build basic info
            info = {
                "path": abs_path,
                "name": os.path.basename(abs_path),
                "type": get_file_type(abs_path),
                "is_file": is_file,
                "is_directory": is_dir,
                "is_symlink": is_link,
                "size_bytes": stat_info.st_size,
                "size_formatted": format_size(stat_info.st_size),
                "permissions": format_permissions(stat_info.st_mode),
                "permissions_octal": oct(stat_info.st_mode)[-3:],
                "owner_uid": stat_info.st_uid,
                "group_gid": stat_info.st_gid,
                "created": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                "accessed": datetime.fromtimestamp(stat_info.st_atime).isoformat(),
                "success": True
            }

            # Add file-specific information
            if is_file:
                # File extension
                _, ext = os.path.splitext(abs_path)
                info["extension"] = ext.lower() if ext else None

                # MIME type
                mime_type, encoding = mimetypes.guess_type(abs_path)
                info["mime_type"] = mime_type
                info["encoding"] = encoding

                # Check if file is executable
                info["is_executable"] = os.access(abs_path, os.X_OK)

                # Calculate hash if requested
                if include_hash and stat_info.st_size < 100 * 1024 * 1024:  # Only for files < 100MB
                    try:
                        with open(abs_path, 'rb') as f:
                            file_hash = hashlib.md5()
                            for chunk in iter(lambda: f.read(4096), b""):
                                file_hash.update(chunk)
                            info["md5_hash"] = file_hash.hexdigest()
                    except Exception as e:
                        info["hash_error"] = f"Could not calculate hash: {str(e)}"
                elif include_hash:
                    info["hash_error"] = "File too large for hash calculation (>100MB)"

            # Add directory-specific information
            elif is_dir:
                try:
                    # Count contents
                    contents = os.listdir(abs_path)
                    files = sum(1 for item in contents if os.path.isfile(os.path.join(abs_path, item)))
                    dirs = sum(1 for item in contents if os.path.isdir(os.path.join(abs_path, item)))

                    info["contents"] = {
                        "total_items": len(contents),
                        "files": files,
                        "directories": dirs
                    }
                except PermissionError:
                    info["contents"] = {"error": "Permission denied"}

            # Add symlink-specific information
            if is_link:
                try:
                    target = os.readlink(abs_path)
                    info["symlink_target"] = target
                    info["symlink_target_exists"] = os.path.exists(target)
                except OSError as e:
                    info["symlink_error"] = str(e)

            # Check accessibility
            info["readable"] = os.access(abs_path, os.R_OK)
            info["writable"] = os.access(abs_path, os.W_OK)
            info["executable"] = os.access(abs_path, os.X_OK)

            return info

        except PermissionError:
            return {"error": f"Permission denied accessing: {path}"}
        except OSError as e:
            return {"error": f"OS error getting file info: {str(e)}"}
        except Exception as e:
            return {"error": f"Failed to get file info: {str(e)}"}