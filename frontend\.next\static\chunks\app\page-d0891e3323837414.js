(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4298:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(5155),a=t(2115),l=t(2550),n=t(3878),i=t(7235),o=t(1073),c=t(2215),d=t(502),u=t(403),m=t(8681);let h=t(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class x{async createQuery(e){let s=await fetch("".concat(this.baseUrl,"/query"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error("HTTP error! status: ".concat(s.status));return s.json()}async getTaskStatus(e){let s=await fetch("".concat(this.baseUrl,"/task/").concat(e));if(!s.ok)throw Error("HTTP error! status: ".concat(s.status));return s.json()}async listTasks(){let e=await fetch("".concat(this.baseUrl,"/tasks"));if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));return e.json()}async deleteTask(e){let s=await fetch("".concat(this.baseUrl,"/task/").concat(e),{method:"DELETE"});if(!s.ok)throw Error("HTTP error! status: ".concat(s.status))}streamTaskProgress(e,s,t){let r=new EventSource("".concat(this.baseUrl,"/task/").concat(e,"/stream"));return r.onmessage=e=>{try{let t=JSON.parse(e.data);s(t)}catch(e){console.error("Error parsing progress update:",e),null==t||t(Error("Failed to parse progress update"))}},r.onerror=e=>{console.error("EventSource error:",e),null==t||t(Error("Connection error"))},()=>{r.close()}}async healthCheck(){let e=await fetch("".concat(this.baseUrl,"/health"));if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));return e.json()}constructor(e=h){this.baseUrl=e}}let g=new x;var b=t(2596),p=t(9688);function j(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,p.QP)((0,b.$)(s))}function N(){var e,s;let[t,h]=(0,a.useState)(""),[x,b]=(0,a.useState)("single"),[p,N]=(0,a.useState)(null),[f,y]=(0,a.useState)(!1),[v,w]=(0,a.useState)(null),[k,A]=(0,a.useState)([]);(0,a.useEffect)(()=>{T()},[]);let T=async()=>{try{let e=await g.listTasks();A(e.slice(0,5))}catch(e){console.error("Failed to load recent tasks:",e)}},E=async e=>{if(e.preventDefault(),t.trim()&&!f){y(!0),N(null),w(null);try{let e=await g.createQuery({query:t.trim(),mode:x});N(e),(async()=>{let s=0;for(;s<60;)try{let t=await g.getTaskStatus(e.task_id);if(N(t),"completed"===t.status||"failed"===t.status){y(!1),T();break}w({task_id:e.task_id,status:t.status,progress:t.progress}),await new Promise(e=>setTimeout(e,2e3)),s++}catch(e){console.error("Error polling task status:",e),y(!1);break}s>=60&&(y(!1),console.warn("Task polling timed out"))})()}catch(e){console.error("Failed to create query:",e),y(!1)}}},S=e=>{switch(e){case"completed":return(0,r.jsx)(l.A,{className:"w-4 h-4 text-green-400"});case"failed":return(0,r.jsx)(n.A,{className:"w-4 h-4 text-red-400"});case"processing":return(0,r.jsx)(i.A,{className:"w-4 h-4 text-blue-400 animate-spin"});default:return(0,r.jsx)(o.A,{className:"w-4 h-4 text-neutral-400"})}};return(0,r.jsxs)("div",{className:"min-h-screen bg-black text-white",children:[(0,r.jsx)("header",{className:"border-b border-neutral-800 bg-neutral-950",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"bg-blue-gradient p-2 rounded-lg",children:(0,r.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-white",children:"Make It Heavy"}),(0,r.jsx)("p",{className:"text-sm text-neutral-400",children:"Multi-Agent AI Analysis System"})]})]})})}),(0,r.jsxs)("main",{className:"max-w-6xl mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"bg-neutral-900 border border-neutral-800 rounded-lg p-6 mb-8",children:(0,r.jsxs)("form",{onSubmit:E,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"query",className:"block text-white font-medium mb-2",children:"What would you like to analyze?"}),(0,r.jsx)("textarea",{id:"query",value:t,onChange:e=>h(e.target.value),placeholder:"Enter your query here... (e.g., 'Analyze the impact of AI on software development')",className:"w-full h-32 bg-neutral-800 border border-neutral-700 rounded-lg px-4 py-3 text-white placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",disabled:f})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>b("single"),className:j("flex items-center gap-2 px-4 py-2 rounded-lg border transition-all","single"===x?"bg-blue-500/10 text-blue-400 border-blue-500/20":"bg-neutral-800 text-neutral-400 border-neutral-700 hover:bg-neutral-700"),disabled:f,children:[(0,r.jsx)(d.A,{className:"w-4 h-4"}),"Single Agent"]}),(0,r.jsxs)("button",{type:"button",onClick:()=>b("heavy"),className:j("flex items-center gap-2 px-4 py-2 rounded-lg border transition-all","heavy"===x?"bg-blue-500/10 text-blue-400 border-blue-500/20":"bg-neutral-800 text-neutral-400 border-neutral-700 hover:bg-neutral-700"),disabled:f,children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),"Heavy Mode (Multi-Agent)"]})]}),(0,r.jsxs)("button",{type:"submit",disabled:!t.trim()||f,className:"bg-blue-gradient-hover text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",children:[f?(0,r.jsx)(i.A,{className:"w-4 h-4 animate-spin"}):(0,r.jsx)(m.A,{className:"w-4 h-4"}),f?"Processing...":"Analyze"]})]})}),(p||v)&&(0,r.jsxs)("div",{className:"bg-neutral-900 border border-neutral-800 rounded-lg p-6 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[S((null==v?void 0:v.status)||(null==p?void 0:p.status)||"pending"),(0,r.jsx)("h3",{className:"text-lg font-medium text-white",children:"heavy"===x?"Heavy Mode Analysis":"Single Agent Analysis"})]}),(null==v||null==(e=v.progress)?void 0:e.message)&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-neutral-300",children:v.progress.message}),v.progress.stage&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"flex justify-between text-sm text-neutral-400 mb-1",children:(0,r.jsxs)("span",{children:["Stage: ",v.progress.stage]})}),(0,r.jsx)("div",{className:"w-full bg-neutral-800 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-gradient h-2 rounded-full transition-all duration-300",style:{width:"completed"===v.progress.stage?"100%":"synthesizing"===v.progress.stage?"80%":"executing"===v.progress.stage?"60%":"decomposing"===v.progress.stage?"40%":"processing"===v.progress.stage?"50%":"initializing"===v.progress.stage?"20%":"10%"}})})]})]}),(null==v||null==(s=v.progress)?void 0:s.questions)&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-white font-medium mb-2",children:"Generated Questions:"}),(0,r.jsx)("ul",{className:"space-y-2",children:v.progress.questions.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-start gap-2",children:[(0,r.jsxs)("span",{className:"text-blue-400 font-medium",children:[s+1,"."]}),(0,r.jsx)("span",{className:"text-neutral-300",children:e})]},s))})]}),(null==p?void 0:p.result)&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h4",{className:"text-white font-medium mb-2",children:"Result:"}),(0,r.jsx)("div",{className:"bg-neutral-800 border border-neutral-700 rounded-lg p-4",children:(0,r.jsx)("pre",{className:"text-neutral-300 whitespace-pre-wrap font-mono text-sm",children:p.result})})]}),(null==p?void 0:p.error)&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h4",{className:"text-red-400 font-medium mb-2",children:"Error:"}),(0,r.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-red-300",children:p.error})})]})]}),k.length>0&&(0,r.jsxs)("div",{className:"bg-neutral-900 border border-neutral-800 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Recent Tasks"}),(0,r.jsx)("div",{className:"space-y-3",children:k.map(e=>(0,r.jsx)("div",{className:"bg-neutral-800 border border-neutral-700 rounded-lg p-4 card-hover-blue",children:(0,r.jsx)("div",{className:"flex items-start justify-between gap-4",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[S(e.status),(0,r.jsx)("span",{className:"text-sm text-neutral-400",children:"heavy"===e.mode?"Heavy Mode":"Single Agent"}),(0,r.jsx)("span",{className:"text-sm text-neutral-500",children:new Date(e.created_at).toLocaleString()})]}),(0,r.jsx)("p",{className:"text-white text-sm mb-2 line-clamp-2",children:e.query}),e.result&&(0,r.jsxs)("p",{className:"text-neutral-400 text-xs line-clamp-1",children:[e.result.substring(0,100),"..."]})]})})},e.task_id))})]})]})]})}},6405:(e,s,t)=>{Promise.resolve().then(t.bind(t,4298))}},e=>{e.O(0,[744,441,964,358],()=>e(e.s=6405)),_N_E=e.O()}]);