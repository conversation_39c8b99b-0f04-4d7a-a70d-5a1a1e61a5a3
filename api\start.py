#!/usr/bin/env python3
"""
Startup script for the Make It Heavy API server
"""
import sys
import os
import subprocess

def main():
    # Check if we're in the right directory
    if not os.path.exists('../config.yaml'):
        print("Error: config.yaml not found. Please run this from the api directory.")
        sys.exit(1)

    # Install dependencies if needed
    try:
        import fastapi
        import uvicorn
    except ImportError:
        print("Installing API dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)

    # Start the server
    print("Starting Make It Heavy API server...")
    print("API will be available at: http://localhost:8000")
    print("API documentation at: http://localhost:8000/docs")

    # Import and run the app
    from main import app
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)

if __name__ == "__main__":
    main()