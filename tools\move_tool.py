from .base_tool import BaseTool
import os
import shutil

class MoveTool(BaseTool):
    def __init__(self, config: dict):
        self.config = config

    @property
    def name(self) -> str:
        return "move"

    @property
    def description(self) -> str:
        return "Move or rename a file or directory from source path to destination path. Can move files between directories or rename them."

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "source": {
                    "type": "string",
                    "description": "The source file or directory path to move"
                },
                "destination": {
                    "type": "string",
                    "description": "The destination path where the file/directory should be moved"
                },
                "overwrite": {
                    "type": "boolean",
                    "description": "Whether to overwrite destination if it exists (default: False)",
                    "default": False
                }
            },
            "required": ["source", "destination"]
        }

    def execute(self, source: str, destination: str, overwrite: bool = False) -> dict:
        try:
            # Get absolute paths
            abs_source = os.path.abspath(source)
            abs_destination = os.path.abspath(destination)

            # Check if source exists
            if not os.path.exists(abs_source):
                return {"error": f"Source path does not exist: {source}"}

            # Check if destination already exists
            if os.path.exists(abs_destination):
                if not overwrite:
                    return {"error": f"Destination already exists: {destination} (use overwrite=True to replace)"}
                else:
                    # Remove destination if overwriting
                    if os.path.isdir(abs_destination):
                        shutil.rmtree(abs_destination)
                    else:
                        os.remove(abs_destination)

            # Create parent directory of destination if it doesn't exist
            dest_parent = os.path.dirname(abs_destination)
            if dest_parent and not os.path.exists(dest_parent):
                os.makedirs(dest_parent, exist_ok=True)

            # Determine operation type
            source_is_file = os.path.isfile(abs_source)
            source_is_dir = os.path.isdir(abs_source)

            # Check if it's a rename (same parent directory) or move
            source_parent = os.path.dirname(abs_source)
            dest_parent = os.path.dirname(abs_destination)
            is_rename = source_parent == dest_parent

            # Perform the move/rename
            shutil.move(abs_source, abs_destination)

            operation = "renamed" if is_rename else "moved"
            item_type = "file" if source_is_file else "directory"

            return {
                "source": abs_source,
                "destination": abs_destination,
                "success": True,
                "message": f"Successfully {operation} {item_type} from {source} to {destination}",
                "operation": operation,
                "type": item_type,
                "overwrite_used": overwrite and os.path.exists(abs_destination)
            }

        except PermissionError:
            return {"error": f"Permission denied moving from {source} to {destination}"}
        except OSError as e:
            return {"error": f"OS error moving file: {str(e)}"}
        except Exception as e:
            return {"error": f"Failed to move: {str(e)}"}