import React, { useState } from 'react';
import { useSettings } from '../lib/store';
import { X } from 'lucide-react';

interface SettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const models = [
  { id: 'anthropic/claude-3-sonnet', name: 'Claude 3 Sonnet' },
  { id: 'anthropic/claude-3-opus', name: 'Claude 3 Opus' },
  { id: 'anthropic/claude-2.1', name: '<PERSON> 2.1' },
  { id: 'meta-llama/llama-2-70b-chat', name: 'Llama 2 70B' },
];

export function SettingsDialog({ isOpen, onClose }: SettingsDialogProps) {
  const { apiKey, model, showUserAgent, setApiKey, setModel, setShowUserAgent } = useSettings();
  const [localApiKey, setLocalApiKey] = useState(apiKey);
  const [localModel, setLocalModel] = useState(model);
  const [localShowUserAgent, setLocalShowUserAgent] = useState(showUserAgent);

  if (!isOpen) return null;

  const handleSave = () => {
    setApiKey(localApiKey);
    setModel(localModel);
    setShowUserAgent(localShowUserAgent);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-[#141414] rounded-2xl border border-white/10 w-full max-w-md p-6 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">Settings</h2>
          <button onClick={onClose} className="p-1 hover:bg-white/5 rounded-lg transition-colors">
            <X size={20} />
          </button>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              OpenRouter API Key
            </label>
            <input
              type="password"
              value={localApiKey}
              onChange={(e) => setLocalApiKey(e.target.value)}
              className="w-full bg-[#1A1A1A] rounded-lg px-3 py-2 text-sm border border-white/10 focus:outline-none focus:ring-2 focus:ring-white/20"
              placeholder="sk-or-..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Default Model
            </label>
            <select
              value={localModel}
              onChange={(e) => setLocalModel(e.target.value)}
              className="w-full bg-[#1A1A1A] rounded-lg px-3 py-2 text-sm border border-white/10 focus:outline-none focus:ring-2 focus:ring-white/20"
            >
              {models.map((m) => (
                <option key={m.id} value={m.id}>
                  {m.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-300">
              Show User Agent in Chat
            </label>
            <button
              onClick={() => setLocalShowUserAgent(!localShowUserAgent)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                localShowUserAgent ? 'bg-white/20' : 'bg-white/5'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  localShowUserAgent ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>

        <div className="flex justify-end mt-8 space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm rounded-lg hover:bg-white/5 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 text-sm bg-white/10 rounded-lg hover:bg-white/20 transition-colors"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
}