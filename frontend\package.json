{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.263.1", "next": "15.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "19.1.8", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}