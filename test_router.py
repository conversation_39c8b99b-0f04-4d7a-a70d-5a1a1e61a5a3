#!/usr/bin/env python3

from tool_router import tool_router

def test_tool_routing():
    queries = [
        "create a TypeScript file for calendar functionality",
        "search the web for JavaScript libraries",
        "calculate 25 * 47",
        "list the contents of the current directory",
        "delete old backup files",
        "run npm start to open the app",
        "execute the build command",
        "start the development server"
    ]
    
    for query in queries:
        print(f"\n🔍 Query: '{query}'")
        tools = tool_router.route_tools(query)
        print(f"📦 Selected {len(tools)} tools:")
        for tool in tools:
            name = tool["function"]["name"]
            desc = tool["function"]["description"]
            print(f"   - {name}: {desc}")

if __name__ == "__main__":
    test_tool_routing()
