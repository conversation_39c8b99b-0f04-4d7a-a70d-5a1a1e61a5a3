#!/usr/bin/env python3
"""
Test script to verify the frontend-backend integration
"""

import requests
import json
import time

def test_api_health():
    """Test if the API is running"""
    try:
        response = requests.get('http://localhost:8000/health', timeout=5)
        print(f"✅ API Health Check: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ API Health Check Failed: {e}")
        return False

def test_query_endpoint():
    """Test the query endpoint"""
    try:
        payload = {
            "query": "List the files in the current directory",
            "mode": "single"
        }
        
        response = requests.post(
            'http://localhost:8000/query',
            json=payload,
            timeout=30
        )
        
        print(f"✅ Query Endpoint: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📋 Task ID: {data.get('task_id')}")
            print(f"📊 Status: {data.get('status')}")
            
            # If task is pending/running, poll for completion
            if data.get('status') in ['pending', 'running']:
                task_id = data.get('task_id')
                print(f"⏳ Polling task {task_id}...")
                
                for i in range(10):  # Poll up to 10 times
                    time.sleep(2)
                    task_response = requests.get(f'http://localhost:8000/task/{task_id}')
                    if task_response.status_code == 200:
                        task_data = task_response.json()
                        print(f"📊 Poll {i+1}: {task_data.get('status')}")
                        
                        if task_data.get('status') == 'completed':
                            print(f"✅ Task completed!")
                            print(f"📝 Result: {task_data.get('result', '')[:200]}...")
                            break
                        elif task_data.get('status') == 'failed':
                            print(f"❌ Task failed: {task_data.get('error')}")
                            break
            else:
                print(f"📝 Result: {data.get('result', '')[:200]}...")
                
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Query Endpoint Failed: {e}")
        return False

def test_frontend_connection():
    """Test if frontend is running"""
    try:
        response = requests.get('http://localhost:5173', timeout=5)  # Vite default port
        print(f"✅ Frontend Check (Vite): {response.status_code}")
        return True
    except:
        try:
            response = requests.get('http://localhost:3000', timeout=5)  # Alternative port
            print(f"✅ Frontend Check (Alt): {response.status_code}")
            return True
        except Exception as e:
            print(f"❌ Frontend Check Failed: {e}")
            return False

def main():
    print("🧪 Testing AG3NT X Integration")
    print("=" * 40)
    
    # Test API
    api_ok = test_api_health()
    
    if api_ok:
        query_ok = test_query_endpoint()
    else:
        print("⚠️  Skipping query test - API not available")
        query_ok = False
    
    # Test Frontend
    frontend_ok = test_frontend_connection()
    
    print("\n" + "=" * 40)
    print("📊 Integration Test Results:")
    print(f"   API Health: {'✅' if api_ok else '❌'}")
    print(f"   Query Endpoint: {'✅' if query_ok else '❌'}")
    print(f"   Frontend: {'✅' if frontend_ok else '❌'}")
    
    if api_ok and query_ok and frontend_ok:
        print("\n🎉 All systems operational!")
        print("🌐 Frontend: http://localhost:5173 or http://localhost:3000")
        print("🔧 API: http://localhost:8000")
    else:
        print("\n⚠️  Some systems need attention")

if __name__ == "__main__":
    main()
