<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemeCoin Updates Calendar</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>📊 MemeCoin Updates Calendar</h1>
            <div class="current-year">2024</div>
        </header>
        
        <div class="month-selector">
            <button class="nav-btn" onclick="previousMonth()">‹</button>
            <h2 id="currentMonth">December</h2>
            <button class="nav-btn" onclick="nextMonth()">›</button>
        </div>

        <div class="calendar">
            <div class="weekday-header">
                <div>SUN</div>
                <div>MON</div>
                <div>TUE</div>
                <div>WED</div>
                <div>THU</div>
                <div>FRI</div>
                <div>SAT</div>
            </div>
            
            <div class="calendar-grid" id="calendarGrid">
                <!-- Calendar days will be populated by JavaScript -->
            </div>
        </div>

        <div class="legend">
            <h3>Market Events Legend</h3>
            <div class="legend-items">
                <div class="legend-item">
                    <span class="dot major-event"></span>
                    Major Launch
                </div>
                <div class="legend-item">
                    <span class="dot pump-event"></span>
                    Pump Expected
                </div>
                <div class="legend-item">
                    <span class="dot dip-event"></span>
                    Dip Alert
                </div>
                <div class="legend-item">
                    <span class="dot listing"></span>
                    New Listing
                </div>
            </div>
        </div>

        <div class="event-details" id="eventDetails">
            <h3>Selected Date Events</h3>
            <p id="selectedDate">Click on a date to see events</p>
            <div id="eventList" class="event-list">
                No events scheduled
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>