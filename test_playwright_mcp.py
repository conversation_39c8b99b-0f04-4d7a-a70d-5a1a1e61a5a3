#!/usr/bin/env python3
"""
Test Playwright MCP server integration
"""

import asyncio
import os
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_playwright_mcp():
    """Test Playwright MCP server"""
    print("🚀 Testing Playwright MCP server...")
    
    # Create server parameters for Playwright
    server_params = StdioServerParameters(
        command="npx",
        args=["-y", "@playwright/mcp@latest"],
        env=os.environ.copy()
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # Initialize the connection
                print("📡 Initializing Playwright MCP connection...")
                await session.initialize()
                print("✅ Playwright MCP connection established!")
                
                # Discover tools
                print("\n🔧 Discovering Playwright tools...")
                tools = await session.list_tools()
                print(f"Found {len(tools.tools)} Playwright tools:")
                for tool in tools.tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # Discover resources
                print("\n📚 Discovering Playwright resources...")
                resources = await session.list_resources()
                print(f"Found {len(resources.resources)} Playwright resources:")
                for resource in resources.resources:
                    print(f"  - {resource.uri}: {resource.name}")
                
                # Test navigation
                print("\n🌐 Testing web navigation...")
                try:
                    result = await session.call_tool("navigate", arguments={"url": "https://httpbin.org/html"})
                    result_content = result.content[0]
                    if hasattr(result_content, 'text'):
                        print(f"✅ Navigation successful: {result_content.text[:100]}...")
                except Exception as e:
                    print(f"❌ Navigation failed: {e}")
                
                # Test getting page content
                print("\n📄 Testing page content extraction...")
                try:
                    result = await session.call_tool("get_page_content", arguments={})
                    result_content = result.content[0]
                    if hasattr(result_content, 'text'):
                        print(f"✅ Page content extracted: {result_content.text[:200]}...")
                except Exception as e:
                    print(f"❌ Content extraction failed: {e}")
                
                # Test screenshot
                print("\n📸 Testing screenshot capture...")
                try:
                    result = await session.call_tool("screenshot", arguments={})
                    result_content = result.content[0]
                    if hasattr(result_content, 'text'):
                        print(f"✅ Screenshot captured: {result_content.text[:100]}...")
                except Exception as e:
                    print(f"❌ Screenshot failed: {e}")
                
                print("\n🎉 Playwright MCP test completed!")
                
    except Exception as e:
        print(f"❌ Playwright MCP test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_combined_workflow():
    """Test a combined workflow using both servers"""
    print("\n🔄 Testing combined workflow...")
    
    # Test our Make It Heavy server first
    print("\n1️⃣ Testing Make It Heavy server...")
    server_params_heavy = StdioServerParameters(
        command="python",
        args=["mcp_server.py"],
        env=os.environ.copy()
    )
    
    try:
        async with stdio_client(server_params_heavy) as (read_heavy, write_heavy):
            async with ClientSession(read_heavy, write_heavy) as session_heavy:
                await session_heavy.initialize()
                print("✅ Make It Heavy server connected")
                
                # Test file operations
                result = await session_heavy.call_tool("calculate", arguments={"expression": "42 * 2"})
                result_content = result.content[0]
                if hasattr(result_content, 'text'):
                    print(f"✅ Calculation: {result_content.text}")
                
    except Exception as e:
        print(f"❌ Make It Heavy test failed: {e}")
    
    # Test Playwright server
    print("\n2️⃣ Testing Playwright server...")
    server_params_pw = StdioServerParameters(
        command="npx",
        args=["-y", "@playwright/mcp@latest"],
        env=os.environ.copy()
    )
    
    try:
        async with stdio_client(server_params_pw) as (read_pw, write_pw):
            async with ClientSession(read_pw, write_pw) as session_pw:
                await session_pw.initialize()
                print("✅ Playwright server connected")
                
                # Test web navigation
                result = await session_pw.call_tool("navigate", arguments={"url": "https://httpbin.org/json"})
                result_content = result.content[0]
                if hasattr(result_content, 'text'):
                    print(f"✅ Web navigation: {result_content.text[:100]}...")
                
    except Exception as e:
        print(f"❌ Playwright test failed: {e}")
    
    print("\n🎉 Combined workflow test completed!")

if __name__ == "__main__":
    # Test Playwright MCP server
    asyncio.run(test_playwright_mcp())
    
    # Test combined workflow
    asyncio.run(test_combined_workflow())
