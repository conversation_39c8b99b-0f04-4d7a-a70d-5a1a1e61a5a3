"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./chat-interface.tsx":
/*!****************************!*\
  !*** ./chat-interface.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { mode = 'heavy', selectedAgent } = param;\n    _s();\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatMode, setIsChatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentTaskId, setCurrentTaskId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [apiStatus, setApiStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('checking');\n    const [currentMode, setCurrentMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mode);\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (scrollAreaRef.current) {\n            const scrollContainer = scrollAreaRef.current.querySelector(\"[data-radix-scroll-area-viewport]\");\n            if (scrollContainer) {\n                scrollContainer.scrollTop = scrollContainer.scrollHeight;\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    // Check API health on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkAPIHealth = {\n                \"ChatInterface.useEffect.checkAPIHealth\": async ()=>{\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.getHealth();\n                        setApiStatus('connected');\n                    } catch (error) {\n                        console.error('API health check failed:', error);\n                        setApiStatus('disconnected');\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkAPIHealth\"];\n            checkAPIHealth();\n            // Check health every 30 seconds\n            const interval = setInterval(checkAPIHealth, 30000);\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(interval)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Auto-resize textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (textareaRef.current) {\n                textareaRef.current.style.height = \"auto\";\n                textareaRef.current.style.height = \"\".concat(Math.min(textareaRef.current.scrollHeight, 120), \"px\");\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        input\n    ]);\n    const handleFileUpload = (event)=>{\n        const files = event.target.files;\n        if (files) {\n            const newFiles = Array.from(files).map((file)=>({\n                    file,\n                    id: Math.random().toString(36).substr(2, 9)\n                }));\n            setUploadedFiles((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n        }\n        // Reset the input so the same file can be selected again\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const removeFile = (fileId)=>{\n        setUploadedFiles((prev)=>prev.filter((f)=>f.id !== fileId));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const submitToAgent = async (userMessage, files)=>{\n        setIsTyping(true);\n        try {\n            // Create initial agent message placeholder\n            const agentMessageId = Date.now().toString() + \"-agent\";\n            const agentMessage = {\n                id: agentMessageId,\n                role: \"agent\",\n                content: \"Processing your request...\",\n                timestamp: new Date(),\n                status: \"processing\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    agentMessage\n                ]);\n            // Submit query to API\n            const task = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.submitQueryWithStream({\n                query: userMessage,\n                mode: currentMode\n            }, (update)=>{\n                // Update the agent message with progress\n                setMessages((prev)=>prev.map((msg)=>msg.id === agentMessageId ? {\n                            ...msg,\n                            content: update.partial_result || msg.content,\n                            status: update.status,\n                            progress: update.progress\n                        } : msg));\n            }, (error)=>{\n                // Handle error\n                setMessages((prev)=>prev.map((msg)=>msg.id === agentMessageId ? {\n                            ...msg,\n                            content: \"Error: \".concat(error.message),\n                            status: \"failed\",\n                            error: error.message\n                        } : msg));\n                setIsTyping(false);\n            }, ()=>{\n                // Handle completion\n                setIsTyping(false);\n                setCurrentTaskId(null);\n            });\n            setCurrentTaskId(task.task_id);\n        } catch (error) {\n            console.error('Failed to submit query:', error);\n            // Update with error message\n            setMessages((prev)=>prev.map((msg)=>msg.role === \"agent\" && msg.content === \"Processing your request...\" ? {\n                        ...msg,\n                        content: \"Sorry, I encountered an error: \".concat(error instanceof Error ? error.message : 'Unknown error'),\n                        status: \"failed\",\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    } : msg));\n            setIsTyping(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!input.trim() && uploadedFiles.length === 0) return;\n        // Check API connection\n        if (apiStatus === 'disconnected') {\n            // Try to reconnect\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.getHealth();\n                setApiStatus('connected');\n            } catch (error) {\n                console.error('API still disconnected:', error);\n                return;\n            }\n        }\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: input.trim(),\n            timestamp: new Date(),\n            files: uploadedFiles.map((f)=>f.file)\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const messageContent = input.trim();\n        const messageFiles = uploadedFiles.map((f)=>f.file);\n        setInput(\"\");\n        setUploadedFiles([]);\n        if (!isChatMode) {\n            setIsChatMode(true);\n        }\n        // Submit to real agent API\n        await submitToAgent(messageContent, messageFiles);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const ChatBubble = (param)=>{\n        let { message } = param;\n        const isUser = message.role === \"user\";\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3 mb-6 \".concat(isUser ? \"justify-end\" : \"justify-start\"),\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: message.status === \"processing\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4 text-white animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 15\n                    }, this) : message.status === \"failed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"AI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[70%] \".concat(isUser ? \"order-first\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-neutral-500\",\n                                    children: isUser ? \"You\" : \"AG3NT X\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-neutral-500\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                !isUser && message.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: message.status === \"completed\" ? \"default\" : message.status === \"failed\" ? \"destructive\" : \"secondary\",\n                                    className: \"text-xs\",\n                                    children: message.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            px-4 py-3 rounded-2xl text-sm leading-relaxed whitespace-pre-wrap\\n            \".concat(isUser ? \"bg-blue-500 text-white ml-auto rounded-br-md\" : \"bg-neutral-800 text-neutral-100 rounded-bl-md\", \"\\n          \"),\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 space-y-2\",\n                            children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\\n                  flex items-center gap-2 px-3 py-2 rounded-lg text-xs\\n                  \".concat(isUser ? \"bg-blue-700/50 text-blue-100\" : \"bg-neutral-700/50 text-neutral-300\", \"\\n                \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: file.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-500\",\n                                            children: [\n                                                \"(\",\n                                                formatFileSize(file.size),\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this),\n                        !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"U\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this);\n    };\n    const TypingIndicator = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3 mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"AI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 px-4 py-3 rounded-2xl rounded-bl-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"150ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"300ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 322,\n            columnNumber: 5\n        }, this);\n    const FileUploadArea = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: uploadedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3 space-y-2\",\n                children: uploadedFiles.map((uploadedFile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 px-3 py-2 bg-neutral-800/50 border border-neutral-700 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-300 truncate flex-1\",\n                                children: uploadedFile.file.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"(\",\n                                    formatFileSize(uploadedFile.file.size),\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"button\",\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>removeFile(uploadedFile.id),\n                                className: \"h-6 w-6 p-0 hover:bg-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, uploadedFile.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    if (!isChatMode) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 border-b border-neutral-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            currentMode === 'heavy' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium\",\n                                                children: currentMode === 'heavy' ? 'Multi-Agent Mode' : 'Single Agent Mode'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    selectedAgent && currentMode === 'single' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-400\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                className: \"bg-green-500/10 text-green-400 border-green-500/20\",\n                                                children: selectedAgent.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setCurrentMode(currentMode === 'heavy' ? 'single' : 'heavy'),\n                                    className: \"border-neutral-700 text-neutral-300 hover:bg-neutral-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Switch to \",\n                                        currentMode === 'heavy' ? 'Single' : 'Multi',\n                                        \"-Agent\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-2xl mx-auto px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl font-medium text-white mb-6 leading-tight\",\n                                children: \"How can I assist you?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-400 mb-12\",\n                                children: currentMode === 'heavy' ? 'Multi-agent mode: Deploy specialized agents working together for comprehensive analysis' : selectedAgent ? \"Single agent mode: Chat directly with \".concat(selectedAgent.name) : 'Single agent mode: Focused assistance from one specialized agent'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileUploadArea, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"textarea-container relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                ref: textareaRef,\n                                                value: input,\n                                                onChange: (e)=>setInput(e.target.value),\n                                                onKeyDown: handleKeyDown,\n                                                placeholder: \"Start a new conversation...\",\n                                                className: \"textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar\",\n                                                rows: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: fileInputRef,\n                                                type: \"file\",\n                                                multiple: true,\n                                                onChange: handleFileUpload,\n                                                className: \"hidden\",\n                                                accept: \"*/*\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    var _fileInputRef_current;\n                                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                },\n                                                className: \"absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10\",\n                                                title: \"Upload files\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                size: \"sm\",\n                                                className: \"absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10\",\n                                                disabled: !input.trim() && uploadedFiles.length === 0,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 366,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 px-4 border-b border-neutral-800 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\",\n                                children: currentMode === 'heavy' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-medium text-white\",\n                                        children: [\n                                            \"AG3NT X \",\n                                            currentMode === 'heavy' ? 'Multi-Agent' : 'Single Agent'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full \".concat(apiStatus === 'connected' ? 'bg-green-500' : apiStatus === 'disconnected' ? 'bg-red-500' : 'bg-yellow-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-neutral-500\",\n                                                children: apiStatus === 'connected' ? 'Connected' : apiStatus === 'disconnected' ? 'Disconnected' : 'Checking...'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 15\n                                            }, this),\n                                            selectedAgent && currentMode === 'single' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-600\",\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-400\",\n                                                        children: selectedAgent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>setCurrentMode(currentMode === 'heavy' ? 'single' : 'heavy'),\n                            className: \"border-neutral-700 text-neutral-300 hover:bg-neutral-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this),\n                                currentMode === 'heavy' ? 'Single' : 'Multi',\n                                \"-Agent\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this),\n                    currentTaskId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"outline\",\n                        className: \"text-xs\",\n                        children: [\n                            \"Task: \",\n                            currentTaskId.slice(0, 8),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                ref: scrollAreaRef,\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-0\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatBubble, {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 13\n                            }, this)),\n                        isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 24\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 517,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-neutral-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileUploadArea, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"textarea-container relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    ref: textareaRef,\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"Type your message...\",\n                                    className: \"textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar\",\n                                    rows: 1,\n                                    disabled: isTyping\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    multiple: true,\n                                    onChange: handleFileUpload,\n                                    className: \"hidden\",\n                                    accept: \"*/*\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10\",\n                                    title: \"Upload files\",\n                                    disabled: isTyping,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    size: \"sm\",\n                                    className: \"absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10\",\n                                    disabled: !input.trim() && uploadedFiles.length === 0 || isTyping,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 528,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 527,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n        lineNumber: 462,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"vSckSmJwDa2Vu2DfFrNQ0pjlArs=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./chat-interface.tsx\n"));

/***/ })

});