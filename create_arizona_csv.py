import csv

# Arizona HOA Management Companies data extracted from hoa-usa.com
companies = [
    {
        'name': 'Associa Arizona',
        'website': 'https://www.associaarizona.com/',
        'phone': '(*************',
        'email': 'swecks<PERSON>@associaonline.com',
        'service_area': 'Scottsdale, Tucson',
        'description': 'Professional HOA management services',
        'recommended': 'Yes'
    },
    {
        'name': 'Colby Management, An Associa® Company',
        'phone': '(*************',
        'email': '<EMAIL>',
        'service_area': 'AZ statewide',
        'description': 'Associa branch office',
        'recommended': 'Yes'
    },
    {
        'name': 'FirstService Residential Arizona',
        'website': 'https://www.fsresidential.com/arizona',
        'phone': '(*************',
        'email': '',
        'service_area': 'Statewide',
        'description': 'Large residential management company',
        'recommended': 'Yes'
    },
    {
        'name': 'Vision Community Management',
        'website': '',
        'phone': '(*************',
        'email': '<EMAIL>',
        'service_area': 'AZ - Statewide',
        'description': 'Community management services',
        'recommended': 'Yes'
    },
    {
        'name': 'Vision Community Management - Northern Arizona Office',
        'website': '',
        'phone': '(*************',
        'email': '',
        'service_area': 'Flagstaff and surrounding areas',
        'description': 'Northern Arizona branch',
        'recommended': 'Yes'
    },
    {
        'name': 'Associated Asset Management (AAM)',
        'website': 'https://www.associatedasset.com/AZ',
        'phone': '(*************',
        'email': '<EMAIL>',
        'service_area': 'Statewide',
        'description': 'Asset management and HOA services',
        'recommended': 'No'
    },
    {
        'name': 'Associated Asset Management - Tucson Office',
        'website': 'https://www.associatedasset.com/AZ',
        'phone': '(*************',
        'email': '<EMAIL>',
        'service_area': 'Tucson and Surrounding Areas',
        'description': 'Tucson branch office',
        'recommended': 'No'
    },
    {
        'name': 'Paramount Association Management',
        'website': 'http://www.paramountamaz.com/',
        'phone': '(************* ext 5',
        'email': '',
        'service_area': 'AZ - Phoenix',
        'description': 'Full-service management company specializing in Master Planned Communities, HOAs, Condominiums',
        'recommended': 'Yes'
    },
    {
        'name': 'PMI San Tan',
        'website': '',
        'phone': '(*************',
        'email': '<EMAIL>',
        'service_area': 'Southeast Valley',
        'description': 'Property Management Inc. branch',
        'recommended': 'No'
    },
    {
        'name': 'Buck Reynolds Corporation',
        'website': 'https://buckreynoldscorporation.com/pms/',
        'phone': '(*************',
        'email': '',
        'service_area': 'AZ - Mohave County and Surrounding Areas (including Needles, CA)',
        'description': 'Professional community management services',
        'recommended': 'No'
    },
    {
        'name': 'Arizona Association Management Group',
        'website': 'https://www.azamg.com/',
        'phone': '(*************',
        'email': '<EMAIL>',
        'service_area': 'AZ - Statewide',
        'description': 'Comprehensive association management',
        'recommended': 'Yes'
    },
    {
        'name': 'Arizona Community Management Services, LLC',
        'website': '',
        'phone': '(*************',
        'email': '',
        'service_area': 'Arizona',
        'description': 'Community management services',
        'recommended': 'No'
    },
    {
        'name': 'Arizona HOA Management, Inc.',
        'website': '',
        'phone': '(*************',
        'email': '',
        'service_area': 'Arizona',
        'description': 'HOA management services',
        'recommended': 'No'
    },
    {
        'name': 'Sentry Management Of Arizona',
        'website': '',
        'phone': '(*************',
        'email': '',
        'service_area': 'Arizona',
        'description': 'Sentry Management branch office',
        'recommended': 'No'
    }
]

# Create CSV file
filename = 'arizona_hoa_management_companies.csv'
fieldnames = ['name', 'phone', 'email', 'website', 'service_area', 'description', 'recommended']

with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    writer.writeheader()
    
    for company in companies:
        writer.writerow(company)

print(f'Created {filename} with {len(companies)} companies')
print('\nCompanies included:')
for i, company in enumerate(companies, 1):
    print(f'{i:2d}. {company["name"]} - {company["phone"]}')
