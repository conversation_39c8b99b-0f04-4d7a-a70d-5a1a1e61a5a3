#!/usr/bin/env python3
"""
Test client for our MCP server
"""

import asyncio
import os
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mcp_server():
    """Test our MCP server functionality"""
    
    # Create server parameters for stdio connection
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_server.py"],
        env=os.environ.copy()
    )
    
    print("🚀 Starting MCP server test...")
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # Initialize the connection
                print("📡 Initializing MCP connection...")
                await session.initialize()
                print("✅ MCP connection established!")
                
                # Test 1: List available tools
                print("\n🔧 Testing tool discovery...")
                tools = await session.list_tools()
                print(f"Found {len(tools.tools)} tools:")
                for tool in tools.tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # Test 2: List available resources
                print("\n📚 Testing resource discovery...")
                resources = await session.list_resources()
                print(f"Found {len(resources.resources)} resources:")
                for resource in resources.resources:
                    print(f"  - {resource.uri}: {resource.name}")
                
                # Test 3: List available prompts
                print("\n💬 Testing prompt discovery...")
                prompts = await session.list_prompts()
                print(f"Found {len(prompts.prompts)} prompts:")
                for prompt in prompts.prompts:
                    print(f"  - {prompt.name}: {prompt.description}")
                
                # Test 4: Read a resource
                print("\n📖 Testing resource reading...")
                try:
                    resource_content = await session.read_resource("agent://system/info")
                    content_block = resource_content.contents[0]
                    if hasattr(content_block, 'text'):
                        print("System info resource:")
                        print(content_block.text[:200] + "..." if len(content_block.text) > 200 else content_block.text)
                except Exception as e:
                    print(f"Error reading resource: {e}")
                
                # Test 5: Call a tool
                print("\n🛠️ Testing tool execution...")
                try:
                    result = await session.call_tool("calculate", arguments={"expression": "2 + 2"})
                    result_content = result.content[0]
                    if hasattr(result_content, 'text'):
                        print(f"Calculator result: {result_content.text}")
                except Exception as e:
                    print(f"Error calling tool: {e}")
                
                # Test 6: Get a prompt
                print("\n📝 Testing prompt generation...")
                try:
                    prompt = await session.get_prompt("file_management_assistant", 
                                                    arguments={"task": "organize project files", "target_path": "/project"})
                    if prompt.messages:
                        message = prompt.messages[0]
                        if hasattr(message.content, 'text'):
                            print("Generated prompt:")
                            print(message.content.text[:200] + "..." if len(message.content.text) > 200 else message.content.text)
                except Exception as e:
                    print(f"Error getting prompt: {e}")
                
                print("\n🎉 MCP server test completed successfully!")
                
    except Exception as e:
        print(f"❌ MCP server test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mcp_server())
