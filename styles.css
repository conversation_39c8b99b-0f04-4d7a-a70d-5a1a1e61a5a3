/* Basic styles for the connected page */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

main {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

section {
    margin-bottom: 30px;
}

button {
    background-color: #3498db;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #2980b9;
}

input[type="text"] {
    padding: 10px;
    width: 200px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
}

form {
    display: flex;
    align-items: center;
    gap: 10px;
}

label {
    font-weight: bold;
}

#output, #greeting {
    margin-top: 15px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

@media (max-width: 600px) {
    body {
        padding: 10px;
    }
    
    main {
        padding: 20px;
    }
    
    form {
        flex-direction: column;
        align-items: stretch;
    }
    
    input[type="text"] {
        width: auto;
        margin-right: 0;
        margin-bottom: 10px;
    }
}