from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import json
import asyncio
import uuid
import sys
import os
from datetime import datetime

# Add parent directory to path to import existing modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

# Change working directory to parent directory so config.yaml can be found
os.chdir(parent_dir)

from agent import OpenRouterAgent
from orchestrator import TaskOrchestrator
from mcp_integration import add_mcp_support

app = FastAPI(
    title="Make It Heavy API",
    description="REST API for the Make It Heavy multi-agent AI system",
    version="1.0.0"
)

# Configure CORS - Allow all origins for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=False,  # Set to False when using allow_origins=["*"]
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add MCP (Model Context Protocol) support
mcp_integration = add_mcp_support(app)

# In-memory storage for tasks (in production, use a proper database)
active_tasks: Dict[str, Dict[str, Any]] = {}

class QueryRequest(BaseModel):
    query: str
    mode: str = "single"  # "single" or "heavy"

class TaskResponse(BaseModel):
    task_id: str
    status: str
    created_at: str
    query: str
    mode: str
    result: Optional[str] = None
    error: Optional[str] = None
    progress: Optional[Dict[str, Any]] = None

class ProgressUpdate(BaseModel):
    task_id: str
    status: str
    progress: Optional[Dict[str, Any]] = None
    partial_result: Optional[str] = None

@app.get("/")
async def root():
    return {"message": "Make It Heavy API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/mcp-info")
async def mcp_info():
    """Get MCP integration information"""
    return {
        "mcp_enabled": True,
        "server_name": "Make It Heavy API",
        "protocol_version": "2025-06-18",
        "transport": "streamable_http",
        "endpoints": {
            "mcp_root": "/mcp",
            "tools": "11 available",
            "resources": "2 available",
            "prompts": "2 available"
        },
        "documentation": "Visit /mcp for full MCP protocol access"
    }

@app.post("/query", response_model=TaskResponse)
async def create_query(request: QueryRequest, background_tasks: BackgroundTasks):
    """Create a new query task"""
    task_id = str(uuid.uuid4())

    # Store task info
    task_info = {
        "task_id": task_id,
        "status": "pending",
        "created_at": datetime.now().isoformat(),
        "query": request.query,
        "mode": request.mode,
        "result": None,
        "error": None,
        "progress": {}
    }

    active_tasks[task_id] = task_info

    # Start processing in background
    if request.mode == "single":
        background_tasks.add_task(process_single_agent, task_id, request.query)
    else:
        background_tasks.add_task(process_heavy_mode, task_id, request.query)

    return TaskResponse(**task_info)

@app.get("/task/{task_id}", response_model=TaskResponse)
async def get_task_status(task_id: str):
    """Get the status of a specific task"""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    return TaskResponse(**active_tasks[task_id])

@app.get("/tasks", response_model=List[TaskResponse])
async def list_tasks():
    """List all tasks"""
    return [TaskResponse(**task) for task in active_tasks.values()]

@app.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """Delete a task"""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    del active_tasks[task_id]
    return {"message": "Task deleted successfully"}

@app.get("/task/{task_id}/stream")
async def stream_task_progress(task_id: str):
    """Stream real-time progress updates for a task"""
    if task_id not in active_tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    async def generate_progress():
        try:
            last_status = None
            max_iterations = 300  # 5 minutes max
            iteration = 0

            while task_id in active_tasks and iteration < max_iterations:
                task = active_tasks[task_id]
                current_status = task["status"]

                # Send update if status changed or if still processing
                if current_status != last_status or current_status in ["processing", "pending"]:
                    update = ProgressUpdate(
                        task_id=task_id,
                        status=current_status,
                        progress=task.get("progress"),
                        partial_result=task.get("result") if current_status == "completed" else None
                    )
                    # Proper SSE format
                    yield f"data: {update.model_dump_json()}\n\n"
                    last_status = current_status

                # Break if task is completed or failed
                if current_status in ["completed", "failed"]:
                    # Send final update and close
                    yield f"data: {update.model_dump_json()}\n\n"
                    break

                await asyncio.sleep(1)  # Check every second
                iteration += 1

            # Send close event if we exit the loop
            yield "event: close\ndata: Connection closed\n\n"

        except Exception as e:
            # Send error event
            yield f"event: error\ndata: {str(e)}\n\n"

    return StreamingResponse(
        generate_progress(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Methods": "*"
        }
    )

async def process_single_agent(task_id: str, query: str):
    """Process query using single agent mode"""
    try:
        active_tasks[task_id]["status"] = "processing"
        active_tasks[task_id]["progress"] = {"stage": "initializing", "message": "Starting single agent..."}

        # Create agent
        agent = OpenRouterAgent(silent=True)

        active_tasks[task_id]["progress"] = {"stage": "processing", "message": "Agent is working on your query..."}

        # Run agent
        result = agent.run(query)

        # Update task with result
        active_tasks[task_id]["status"] = "completed"
        active_tasks[task_id]["result"] = result
        active_tasks[task_id]["progress"] = {"stage": "completed", "message": "Task completed successfully"}

    except Exception as e:
        active_tasks[task_id]["status"] = "failed"
        active_tasks[task_id]["error"] = str(e)
        active_tasks[task_id]["progress"] = {"stage": "failed", "message": f"Error: {str(e)}"}

async def process_heavy_mode(task_id: str, query: str):
    """Process query using heavy mode (multi-agent)"""
    try:
        active_tasks[task_id]["status"] = "processing"
        active_tasks[task_id]["progress"] = {"stage": "initializing", "message": "Starting heavy mode..."}

        # Create orchestrator
        orchestrator = TaskOrchestrator(silent=True)

        active_tasks[task_id]["progress"] = {
            "stage": "decomposing",
            "message": "Generating specialized questions..."
        }

        # Use the orchestrator's main method which handles everything
        active_tasks[task_id]["progress"] = {
            "stage": "processing",
            "message": "Running multi-agent analysis..."
        }

        # The orchestrate method handles decomposition, parallel execution, and synthesis
        final_result = orchestrator.orchestrate(query)

        # Update task with result
        active_tasks[task_id]["status"] = "completed"
        active_tasks[task_id]["result"] = final_result
        active_tasks[task_id]["progress"] = {"stage": "completed", "message": "Heavy mode analysis completed"}

    except Exception as e:
        active_tasks[task_id]["status"] = "failed"
        active_tasks[task_id]["error"] = str(e)
        active_tasks[task_id]["progress"] = {"stage": "failed", "message": f"Error: {str(e)}"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)