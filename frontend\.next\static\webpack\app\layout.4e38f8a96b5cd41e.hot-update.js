"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f24a2ce4f51e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjI0YTJjZTRmNTFlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./mcp-library-tab.tsx":
/*!*****************************!*\
  !*** ./mcp-library-tab.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpLibraryTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst getInitialMcpServers = ()=>[\n        {\n            id: \"make-it-heavy\",\n            name: \"Make It Heavy Agent\",\n            description: \"Our comprehensive agent system with file operations, terminal commands, web search, calculations, and system management capabilities.\",\n            author: \"Make It Heavy\",\n            version: \"1.0.0\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 59,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"Calculations\"\n            ],\n            status: \"installed\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"playwright\",\n            name: \"Playwright MCP\",\n            description: \"Professional web automation and scraping with Playwright. Navigate websites, take screenshots, extract content, and automate browser interactions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 75,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 61200,\n            tags: [\n                \"Playwright\",\n                \"Web Automation\",\n                \"Screenshots\",\n                \"Browser\"\n            ],\n            status: \"installed\",\n            gradient: \"from-green-500 to-emerald-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"filesystem\",\n            name: \"Filesystem MCP\",\n            description: \"Secure file operations with configurable access controls. Read, write, create, delete, and manage files and directories with advanced permissions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 45300,\n            tags: [\n                \"Files\",\n                \"Directories\",\n                \"Security\",\n                \"Permissions\"\n            ],\n            status: \"available\",\n            gradient: \"from-purple-500 to-pink-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"git\",\n            name: \"Git MCP\",\n            description: \"Tools to read, search, and manipulate Git repositories. Manage commits, branches, and repository operations through MCP.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 107,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 32100,\n            tags: [\n                \"Git\",\n                \"Version Control\",\n                \"Repository\",\n                \"Commits\"\n            ],\n            status: \"available\",\n            gradient: \"from-orange-500 to-red-500\",\n            lastUpdated: \"3 days ago\"\n        },\n        {\n            id: \"memory\",\n            name: \"Memory MCP\",\n            description: \"Knowledge graph-based persistent memory system. Store and retrieve information across conversations with intelligent context management.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"AI\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 123,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.7,\n            downloads: 28900,\n            tags: [\n                \"Memory\",\n                \"Knowledge Graph\",\n                \"Context\",\n                \"AI\"\n            ],\n            status: \"available\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"fetch\",\n            name: \"Fetch MCP\",\n            description: \"Web content fetching and conversion for efficient LLM usage. Convert web pages to markdown and extract structured content.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 139,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.6,\n            downloads: 19800,\n            tags: [\n                \"Web Fetch\",\n                \"Markdown\",\n                \"Content\",\n                \"Conversion\"\n            ],\n            status: \"available\",\n            gradient: \"from-indigo-500 to-purple-500\",\n            lastUpdated: \"2 days ago\"\n        },\n        {\n            id: \"time\",\n            name: \"Time MCP\",\n            description: \"Time and timezone conversion capabilities. Handle dates, times, and timezone operations with precision.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Utility\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 155,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.5,\n            downloads: 15600,\n            tags: [\n                \"Time\",\n                \"Timezone\",\n                \"Date\",\n                \"Conversion\"\n            ],\n            status: \"available\",\n            gradient: \"from-cyan-500 to-blue-500\",\n            lastUpdated: \"1 week ago\"\n        }\n    ];\nconst categories = [\n    \"All\",\n    \"Development\",\n    \"Web\",\n    \"AI\",\n    \"Utility\",\n    \"Database\",\n    \"Communication\"\n];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"bg-green-500\";\n        case \"updating\":\n            return \"bg-yellow-500\";\n        case \"available\":\n            return \"bg-gray-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"Installed\";\n        case \"updating\":\n            return \"Updating\";\n        case \"available\":\n            return \"Available\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction McpLibraryTab() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [mcpServers, setMcpServers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialMcpServers());\n    const [mcpInfo, setMcpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [addServerDialog, setAddServerDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newServer, setNewServer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        command: \"\",\n        args: \"\",\n        description: \"\",\n        category: \"Development\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"McpLibraryTab.useEffect\": ()=>{\n            const loadMcpInfo = {\n                \"McpLibraryTab.useEffect.loadMcpInfo\": async ()=>{\n                    try {\n                        const info = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.getMCPInfo();\n                        setMcpInfo(info);\n                    } catch (error) {\n                        console.error('Failed to load MCP info:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"McpLibraryTab.useEffect.loadMcpInfo\"];\n            loadMcpInfo();\n        }\n    }[\"McpLibraryTab.useEffect\"], []);\n    const filteredServers = mcpServers.filter((server)=>{\n        const matchesSearch = server.name.toLowerCase().includes(searchQuery.toLowerCase()) || server.description.toLowerCase().includes(searchQuery.toLowerCase()) || server.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"All\" || server.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleInstall = async (serverId, serverName)=>{\n        console.log(\"Installing MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n        // Update server status to installing\n        setMcpServers((prev)=>prev.map((server)=>server.id === serverId ? {\n                    ...server,\n                    status: \"updating\"\n                } : server));\n        // Simulate installation process\n        setTimeout(()=>{\n            setMcpServers((prev)=>prev.map((server)=>server.id === serverId ? {\n                        ...server,\n                        status: \"installed\",\n                        lastUpdated: \"Just now\"\n                    } : server));\n        }, 2000);\n    };\n    const handleUninstall = async (serverId, serverName)=>{\n        console.log(\"Uninstalling MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n        setMcpServers((prev)=>prev.map((server)=>server.id === serverId ? {\n                    ...server,\n                    status: \"available\",\n                    lastUpdated: \"Just now\"\n                } : server));\n    };\n    const handleServerSettings = (serverId, serverName)=>{\n        console.log(\"Opening settings for MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    };\n    const handleAddServer = ()=>{\n        if (!newServer.name || !newServer.command) return;\n        const server = {\n            id: \"custom-\".concat(Date.now()),\n            name: newServer.name,\n            description: newServer.description || \"Custom MCP server: \".concat(newServer.name),\n            author: \"Custom\",\n            version: \"1.0.0\",\n            category: newServer.category,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 278,\n                columnNumber: 13\n            }, this),\n            rating: 4.0,\n            downloads: 0,\n            tags: [\n                \"Custom\",\n                newServer.category\n            ],\n            status: \"available\",\n            gradient: \"from-gray-500 to-gray-600\",\n            lastUpdated: \"Just added\"\n        };\n        setMcpServers((prev)=>[\n                ...prev,\n                server\n            ]);\n        setNewServer({\n            name: \"\",\n            command: \"\",\n            args: \"\",\n            description: \"\",\n            category: \"Development\"\n        });\n        setAddServerDialog(false);\n    };\n    const getStatusColorNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"bg-green-500\";\n            case \"updating\":\n                return \"bg-yellow-500\";\n            case \"available\":\n                return \"bg-neutral-500\";\n            default:\n                return \"bg-neutral-500\";\n        }\n    };\n    const getStatusTextNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"Installed\";\n            case \"updating\":\n                return \"Updating\";\n            case \"available\":\n                return \"Available\";\n            default:\n                return \"Unknown\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"MCP Library\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400\",\n                                        children: \"Model Context Protocol servers and integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"secondary\",\n                                className: \"bg-gray-800 text-gray-300\",\n                                children: [\n                                    filteredServers.length,\n                                    \" of \",\n                                    mcpServers.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search MCP servers...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"\".concat(selectedCategory === category ? \"border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300\" : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                        children: filteredServers.map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200\",\n                                children: [\n                                    server.status === \"installed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            handleServerSettings(server.id, server.name);\n                                        },\n                                        className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                        title: \"Settings for \".concat(server.name),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(server.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                                children: server.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0 pr-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white truncate\",\n                                                                children: server.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(getStatusColorNew(server.status))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-neutral-400\",\n                                                                        children: getStatusTextNew(server.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-neutral-400\",\n                                                                children: server.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"v\",\n                                                                    server.version\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"by \",\n                                                                    server.author\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.downloads.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Updated \",\n                                                                    server.lastUpdated\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                        children: server.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: server.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: server.status === \"installed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleUninstall(server.id, server.name),\n                                            variant: \"outline\",\n                                            className: \"flex-1 border-neutral-700 text-neutral-300 hover:bg-neutral-800 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Installed\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 19\n                                        }, this) : server.status === \"updating\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            disabled: true,\n                                            className: \"flex-1 bg-yellow-600/20 text-yellow-400 border border-yellow-600/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleInstall(server.id, server.name),\n                                            className: \"flex-1 bg-blue-gradient-hover text-white shadow-lg border-0 transition-all duration-200 group-hover:shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Install\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, server.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, this),\n                    filteredServers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-400 mb-2\",\n                                    children: \"No MCP servers found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Try adjusting your search or filter criteria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(McpLibraryTab, \"JES5t+5BpmI/hc8eqIO6/+S0Kfg=\");\n_c = McpLibraryTab;\nvar _c;\n$RefreshReg$(_c, \"McpLibraryTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./mcp-library-tab.tsx\n"));

/***/ })

});