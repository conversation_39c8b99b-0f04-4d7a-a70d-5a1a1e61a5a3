import React from 'react';
import { Wifi, WifiOff } from 'lucide-react';

interface ApiStatusProps {
  className?: string;
}

export function ApiStatus({ className = '' }: ApiStatusProps) {
  const [isConnected, setIsConnected] = React.useState<boolean | null>(null);
  const [isChecking, setIsChecking] = React.useState(false);

  const checkApiStatus = React.useCallback(async () => {
    if (isChecking) return;
    
    setIsChecking(true);
    try {
      const response = await fetch('http://localhost:8000/health', {
        method: 'GET',
        signal: AbortSignal.timeout(3000) // 3 second timeout
      });
      setIsConnected(response.ok);
    } catch (error) {
      setIsConnected(false);
    } finally {
      setIsChecking(false);
    }
  }, [isChecking]);

  React.useEffect(() => {
    checkApiStatus();
    
    // Check status every 30 seconds
    const interval = setInterval(checkApiStatus, 30000);
    
    return () => clearInterval(interval);
  }, [checkApiStatus]);

  if (isConnected === null) {
    return (
      <div className={`flex items-center space-x-2 text-gray-400 ${className}`}>
        <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse" />
        <span className="text-xs">Checking...</span>
      </div>
    );
  }

  return (
    <div 
      className={`flex items-center space-x-2 cursor-pointer ${className}`}
      onClick={checkApiStatus}
      title={isConnected ? 'Local Agents API Connected' : 'Local Agents API Disconnected - Click to retry'}
    >
      {isConnected ? (
        <>
          <div className="w-2 h-2 rounded-full bg-green-400" />
          <Wifi size={14} className="text-green-400" />
          <span className="text-xs text-green-400">Local API</span>
        </>
      ) : (
        <>
          <div className="w-2 h-2 rounded-full bg-red-400" />
          <WifiOff size={14} className="text-red-400" />
          <span className="text-xs text-red-400">Offline</span>
        </>
      )}
    </div>
  );
}
