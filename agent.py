import json
import yaml
from openai import OpenAI
from tools import discover_tools
from tool_router import tool_router

class OpenRouterAgent:
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Silent mode for orchestrator (suppresses debug output)
        self.silent = silent
        
        # Initialize OpenAI client with OpenRouter
        self.client = OpenAI(
            base_url=self.config['openrouter']['base_url'],
            api_key=self.config['openrouter']['api_key']
        )
        
        # Discover all tools but don't load them all into context yet
        self.discovered_tools = discover_tools(self.config, silent=self.silent)

        # Tools and tool_mapping will be set dynamically per query using tool_router
    
    
    def call_llm(self, messages, tools=None):
        """Make OpenRouter API call with dynamic tools"""
        try:
            response = self.client.chat.completions.create(
                model=self.config['openrouter']['model'],
                messages=messages,
                tools=tools or []
            )
            return response
        except Exception as e:
            raise Exception(f"LLM call failed: {str(e)}")
    
    def handle_tool_call(self, tool_call, tool_mapping):
        """Handle a tool call and return the result message"""
        try:
            # Extract tool name and arguments
            tool_name = tool_call.function.name
            tool_args = json.loads(tool_call.function.arguments)

            # Call appropriate tool from dynamic tool_mapping
            if tool_name in tool_mapping:
                tool_result = tool_mapping[tool_name](**tool_args)
            else:
                tool_result = {"error": f"Tool not available in current context: {tool_name}"}

            # Return tool result message
            return {
                "role": "tool",
                "tool_call_id": tool_call.id,
                "name": tool_name,
                "content": json.dumps(tool_result)
            }

        except Exception as e:
            return {
                "role": "tool",
                "tool_call_id": tool_call.id,
                "name": tool_name,
                "content": json.dumps({"error": f"Tool execution failed: {str(e)}"})
            }
    
    def run(self, user_input: str):
        """Run the agent with user input and return FULL conversation content"""
        # 🧠 DYNAMIC TOOL ROUTING - Select relevant tools based on query
        if not self.silent:
            print(f"🔍 Analyzing query for tool selection...")

        selected_tools = tool_router.route_tools(user_input, max_tools=4)
        tool_mapping = tool_router.get_tool_mapping(selected_tools)

        if not self.silent:
            tool_names = [tool["function"]["name"] for tool in selected_tools]
            print(f"🎯 Selected {len(selected_tools)} tools: {', '.join(tool_names)}")

        # Initialize messages with system prompt and user input
        messages = [
            {
                "role": "system",
                "content": self.config['system_prompt']
            },
            {
                "role": "user",
                "content": user_input
            }
        ]

        # Track all assistant responses for full content capture
        full_response_content = []

        # Implement agentic loop from OpenRouter docs
        max_iterations = self.config.get('agent', {}).get('max_iterations', 10)
        iteration = 0
        
        while iteration < max_iterations:
            iteration += 1
            if not self.silent:
                print(f"🔄 Agent iteration {iteration}/{max_iterations}")

            # Context management: trim messages if getting too long
            messages = self._manage_context(messages)

            # Call LLM with dynamically selected tools
            response = self.call_llm(messages, tools=selected_tools)
            
            # Add the response to messages
            assistant_message = response.choices[0].message
            messages.append({
                "role": "assistant",
                "content": assistant_message.content,
                "tool_calls": assistant_message.tool_calls
            })
            
            # Capture assistant content for full response
            if assistant_message.content:
                full_response_content.append(assistant_message.content)
            
            # Check if there are tool calls
            if assistant_message.tool_calls:
                if not self.silent:
                    print(f"🔧 Agent making {len(assistant_message.tool_calls)} tool call(s)")
                # Handle each tool call
                task_completed = False
                for tool_call in assistant_message.tool_calls:
                    if not self.silent:
                        print(f"   📞 Calling tool: {tool_call.function.name}")
                    tool_result = self.handle_tool_call(tool_call, tool_mapping)
                    messages.append(tool_result)
                    
                    # Check if this was the task completion tool
                    if tool_call.function.name == "mark_task_complete":
                        task_completed = True
                        if not self.silent:
                            print("✅ Task completion tool called - exiting loop")
                        # Return FULL conversation content, not just completion message
                        return "\n\n".join(full_response_content)
                
                # If task was completed, we already returned above
                if task_completed:
                    return "\n\n".join(full_response_content)
            else:
                if not self.silent:
                    print("💭 Agent responded without tool calls - continuing loop")
            
            # Continue the loop regardless of whether there were tool calls or not
        
        # If max iterations reached, return whatever content we gathered
        return "\n\n".join(full_response_content) if full_response_content else "Maximum iterations reached. The agent may be stuck in a loop."

    def _manage_context(self, messages):
        """Manage context length to prevent token limit issues"""
        # Estimate token count (rough approximation: 1 token ≈ 4 characters)
        total_chars = sum(len(str(msg.get('content', ''))) for msg in messages)
        estimated_tokens = total_chars // 4

        # If approaching token limit, trim older messages but keep system message and recent context
        max_tokens = 100000  # Conservative limit
        if estimated_tokens > max_tokens:
            if not self.silent:
                print(f"⚠️  Context getting large ({estimated_tokens} estimated tokens), trimming...")

            # Keep system message, user message, and last few exchanges
            system_msg = messages[0] if messages and messages[0]['role'] == 'system' else None
            user_msg = messages[1] if len(messages) > 1 and messages[1]['role'] == 'user' else None
            recent_messages = messages[-10:]  # Keep last 10 messages

            trimmed_messages = []
            if system_msg:
                trimmed_messages.append(system_msg)
            if user_msg:
                trimmed_messages.append(user_msg)

            # Add context break message
            trimmed_messages.append({
                "role": "system",
                "content": "[Previous conversation context was trimmed to manage token limits. Continue with the current task.]"
            })

            # Add recent messages
            trimmed_messages.extend(recent_messages)

            return trimmed_messages

        return messages