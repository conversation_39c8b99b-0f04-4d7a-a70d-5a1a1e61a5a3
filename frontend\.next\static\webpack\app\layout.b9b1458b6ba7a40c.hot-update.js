"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./agents-tab.tsx":
/*!************************!*\
  !*** ./agents-tab.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AgentsTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst getInitialAgents = ()=>[\n        {\n            id: \"make-it-heavy\",\n            name: \"Make It Heavy Agent\",\n            description: \"Comprehensive AI agent with file operations, terminal commands, web search, calculations, and MCP integration. Your primary assistant for development and automation tasks.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Full-Stack Development\",\n            rating: 4.9,\n            users: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"MCP\",\n                \"Automation\"\n            ],\n            status: \"online\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            tools: [\n                \"read_file\",\n                \"write_file\",\n                \"run_terminal_command\",\n                \"search_web\",\n                \"calculate\"\n            ],\n            capabilities: [\n                \"File Management\",\n                \"System Operations\",\n                \"Web Research\",\n                \"Mathematical Calculations\"\n            ]\n        },\n        {\n            id: \"playwright-agent\",\n            name: \"Playwright Web Agent\",\n            description: \"Advanced web automation and scraping agent powered by Playwright. Navigate websites, take screenshots, extract content, and automate web interactions.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 47,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Web Automation\",\n            rating: 4.8,\n            users: 1,\n            tags: [\n                \"Web Scraping\",\n                \"Screenshots\",\n                \"Automation\",\n                \"Browser Control\"\n            ],\n            status: \"online\",\n            gradient: \"from-purple-500 to-pink-500\",\n            tools: [\n                \"browser_navigate\",\n                \"browser_screenshot\",\n                \"browser_click\",\n                \"browser_type\"\n            ],\n            capabilities: [\n                \"Web Navigation\",\n                \"Content Extraction\",\n                \"Form Automation\",\n                \"Visual Capture\"\n            ]\n        },\n        {\n            id: \"file-manager\",\n            name: \"File System Manager\",\n            description: \"Specialized agent for comprehensive file and directory operations. Create, read, write, move, copy, and manage files with advanced permissions and metadata handling.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"File Management\",\n            rating: 4.9,\n            users: 1,\n            tags: [\n                \"Files\",\n                \"Directories\",\n                \"CRUD\",\n                \"Permissions\"\n            ],\n            status: \"online\",\n            gradient: \"from-green-500 to-emerald-500\",\n            tools: [\n                \"create_directory\",\n                \"delete_file_or_directory\",\n                \"move_file_or_directory\",\n                \"copy_file_or_directory\"\n            ],\n            capabilities: [\n                \"File Operations\",\n                \"Directory Management\",\n                \"Permission Control\",\n                \"Metadata Access\"\n            ]\n        },\n        {\n            id: \"terminal-agent\",\n            name: \"Terminal Commander\",\n            description: \"Powerful terminal and system operations agent. Execute commands, manage processes, monitor system health, and automate shell-based workflows.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 76,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"System Operations\",\n            rating: 4.7,\n            users: 1,\n            tags: [\n                \"Terminal\",\n                \"Shell\",\n                \"Commands\",\n                \"System\"\n            ],\n            status: \"online\",\n            gradient: \"from-orange-500 to-red-500\",\n            tools: [\n                \"run_terminal_command\",\n                \"list_directory\",\n                \"get_file_info\"\n            ],\n            capabilities: [\n                \"Command Execution\",\n                \"Process Management\",\n                \"System Monitoring\",\n                \"Shell Automation\"\n            ]\n        },\n        {\n            id: \"research-agent\",\n            name: \"Research Assistant\",\n            description: \"Intelligent research agent with web search capabilities and mathematical computation. Perfect for gathering information, analyzing data, and performing calculations.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Research & Analysis\",\n            rating: 4.6,\n            users: 1,\n            tags: [\n                \"Research\",\n                \"Web Search\",\n                \"Calculations\",\n                \"Analysis\"\n            ],\n            status: \"online\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            tools: [\n                \"search_web\",\n                \"calculate\"\n            ],\n            capabilities: [\n                \"Web Research\",\n                \"Mathematical Calculations\",\n                \"Data Analysis\",\n                \"Information Gathering\"\n            ]\n        }\n    ];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"online\":\n            return \"bg-green-500\";\n        case \"busy\":\n            return \"bg-yellow-500\";\n        case \"offline\":\n            return \"bg-neutral-500\";\n        default:\n            return \"bg-neutral-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"online\":\n            return \"Online\";\n        case \"busy\":\n            return \"Busy\";\n        case \"offline\":\n            return \"Offline\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction AgentsTab() {\n    _s();\n    const [agents, setAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialAgents());\n    const [apiHealth, setApiHealth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgentsTab.useEffect\": ()=>{\n            const checkAgentStatus = {\n                \"AgentsTab.useEffect.checkAgentStatus\": async ()=>{\n                    try {\n                        const health = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.apiClient.getHealth();\n                        setApiHealth(health);\n                        // Update agent status based on API health\n                        setAgents({\n                            \"AgentsTab.useEffect.checkAgentStatus\": (prev)=>prev.map({\n                                    \"AgentsTab.useEffect.checkAgentStatus\": (agent)=>({\n                                            ...agent,\n                                            status: health.status === 'healthy' ? 'online' : 'offline'\n                                        })\n                                }[\"AgentsTab.useEffect.checkAgentStatus\"])\n                        }[\"AgentsTab.useEffect.checkAgentStatus\"]);\n                    } catch (error) {\n                        console.error('Failed to check agent status:', error);\n                        setAgents({\n                            \"AgentsTab.useEffect.checkAgentStatus\": (prev)=>prev.map({\n                                    \"AgentsTab.useEffect.checkAgentStatus\": (agent)=>({\n                                            ...agent,\n                                            status: 'offline'\n                                        })\n                                }[\"AgentsTab.useEffect.checkAgentStatus\"])\n                        }[\"AgentsTab.useEffect.checkAgentStatus\"]);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AgentsTab.useEffect.checkAgentStatus\"];\n            checkAgentStatus();\n            // Check status every 30 seconds\n            const interval = setInterval(checkAgentStatus, 30000);\n            return ({\n                \"AgentsTab.useEffect\": ()=>clearInterval(interval)\n            })[\"AgentsTab.useEffect\"];\n        }\n    }[\"AgentsTab.useEffect\"], []);\n    const handleAgentSettings = (agentId, agentName)=>{\n        console.log(\"Opening settings for \".concat(agentName, \" (ID: \").concat(agentId, \")\"));\n    };\n    const handleStartChat = (agentId, agentName)=>{\n        console.log(\"Starting chat with \".concat(agentName, \" (ID: \").concat(agentId, \")\"));\n    // This could navigate to the chat interface with a specific agent context\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 px-6 border-b border-neutral-800 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: \"AI Agents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-neutral-400\",\n                                children: \"Choose your specialized assistant\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                        variant: \"secondary\",\n                        className: \"bg-neutral-500/10 text-neutral-400 border-neutral-500/20\",\n                        children: [\n                            agents.length,\n                            \" Available\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                    children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAgentSettings(agent.id, agent.name);\n                                    },\n                                    className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-800/50 hover:bg-neutral-700/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                    title: \"Settings for \".concat(agent.name),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-3.5 w-3.5 text-gray-400 hover:text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(agent.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                            children: agent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0 pr-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white truncate\",\n                                                            children: agent.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 rounded-full \".concat(getStatusColor(agent.status))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-neutral-400\",\n                                                                    children: getStatusText(agent.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-neutral-400 mb-2\",\n                                                    children: agent.specialty\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: agent.rating\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: agent.users.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                    children: agent.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: agent.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    className: \"w-full bg-gradient-to-r \".concat(agent.gradient, \" hover:opacity-90 text-white border-0 transition-all duration-200 group-hover:shadow-lg bg-blue-gradient-hover text-white shadow-lg\"),\n                                    disabled: agent.status === \"offline\",\n                                    children: agent.status === \"offline\" ? \"Unavailable\" : \"Start Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, agent.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n_s(AgentsTab, \"I+yao7FIp7AsNQfUeBrjLXlQ+QM=\");\n_c = AgentsTab;\nvar _c;\n$RefreshReg$(_c, \"AgentsTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./agents-tab.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8404aca65640\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODQwNGFjYTY1NjQwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ })

});