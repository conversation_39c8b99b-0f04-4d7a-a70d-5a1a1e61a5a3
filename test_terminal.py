#!/usr/bin/env python3

from tool_router import tool_router

def test_terminal_routing():
    query = "run npm start to open the app"
    print(f"Query: {query}")
    
    tools = tool_router.route_tools(query)
    print(f"Selected {len(tools)} tools:")
    for tool in tools:
        name = tool["function"]["name"]
        desc = tool["function"]["description"]
        print(f"- {name}: {desc}")

if __name__ == "__main__":
    test_terminal_routing()
