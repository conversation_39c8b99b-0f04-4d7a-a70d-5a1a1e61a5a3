"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Zap, Brain, Code, Palette, MessageSquare, Star, Users, Settings, Terminal, FileText, Globe, Calculator, Activity, Loader2 } from "lucide-react"
import { apiClient, type TaskResponse, type HealthResponse } from "@/lib/api"

interface Agent {
  id: string
  name: string
  description: string
  avatar: React.ReactNode
  specialty: string
  rating: number
  users: number
  tags: string[]
  status: "online" | "offline" | "busy"
  gradient: string
  tools?: string[]
  capabilities?: string[]
}

const getInitialAgents = (): Agent[] => [
  {
    id: "ag3nt-x-core",
    name: "AG3NT X Core",
    description:
      "Comprehensive AI agent with file operations, terminal commands, web search, calculations, and MCP integration. Your primary assistant for development and automation tasks.",
    avatar: <Brain className="h-5 w-5" />,
    specialty: "Full-Stack Development",
    rating: 4.9,
    users: 1,
    tags: ["File Operations", "Terminal", "Web Search", "MCP", "Automation"],
    status: "online",
    gradient: "from-blue-500 to-cyan-500",
    tools: ["read_file", "write_file", "run_terminal_command", "search_web", "calculate"],
    capabilities: ["File Management", "System Operations", "Web Research", "Mathematical Calculations"]
  },
  {
    id: "ag3nt-x-web",
    name: "AG3NT X Web",
    description:
      "Advanced web automation and scraping agent powered by Playwright. Navigate websites, take screenshots, extract content, and automate web interactions.",
    avatar: <Globe className="h-5 w-5" />,
    specialty: "Web Automation",
    rating: 4.8,
    users: 1,
    tags: ["Web Scraping", "Screenshots", "Automation", "Browser Control"],
    status: "online",
    gradient: "from-purple-500 to-pink-500",
    tools: ["browser_navigate", "browser_screenshot", "browser_click", "browser_type"],
    capabilities: ["Web Navigation", "Content Extraction", "Form Automation", "Visual Capture"]
  },
  {
    id: "ag3nt-x-files",
    name: "AG3NT X Files",
    description:
      "Specialized agent for comprehensive file and directory operations. Create, read, write, move, copy, and manage files with advanced permissions and metadata handling.",
    avatar: <FileText className="h-5 w-5" />,
    specialty: "File Management",
    rating: 4.9,
    users: 1,
    tags: ["Files", "Directories", "CRUD", "Permissions"],
    status: "online",
    gradient: "from-green-500 to-emerald-500",
    tools: ["create_directory", "delete_file_or_directory", "move_file_or_directory", "copy_file_or_directory"],
    capabilities: ["File Operations", "Directory Management", "Permission Control", "Metadata Access"]
  },
  {
    id: "ag3nt-x-terminal",
    name: "AG3NT X Terminal",
    description: "Powerful terminal and system operations agent. Execute commands, manage processes, monitor system health, and automate shell-based workflows.",
    avatar: <Terminal className="h-5 w-5" />,
    specialty: "System Operations",
    rating: 4.7,
    users: 1,
    tags: ["Terminal", "Shell", "Commands", "System"],
    status: "online",
    gradient: "from-orange-500 to-red-500",
    tools: ["run_terminal_command", "list_directory", "get_file_info"],
    capabilities: ["Command Execution", "Process Management", "System Monitoring", "Shell Automation"]
  },
  {
    id: "ag3nt-x-research",
    name: "AG3NT X Research",
    description:
      "Intelligent research agent with web search capabilities and mathematical computation. Perfect for gathering information, analyzing data, and performing calculations.",
    avatar: <Zap className="h-5 w-5" />,
    specialty: "Research & Analysis",
    rating: 4.6,
    users: 1,
    tags: ["Research", "Web Search", "Calculations", "Analysis"],
    status: "online",
    gradient: "from-yellow-500 to-orange-500",
    tools: ["search_web", "calculate"],
    capabilities: ["Web Research", "Mathematical Calculations", "Data Analysis", "Information Gathering"]
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case "online":
      return "bg-green-500"
    case "busy":
      return "bg-yellow-500"
    case "offline":
      return "bg-neutral-500"
    default:
      return "bg-neutral-500"
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case "online":
      return "Online"
    case "busy":
      return "Busy"
    case "offline":
      return "Offline"
    default:
      return "Unknown"
  }
}

export default function AgentsTab() {
  const [agents, setAgents] = useState<Agent[]>(getInitialAgents())
  const [apiHealth, setApiHealth] = useState<HealthResponse | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkAgentStatus = async () => {
      try {
        const health = await apiClient.getHealth()
        setApiHealth(health)

        // Update agent status based on API health
        setAgents(prev => prev.map(agent => ({
          ...agent,
          status: health.status === 'healthy' ? 'online' : 'offline'
        })))
      } catch (error) {
        console.error('Failed to check agent status:', error)
        setAgents(prev => prev.map(agent => ({
          ...agent,
          status: 'offline'
        })))
      } finally {
        setLoading(false)
      }
    }

    checkAgentStatus()
    // Check status every 30 seconds
    const interval = setInterval(checkAgentStatus, 30000)
    return () => clearInterval(interval)
  }, [])

  const handleAgentSettings = (agentId: string, agentName: string) => {
    console.log(`Opening settings for ${agentName} (ID: ${agentId})`)
  }

  const handleStartChat = (agentId: string, agentName: string) => {
    console.log(`Starting chat with ${agentName} (ID: ${agentId})`)
    // Store selected agent in localStorage for the chat interface
    localStorage.setItem('selectedAgent', JSON.stringify({ id: agentId, name: agentName }))
    localStorage.setItem('chatMode', 'single')
    // Navigate to chat tab
    window.dispatchEvent(new CustomEvent('navigateToChat', { detail: { agentId, agentName } }))
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="h-16 px-6 border-b border-neutral-800 flex items-center justify-between">
        <div>
          <h1 className="text-xl font-semibold text-white">AI Agents</h1>
          <p className="text-sm text-neutral-400">Choose your specialized assistant</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-neutral-500/10 text-neutral-400 border-neutral-500/20">
            {agents.filter(a => a.status === 'online').length} Online
          </Badge>
          {apiHealth && (
            <Badge className={`${apiHealth.status === 'healthy' ? 'bg-green-500/10 text-green-400 border-green-500/20' : 'bg-red-500/10 text-red-400 border-red-500/20'}`}>
              API {apiHealth.status === 'healthy' ? 'Healthy' : 'Offline'}
            </Badge>
          )}
        </div>
      </div>

      {/* Agents Grid */}
      <ScrollArea className="flex-1 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {agents.map((agent) => (
            <div
              key={agent.id}
              className="group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue"
            >
              {/* Settings Gear Icon */}
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  handleAgentSettings(agent.id, agent.name)
                }}
                className="absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-800/50 hover:bg-neutral-700/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10"
                title={`Settings for ${agent.name}`}
              >
                <Settings className="h-3.5 w-3.5 text-gray-400 hover:text-gray-300" />
              </button>

              {/* Agent Header */}
              <div className="flex items-start gap-4 mb-4">
                <div
                  className={`w-12 h-12 rounded-xl bg-gradient-to-br ${agent.gradient} flex items-center justify-center text-white flex-shrink-0`}
                >
                  {agent.avatar}
                </div>

                <div className="flex-1 min-w-0 pr-8">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-white truncate">{agent.name}</h3>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${getStatusColor(agent.status)}`} />
                      <span className="text-xs text-neutral-400">{getStatusText(agent.status)}</span>
                    </div>
                  </div>

                  <p className="text-sm text-neutral-400 mb-2">{agent.specialty}</p>

                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                      <span>{agent.rating}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>{agent.users.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-neutral-400 mb-4 leading-relaxed">{agent.description}</p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-3">
                {agent.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* Tools & Capabilities */}
              {agent.tools && agent.tools.length > 0 && (
                <div className="mb-3">
                  <p className="text-xs font-medium text-neutral-300 mb-2">Available Tools:</p>
                  <div className="flex flex-wrap gap-1">
                    {agent.tools.slice(0, 3).map((tool) => (
                      <Badge
                        key={tool}
                        className="bg-blue-500/10 text-blue-400 border-blue-500/20 text-xs px-2 py-1"
                      >
                        {tool}
                      </Badge>
                    ))}
                    {agent.tools.length > 3 && (
                      <Badge className="bg-neutral-700 text-neutral-400 text-xs px-2 py-1">
                        +{agent.tools.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Action Button */}
              <Button
                onClick={() => handleStartChat(agent.id, agent.name)}
                className={`w-full bg-gradient-to-r ${agent.gradient} hover:opacity-90 text-white border-0 transition-all duration-200 group-hover:shadow-lg bg-blue-gradient-hover text-white shadow-lg`}
                disabled={agent.status === "offline" || loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Checking...
                  </>
                ) : agent.status === "offline" ? (
                  "Unavailable"
                ) : (
                  "Start Chat"
                )}
              </Button>

              {/* Hover Effect */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}
