"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"52095c84594c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTIwOTVjODQ1OTRjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./mcp-library-tab.tsx":
/*!*****************************!*\
  !*** ./mcp-library-tab.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpLibraryTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst getInitialMcpServers = ()=>[\n        {\n            id: \"make-it-heavy\",\n            name: \"Make It Heavy Agent\",\n            description: \"Our comprehensive agent system with file operations, terminal commands, web search, calculations, and system management capabilities.\",\n            author: \"Make It Heavy\",\n            version: \"1.0.0\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 59,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"Calculations\"\n            ],\n            status: \"installed\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"playwright\",\n            name: \"Playwright MCP\",\n            description: \"Professional web automation and scraping with Playwright. Navigate websites, take screenshots, extract content, and automate browser interactions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 75,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 61200,\n            tags: [\n                \"Playwright\",\n                \"Web Automation\",\n                \"Screenshots\",\n                \"Browser\"\n            ],\n            status: \"installed\",\n            gradient: \"from-green-500 to-emerald-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"3\",\n            name: \"Document Parser\",\n            description: \"Parse and extract content from PDFs, Word documents, spreadsheets, and other file formats with AI-powered analysis.\",\n            author: \"DocAI Labs\",\n            version: \"3.0.1\",\n            category: \"Documents\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 12400,\n            tags: [\n                \"PDF\",\n                \"Word\",\n                \"Excel\",\n                \"OCR\"\n            ],\n            status: \"installed\",\n            gradient: \"from-purple-500 to-pink-500\",\n            lastUpdated: \"3 days ago\"\n        },\n        {\n            id: \"4\",\n            name: \"Calendar Integration\",\n            description: \"Integrate with Google Calendar, Outlook, and other calendar services. Schedule meetings and manage events.\",\n            author: \"TimeSync\",\n            version: \"1.5.2\",\n            category: \"Productivity\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 107,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.6,\n            downloads: 8700,\n            tags: [\n                \"Calendar\",\n                \"Google\",\n                \"Outlook\",\n                \"Scheduling\"\n            ],\n            status: \"available\",\n            gradient: \"from-orange-500 to-red-500\",\n            lastUpdated: \"5 days ago\"\n        },\n        {\n            id: \"5\",\n            name: \"Email Assistant\",\n            description: \"Send, receive, and manage emails across multiple providers. Smart filtering and automated responses included.\",\n            author: \"MailBot Co\",\n            version: \"2.3.0\",\n            category: \"Communication\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 123,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.5,\n            downloads: 15200,\n            tags: [\n                \"Email\",\n                \"Gmail\",\n                \"Outlook\",\n                \"Automation\"\n            ],\n            status: \"updating\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            lastUpdated: \"1 day ago\"\n        },\n        {\n            id: \"6\",\n            name: \"Code Repository\",\n            description: \"Connect to GitHub, GitLab, and Bitbucket. Manage repositories, create pull requests, and analyze code.\",\n            author: \"DevTools Pro\",\n            version: \"1.9.4\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 139,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 22100,\n            tags: [\n                \"GitHub\",\n                \"GitLab\",\n                \"Git\",\n                \"Code\"\n            ],\n            status: \"available\",\n            gradient: \"from-indigo-500 to-purple-500\",\n            lastUpdated: \"4 days ago\"\n        }\n    ];\nconst categories = [\n    \"All\",\n    \"Database\",\n    \"Web\",\n    \"Documents\",\n    \"Productivity\",\n    \"Communication\",\n    \"Development\"\n];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"bg-green-500\";\n        case \"updating\":\n            return \"bg-yellow-500\";\n        case \"available\":\n            return \"bg-gray-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"Installed\";\n        case \"updating\":\n            return \"Updating\";\n        case \"available\":\n            return \"Available\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction McpLibraryTab() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const filteredServers = mcpServers.filter((server)=>{\n        const matchesSearch = server.name.toLowerCase().includes(searchQuery.toLowerCase()) || server.description.toLowerCase().includes(searchQuery.toLowerCase()) || server.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"All\" || server.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleInstall = (serverId, serverName)=>{\n        console.log(\"Installing MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for installation logic\n    };\n    const handleUninstall = (serverId, serverName)=>{\n        console.log(\"Uninstalling MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for uninstallation logic\n    };\n    const handleServerSettings = (serverId, serverName)=>{\n        console.log(\"Opening settings for MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for settings logic\n    };\n    const getStatusColorNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"bg-green-500\";\n            case \"updating\":\n                return \"bg-yellow-500\";\n            case \"available\":\n                return \"bg-neutral-500\";\n            default:\n                return \"bg-neutral-500\";\n        }\n    };\n    const getStatusTextNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"Installed\";\n            case \"updating\":\n                return \"Updating\";\n            case \"available\":\n                return \"Available\";\n            default:\n                return \"Unknown\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"MCP Library\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400\",\n                                        children: \"Model Context Protocol servers and integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"secondary\",\n                                className: \"bg-gray-800 text-gray-300\",\n                                children: [\n                                    filteredServers.length,\n                                    \" of \",\n                                    mcpServers.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search MCP servers...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"\".concat(selectedCategory === category ? \"border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300\" : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                        children: filteredServers.map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200\",\n                                children: [\n                                    server.status === \"installed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            handleServerSettings(server.id, server.name);\n                                        },\n                                        className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                        title: \"Settings for \".concat(server.name),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(server.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                                children: server.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0 pr-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white truncate\",\n                                                                children: server.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(getStatusColorNew(server.status))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-neutral-400\",\n                                                                        children: getStatusTextNew(server.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-neutral-400\",\n                                                                children: server.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"v\",\n                                                                    server.version\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"by \",\n                                                                    server.author\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.downloads.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Updated \",\n                                                                    server.lastUpdated\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                        children: server.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: server.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: server.status === \"installed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleUninstall(server.id, server.name),\n                                            variant: \"outline\",\n                                            className: \"flex-1 border-neutral-700 text-neutral-300 hover:bg-neutral-800 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Installed\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 19\n                                        }, this) : server.status === \"updating\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            disabled: true,\n                                            className: \"flex-1 bg-yellow-600/20 text-yellow-400 border border-yellow-600/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleInstall(server.id, server.name),\n                                            className: \"flex-1 bg-blue-gradient-hover text-white shadow-lg border-0 transition-all duration-200 group-hover:shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Install\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, server.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    filteredServers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-400 mb-2\",\n                                    children: \"No MCP servers found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Try adjusting your search or filter criteria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, this);\n}\n_s(McpLibraryTab, \"qPp0YF131Nuu/8g7tEzKW52PTM0=\");\n_c = McpLibraryTab;\nvar _c;\n$RefreshReg$(_c, \"McpLibraryTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./mcp-library-tab.tsx\n"));

/***/ })

});