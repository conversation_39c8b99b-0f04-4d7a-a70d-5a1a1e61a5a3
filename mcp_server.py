#!/usr/bin/env python3
"""
MCP Server for AG3NT X Agent System
Exposes our existing tools through the Model Context Protocol
"""

import asyncio
import json
import os
import sys
from typing import Any, Dict, List

from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp import Context
from tools import discover_tools
import yaml

# Load configuration
def load_config():
    """Load configuration from config.yaml"""
    try:
        with open("config.yaml", "r") as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        # Fallback config for MCP
        return {
            'openrouter': {
                'api_key': 'dummy',
                'base_url': 'dummy', 
                'model': 'dummy'
            },
            'system_prompt': 'You are a helpful AI assistant with access to various tools.'
        }

# Initialize MCP server
mcp = FastMCP("AG3NT X Agent System")

# Load our existing tools
config = load_config()
available_tools = discover_tools(config, silent=True)

# Convert our tools to MCP tools
@mcp.tool()
def read_file(path: str) -> str:
    """Read the complete contents of a file from the file system."""
    if 'read_file' in available_tools:
        result = available_tools['read_file'].execute(path=path)
        if 'error' in result:
            raise Exception(result['error'])
        return result.get('content', '')
    raise Exception("read_file tool not available")

@mcp.tool()
def write_file(path: str, content: str) -> str:
    """Create a new file or completely overwrite an existing file with new content."""
    if 'write_file' in available_tools:
        result = available_tools['write_file'].execute(path=path, content=content)
        if 'error' in result:
            raise Exception(result['error'])
        return f"Successfully wrote {result.get('bytes_written', 0)} bytes to {path}"
    raise Exception("write_file tool not available")

@mcp.tool()
def create_directory(path: str, parents: bool = True) -> str:
    """Create a new directory (folder) at the specified path."""
    if 'create_directory' in available_tools:
        result = available_tools['create_directory'].execute(path=path, parents=parents)
        if 'error' in result:
            raise Exception(result['error'])
        return result.get('message', f'Directory created: {path}')
    raise Exception("create_directory tool not available")

@mcp.tool()
def delete_file_or_directory(path: str, recursive: bool = False, force: bool = False) -> str:
    """Delete a file or directory. Use with caution as this operation cannot be undone."""
    if 'delete' in available_tools:
        result = available_tools['delete'].execute(path=path, recursive=recursive, force=force)
        if 'error' in result:
            raise Exception(result['error'])
        return result.get('message', f'Deleted: {path}')
    raise Exception("delete tool not available")

@mcp.tool()
def move_file_or_directory(source: str, destination: str, overwrite: bool = False) -> str:
    """Move or rename a file or directory from source path to destination path."""
    if 'move' in available_tools:
        result = available_tools['move'].execute(source=source, destination=destination, overwrite=overwrite)
        if 'error' in result:
            raise Exception(result['error'])
        return result.get('message', f'Moved {source} to {destination}')
    raise Exception("move tool not available")

@mcp.tool()
def copy_file_or_directory(source: str, destination: str, overwrite: bool = False, preserve_metadata: bool = True) -> str:
    """Copy a file or directory from source to destination."""
    if 'copy' in available_tools:
        result = available_tools['copy'].execute(
            source=source, 
            destination=destination, 
            overwrite=overwrite, 
            preserve_metadata=preserve_metadata
        )
        if 'error' in result:
            raise Exception(result['error'])
        return result.get('message', f'Copied {source} to {destination}')
    raise Exception("copy tool not available")

@mcp.tool()
def list_directory(path: str = ".", show_hidden: bool = False, detailed: bool = True, recursive: bool = False) -> str:
    """List the contents of a directory, showing files and subdirectories with detailed information."""
    if 'list_directory' in available_tools:
        result = available_tools['list_directory'].execute(
            path=path, 
            show_hidden=show_hidden, 
            detailed=detailed, 
            recursive=recursive
        )
        if 'error' in result:
            raise Exception(result['error'])
        
        # Format the directory listing for display
        contents = result.get('contents', [])
        summary = result.get('summary', {})
        
        output = [f"Directory listing for: {result.get('path', path)}"]
        output.append(f"Total items: {summary.get('total_items', 0)} ({summary.get('files', 0)} files, {summary.get('directories', 0)} directories)")
        output.append("")
        
        for item in contents:
            if detailed:
                size = item.get('size_formatted', 'DIR' if item.get('type') == 'directory' else '0B')
                modified = item.get('modified', '')[:19] if item.get('modified') else ''
                permissions = item.get('permissions', '')
                output.append(f"{permissions:10} {size:>8} {modified:19} {item.get('name', '')}")
            else:
                output.append(item.get('name', ''))
        
        return "\n".join(output)
    raise Exception("list_directory tool not available")

@mcp.tool()
def get_file_info(path: str, include_hash: bool = False) -> str:
    """Get detailed information about a file or directory including size, permissions, timestamps, and file type."""
    if 'file_info' in available_tools:
        result = available_tools['file_info'].execute(path=path, include_hash=include_hash)
        if 'error' in result:
            raise Exception(result['error'])
        
        # Format file info for display
        info_lines = [
            f"Path: {result.get('path', path)}",
            f"Name: {result.get('name', '')}",
            f"Type: {result.get('type', '')}",
            f"Size: {result.get('size_formatted', '')} ({result.get('size_bytes', 0)} bytes)",
            f"Permissions: {result.get('permissions', '')}",
            f"Created: {result.get('created', '')}",
            f"Modified: {result.get('modified', '')}",
            f"Accessed: {result.get('accessed', '')}",
        ]
        
        if result.get('md5_hash'):
            info_lines.append(f"MD5 Hash: {result['md5_hash']}")
        
        if result.get('mime_type'):
            info_lines.append(f"MIME Type: {result['mime_type']}")
            
        return "\n".join(info_lines)
    raise Exception("file_info tool not available")

@mcp.tool()
def run_terminal_command(command: str, working_directory: str = ".", background: bool = False, timeout: float = 30, shell: bool = True) -> str:
    """Execute terminal/shell commands. Can run commands in background or wait for completion."""
    if 'run_terminal_command' in available_tools:
        result = available_tools['run_terminal_command'].execute(
            command=command,
            working_directory=working_directory,
            background=background,
            timeout=timeout,
            shell=shell
        )
        if 'error' in result:
            raise Exception(result['error'])
        
        if background:
            return f"Command started in background: {result.get('message', '')}\nProcess ID: {result.get('process_id', '')}"
        else:
            output = []
            output.append(f"Command: {command}")
            output.append(f"Return code: {result.get('return_code', 0)}")
            if result.get('stdout'):
                output.append(f"Output:\n{result['stdout']}")
            if result.get('stderr'):
                output.append(f"Errors:\n{result['stderr']}")
            return "\n".join(output)
    raise Exception("run_terminal_command tool not available")

@mcp.tool()
def search_web(query: str, max_results: int = 5) -> str:
    """Search the web using DuckDuckGo for current information."""
    if 'search_web' in available_tools:
        result = available_tools['search_web'].execute(query=query, max_results=max_results)
        if 'error' in result:
            raise Exception(result['error'])
        
        results = result.get('results', [])
        output = [f"Search results for: {query}"]
        output.append("")
        
        for i, item in enumerate(results, 1):
            output.append(f"{i}. {item.get('title', '')}")
            output.append(f"   URL: {item.get('url', '')}")
            if item.get('snippet'):
                output.append(f"   {item['snippet']}")
            output.append("")
        
        return "\n".join(output)
    raise Exception("search_web tool not available")

@mcp.tool()
def calculate(expression: str) -> str:
    """Perform mathematical calculations and evaluations."""
    if 'calculate' in available_tools:
        result = available_tools['calculate'].execute(expression=expression)
        if 'error' in result:
            raise Exception(result['error'])
        return f"{expression} = {result.get('result', '')}"
    raise Exception("calculate tool not available")

# Add resources for agent system information
@mcp.resource("agent://system/info")
def get_system_info() -> str:
    """Get information about the AG3NT X agent system."""
    return json.dumps({
        "name": "AG3NT X Agent System",
        "description": "A powerful AI agent system with comprehensive tool support",
        "version": "1.0.0",
        "capabilities": [
            "File and directory operations",
            "Terminal command execution", 
            "Web search",
            "Mathematical calculations",
            "Dynamic tool routing",
            "Context management"
        ],
        "available_tools": list(available_tools.keys()),
        "mcp_compatible": True
    }, indent=2)

@mcp.resource("agent://tools/list")
def get_tools_list() -> str:
    """Get a list of all available tools with descriptions."""
    tools_info = {}
    for name, tool in available_tools.items():
        tools_info[name] = {
            "name": tool.name,
            "description": tool.description,
            "parameters": tool.parameters
        }
    return json.dumps(tools_info, indent=2)

# Add prompts for common agent interactions
@mcp.prompt()
def file_management_assistant(task: str, target_path: str = "") -> str:
    """Generate a prompt for file management tasks."""
    return f"""You are a file management assistant. Help the user with the following task:

Task: {task}
Target path: {target_path}

Available file operations:
- read_file: Read file contents
- write_file: Create or overwrite files
- create_directory: Create folders
- delete_file_or_directory: Remove files/folders
- move_file_or_directory: Move or rename items
- copy_file_or_directory: Copy files/folders
- list_directory: Browse directory contents
- get_file_info: Get detailed file information

Please help the user accomplish their file management task safely and efficiently."""

@mcp.prompt()
def development_assistant(language: str = "python", task: str = "") -> str:
    """Generate a prompt for development tasks."""
    return f"""You are a development assistant specializing in {language}. Help with the following task:

Task: {task}

Available development tools:
- File operations: Create, read, write, and manage code files
- Terminal commands: Run build tools, package managers, tests
- Web search: Find documentation and solutions
- Directory management: Organize project structure

Please provide step-by-step guidance for the development task."""

if __name__ == "__main__":
    # Run the MCP server
    mcp.run()
