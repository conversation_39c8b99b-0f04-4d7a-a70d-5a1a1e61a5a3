"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/client-layout.tsx":
/*!*******************************!*\
  !*** ./app/client-layout.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,Home,Package,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,Home,Package,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,Home,Package,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,Home,Package,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,Home,Package,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,Home,Package,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _agents_tab__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../agents-tab */ \"(app-pages-browser)/./agents-tab.tsx\");\n/* harmony import */ var _mcp_library_tab__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../mcp-library-tab */ \"(app-pages-browser)/./mcp-library-tab.tsx\");\n/* harmony import */ var _components_dashboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard */ \"(app-pages-browser)/./components/dashboard.tsx\");\n/* harmony import */ var _chat_interface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../chat-interface */ \"(app-pages-browser)/./chat-interface.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ClientLayout(param) {\n    let { children } = param;\n    _s();\n    const [activeTab, setActiveTab] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"home\");\n    const [selectedAgent, setSelectedAgent] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    const [chatMode, setChatMode] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('heavy');\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ClientLayout.useEffect\": ()=>{\n            // Listen for navigation events from agents tab\n            const handleNavigateToChat = {\n                \"ClientLayout.useEffect.handleNavigateToChat\": (event)=>{\n                    const { agentId, agentName } = event.detail;\n                    setSelectedAgent({\n                        id: agentId,\n                        name: agentName\n                    });\n                    setChatMode('single');\n                    setActiveTab('home');\n                }\n            }[\"ClientLayout.useEffect.handleNavigateToChat\"];\n            // Load saved agent and mode from localStorage\n            const savedAgent = localStorage.getItem('selectedAgent');\n            const savedMode = localStorage.getItem('chatMode');\n            if (savedAgent) {\n                setSelectedAgent(JSON.parse(savedAgent));\n            }\n            if (savedMode) {\n                setChatMode(savedMode);\n            }\n            window.addEventListener('navigateToChat', handleNavigateToChat);\n            return ({\n                \"ClientLayout.useEffect\": ()=>{\n                    window.removeEventListener('navigateToChat', handleNavigateToChat);\n                }\n            })[\"ClientLayout.useEffect\"];\n        }\n    }[\"ClientLayout.useEffect\"], []);\n    const renderContent = ()=>{\n        switch(activeTab){\n            case \"dashboard\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 16\n                }, this);\n            case \"agents\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_agents_tab__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, this);\n            case \"mcp-library\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mcp_library_tab__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, this);\n            case \"settings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-medium text-white mb-4\",\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-400\",\n                                children: \"Settings panel coming soon...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chat_interface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    mode: chatMode,\n                    selectedAgent: selectedAgent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-black text-white p-4 gap-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-64 bg-neutral-900 border border-neutral-800 rounded-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 p-4 border-b border-neutral-800 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"AG3NT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-500 font-bold\",\n                                        children: \"X\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-auto flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-blue-500/10 px-2 py-1 rounded text-blue-400 border border-blue-500/20\",\n                                                children: \"MCP\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-neutral-500\",\n                                                children: \"v1.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                            className: \"h-[calc(100vh-140px)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>{\n                                            setSelectedAgent(null);\n                                            setChatMode('heavy');\n                                            setActiveTab('home');\n                                            localStorage.removeItem('selectedAgent');\n                                            localStorage.removeItem('chatMode');\n                                        },\n                                        className: \"w-full bg-blue-gradient-hover text-white shadow-lg\",\n                                        children: \"New Multi-Agent Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xs font-medium text-neutral-500 mb-3 uppercase tracking-wider\",\n                                                children: \"Explore\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: ()=>setActiveTab(\"home\"),\n                                                        className: \"w-full justify-start h-9 px-3 \".concat(activeTab === \"home\" ? \"bg-blue-500/10 text-blue-400\" : \"text-neutral-400 hover:text-white hover:bg-blue-500/10\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Chat\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: ()=>setActiveTab(\"dashboard\"),\n                                                        className: \"w-full justify-start h-9 px-3 \".concat(activeTab === \"dashboard\" ? \"bg-blue-500/10 text-blue-400\" : \"text-neutral-400 hover:text-white hover:bg-blue-500/10\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Dashboard\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: ()=>setActiveTab(\"agents\"),\n                                                        className: \"w-full justify-start h-9 px-3 \".concat(activeTab === \"agents\" ? \"bg-blue-500/10 text-blue-400\" : \"text-neutral-400 hover:text-white hover:bg-blue-500/10\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Agents\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: ()=>setActiveTab(\"mcp-library\"),\n                                                        className: \"w-full justify-start h-9 px-3 \".concat(activeTab === \"mcp-library\" ? \"bg-blue-500/10 text-blue-400\" : \"text-neutral-400 hover:text-white hover:bg-blue-500/10\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"MCP Library\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: ()=>setActiveTab(\"settings\"),\n                                                        className: \"w-full justify-start h-9 px-3 \".concat(activeTab === \"settings\" ? \"bg-blue-500/10 text-blue-400\" : \"text-neutral-400 hover:text-white hover:bg-blue-500/10\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-3 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Settings\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xs font-medium text-neutral-500 mb-3 uppercase tracking-wider\",\n                                                children: \"Conversations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-neutral-500\",\n                                                children: \"No conversations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-4 left-4 right-4 w-56 p-4 border-t border-neutral-800 rounded-b-xl bg-gradient-to-t from-[#2b2b2b]/80 to-transparent backdrop-blur-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: \"w-full justify-start h-10 px-3 text-neutral-400 hover:text-white hover:bg-blue-500/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-6 rounded-full bg-neutral-600 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: \"@ag3nt_x\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-neutral-500 truncate\",\n                                                    children: \"AG3NT X Agent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_Home_Package_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 bg-neutral-950 rounded-xl\",\n                    children: renderContent()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\app\\\\client-layout.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientLayout, \"lqu9Sl+fsTJGv/J3LT0YfQINFCM=\");\n_c = ClientLayout;\nvar _c;\n$RefreshReg$(_c, \"ClientLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/client-layout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2a45d725e55d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmE0NWQ3MjVlNTVkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ })

});