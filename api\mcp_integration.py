"""
MCP Integration for AG3NT X FastAPI Backend
Adds Model Context Protocol support to our existing API
"""

import asyncio
import json
from typing import Dict, Any
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp import Context
import sys
import os

# Add parent directory to path to import tools
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools import discover_tools
import yaml

class MCPIntegration:
    """Integration class to add MCP support to FastAPI"""
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.mcp_server = None
        self.available_tools = None
        self.setup_mcp()
        
    def load_config(self):
        """Load configuration from config.yaml"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.yaml")
            with open(config_path, "r") as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            return {
                'openrouter': {'api_key': 'dummy', 'base_url': 'dummy', 'model': 'dummy'},
                'system_prompt': 'You are a helpful AI assistant.'
            }
    
    def setup_mcp(self):
        """Setup MCP server with our tools"""
        # Initialize MCP server for HTTP transport
        self.mcp_server = FastMCP("AG3NT X API", stateless_http=True)
        
        # Load our existing tools
        config = self.load_config()
        self.available_tools = discover_tools(config, silent=True)
        
        # Register all our tools with MCP
        self.register_mcp_tools()
        self.register_mcp_resources()
        self.register_mcp_prompts()
        
        # Mount MCP endpoints to FastAPI
        self.mount_mcp_endpoints()
    
    def register_mcp_tools(self):
        """Register our existing tools as MCP tools"""
        
        @self.mcp_server.tool()
        def read_file(path: str) -> str:
            """Read file contents"""
            if 'read_file' in self.available_tools:
                result = self.available_tools['read_file'].execute(path=path)
                if 'error' in result:
                    raise Exception(result['error'])
                return result.get('content', '')
            raise Exception("read_file tool not available")
        
        @self.mcp_server.tool()
        def write_file(path: str, content: str) -> str:
            """Write content to file"""
            if 'write_file' in self.available_tools:
                result = self.available_tools['write_file'].execute(path=path, content=content)
                if 'error' in result:
                    raise Exception(result['error'])
                return f"Successfully wrote {result.get('bytes_written', 0)} bytes to {path}"
            raise Exception("write_file tool not available")
        
        @self.mcp_server.tool()
        def list_directory(path: str = ".", detailed: bool = True) -> str:
            """List directory contents"""
            if 'list_directory' in self.available_tools:
                result = self.available_tools['list_directory'].execute(path=path, detailed=detailed)
                if 'error' in result:
                    raise Exception(result['error'])
                
                contents = result.get('contents', [])
                summary = result.get('summary', {})
                
                output = [f"Directory: {result.get('path', path)}"]
                output.append(f"Items: {summary.get('total_items', 0)} ({summary.get('files', 0)} files, {summary.get('directories', 0)} directories)")
                output.append("")
                
                for item in contents[:20]:  # Limit to first 20 items
                    if detailed:
                        size = item.get('size_formatted', 'DIR' if item.get('type') == 'directory' else '0B')
                        output.append(f"{item.get('name', ''):30} {size:>8} {item.get('type', '')}")
                    else:
                        output.append(item.get('name', ''))
                
                if len(contents) > 20:
                    output.append(f"... and {len(contents) - 20} more items")
                
                return "\n".join(output)
            raise Exception("list_directory tool not available")
        
        @self.mcp_server.tool()
        def run_command(command: str, working_directory: str = ".") -> str:
            """Execute terminal command"""
            if 'run_terminal_command' in self.available_tools:
                result = self.available_tools['run_terminal_command'].execute(
                    command=command,
                    working_directory=working_directory,
                    background=False,
                    timeout=30
                )
                if 'error' in result:
                    raise Exception(result['error'])
                
                output = [f"Command: {command}"]
                output.append(f"Exit code: {result.get('return_code', 0)}")
                if result.get('stdout'):
                    output.append(f"Output:\n{result['stdout']}")
                if result.get('stderr'):
                    output.append(f"Errors:\n{result['stderr']}")
                return "\n".join(output)
            raise Exception("run_terminal_command tool not available")
        
        @self.mcp_server.tool()
        def search_web(query: str, max_results: int = 3) -> str:
            """Search the web"""
            if 'search_web' in self.available_tools:
                result = self.available_tools['search_web'].execute(query=query, max_results=max_results)
                if 'error' in result:
                    raise Exception(result['error'])
                
                results = result.get('results', [])
                output = [f"Search: {query}"]
                output.append("")
                
                for i, item in enumerate(results, 1):
                    output.append(f"{i}. {item.get('title', '')}")
                    output.append(f"   {item.get('url', '')}")
                    if item.get('snippet'):
                        output.append(f"   {item['snippet'][:100]}...")
                    output.append("")
                
                return "\n".join(output)
            raise Exception("search_web tool not available")
        
        @self.mcp_server.tool()
        def calculate(expression: str) -> str:
            """Perform calculation"""
            if 'calculate' in self.available_tools:
                result = self.available_tools['calculate'].execute(expression=expression)
                if 'error' in result:
                    raise Exception(result['error'])
                return f"{expression} = {result.get('result', '')}"
            raise Exception("calculate tool not available")
    
    def register_mcp_resources(self):
        """Register MCP resources"""
        
        @self.mcp_server.resource("api://system/status")
        def get_api_status() -> str:
            """Get API system status"""
            return json.dumps({
                "status": "running",
                "mcp_enabled": True,
                "available_tools": list(self.available_tools.keys()) if self.available_tools else [],
                "endpoints": [
                    "/health",
                    "/query", 
                    "/tasks",
                    "/mcp"
                ]
            }, indent=2)
        
        @self.mcp_server.resource("api://tools/documentation")
        def get_tools_documentation() -> str:
            """Get detailed tool documentation"""
            if not self.available_tools:
                return "No tools available"
            
            docs = {"tools": {}}
            for name, tool in self.available_tools.items():
                docs["tools"][name] = {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.parameters
                }
            
            return json.dumps(docs, indent=2)
    
    def register_mcp_prompts(self):
        """Register MCP prompts"""
        
        @self.mcp_server.prompt()
        def api_integration_guide(task: str = "general") -> str:
            """Generate guidance for API integration tasks"""
            return f"""You are an API integration assistant. Help with the following task: {task}

Available API endpoints:
- POST /query: Submit queries to the agent system
- GET /tasks: List all tasks
- GET /task/{{id}}: Get specific task status
- GET /health: Check API health

Available MCP tools:
- File operations: read_file, write_file, list_directory
- System operations: run_command
- Information: search_web, calculate

The API supports both traditional REST calls and MCP protocol for tool integration.
Provide step-by-step guidance for the integration task."""
        
        @self.mcp_server.prompt()
        def agent_query_helper(domain: str = "general", complexity: str = "simple") -> str:
            """Generate optimized prompts for agent queries"""
            return f"""You are helping to construct an effective query for the AG3NT X agent system.

Domain: {domain}
Complexity: {complexity}

The agent has access to:
- File system operations
- Terminal/shell commands  
- Web search capabilities
- Mathematical calculations
- Directory management

For {complexity} {domain} tasks, structure your query to:
1. Be specific about the desired outcome
2. Mention relevant file paths or directories
3. Specify any constraints or preferences
4. Include context about the environment

Example query structure: "Please [action] [target] by [method] while [constraints]"
"""
    
    def mount_mcp_endpoints(self):
        """Mount MCP endpoints to FastAPI app"""
        
        # Mount the MCP server as a sub-application
        mcp_app = self.mcp_server.streamable_http_app()
        self.app.mount("/mcp", mcp_app)
        
        # Add a convenience endpoint for MCP info
        @self.app.get("/mcp/info")
        async def mcp_info():
            """Get information about MCP integration"""
            return {
                "mcp_enabled": True,
                "server_name": "Make It Heavy API",
                "protocol_version": "2025-06-18",
                "transport": "streamable_http",
                "endpoints": {
                    "mcp_root": "/mcp",
                    "tools": f"{len(self.available_tools)} available" if self.available_tools else "0 available",
                    "resources": "2 available",
                    "prompts": "2 available"
                },
                "documentation": "Visit /mcp for full MCP protocol access"
            }

def add_mcp_support(app: FastAPI) -> MCPIntegration:
    """Add MCP support to an existing FastAPI application"""
    return MCPIntegration(app)
