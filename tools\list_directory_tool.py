from .base_tool import BaseTool
import os
import stat
from datetime import datetime

class ListDirectoryTool(BaseTool):
    def __init__(self, config: dict):
        self.config = config

    @property
    def name(self) -> str:
        return "list_directory"

    @property
    def description(self) -> str:
        return "List directory contents with file details."

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "The directory path to list (default: current directory)",
                    "default": "."
                },
                "show_hidden": {
                    "type": "boolean",
                    "description": "Whether to show hidden files and directories (default: False)",
                    "default": False
                },
                "detailed": {
                    "type": "boolean",
                    "description": "Whether to show detailed information like size and timestamps (default: True)",
                    "default": True
                },
                "recursive": {
                    "type": "boolean",
                    "description": "Whether to list subdirectories recursively (default: False)",
                    "default": False
                }
            },
            "required": []
        }

    def execute(self, path: str = ".", show_hidden: bool = False, detailed: bool = True, recursive: bool = False) -> dict:
        try:
            # Get absolute path
            abs_path = os.path.abspath(path)

            # Check if path exists and is a directory
            if not os.path.exists(abs_path):
                return {"error": f"Path does not exist: {path}"}

            if not os.path.isdir(abs_path):
                return {"error": f"Path is not a directory: {path}"}

            def format_size(size_bytes):
                """Format file size in human readable format"""
                for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
                    if size_bytes < 1024.0:
                        return f"{size_bytes:.1f} {unit}"
                    size_bytes /= 1024.0
                return f"{size_bytes:.1f} PB"

            def get_file_info(file_path):
                """Get detailed information about a file or directory"""
                try:
                    stat_info = os.stat(file_path)
                    is_dir = os.path.isdir(file_path)

                    info = {
                        "name": os.path.basename(file_path),
                        "type": "directory" if is_dir else "file",
                        "path": file_path
                    }

                    if detailed:
                        info.update({
                            "size": 0 if is_dir else stat_info.st_size,
                            "size_formatted": "DIR" if is_dir else format_size(stat_info.st_size),
                            "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                            "permissions": stat.filemode(stat_info.st_mode),
                            "is_hidden": os.path.basename(file_path).startswith('.')
                        })

                    return info
                except (OSError, PermissionError):
                    return {
                        "name": os.path.basename(file_path),
                        "type": "unknown",
                        "path": file_path,
                        "error": "Permission denied or file not accessible"
                    }

            def list_directory_contents(dir_path, current_depth=0, max_depth=None):
                """Recursively list directory contents"""
                items = []

                try:
                    entries = os.listdir(dir_path)
                    entries.sort()  # Sort alphabetically

                    for entry in entries:
                        # Skip hidden files if not requested
                        if not show_hidden and entry.startswith('.'):
                            continue

                        entry_path = os.path.join(dir_path, entry)
                        file_info = get_file_info(entry_path)

                        # Add indentation for recursive listing
                        if recursive and current_depth > 0:
                            file_info["indent"] = "  " * current_depth

                        items.append(file_info)

                        # Recurse into subdirectories if requested
                        if recursive and os.path.isdir(entry_path) and (max_depth is None or current_depth < max_depth):
                            sub_items = list_directory_contents(entry_path, current_depth + 1, max_depth)
                            items.extend(sub_items)

                except PermissionError:
                    items.append({
                        "name": f"<Permission denied for {dir_path}>",
                        "type": "error",
                        "path": dir_path
                    })

                return items

            # List contents
            max_depth = 3 if recursive else None  # Limit recursion depth for safety
            contents = list_directory_contents(abs_path, max_depth=max_depth)

            # Summary statistics
            total_files = sum(1 for item in contents if item.get("type") == "file")
            total_dirs = sum(1 for item in contents if item.get("type") == "directory")
            total_size = sum(item.get("size", 0) for item in contents if item.get("type") == "file")

            return {
                "path": abs_path,
                "success": True,
                "contents": contents,
                "summary": {
                    "total_items": len(contents),
                    "files": total_files,
                    "directories": total_dirs,
                    "total_size_bytes": total_size,
                    "total_size_formatted": format_size(total_size)
                },
                "options": {
                    "show_hidden": show_hidden,
                    "detailed": detailed,
                    "recursive": recursive
                }
            }

        except PermissionError:
            return {"error": f"Permission denied accessing directory: {path}"}
        except OSError as e:
            return {"error": f"OS error listing directory: {str(e)}"}
        except Exception as e:
            return {"error": f"Failed to list directory: {str(e)}"}