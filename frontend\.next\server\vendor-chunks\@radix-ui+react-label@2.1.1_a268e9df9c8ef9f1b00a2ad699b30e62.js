"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-label@2.1.1_a268e9df9c8ef9f1b00a2ad699b30e62";
exports.ids = ["vendor-chunks/@radix-ui+react-label@2.1.1_a268e9df9c8ef9f1b00a2ad699b30e62"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_a268e9df9c8ef9f1b00a2ad699b30e62/node_modules/@radix-ui/react-label/dist/index.mjs":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-label@2.1.1_a268e9df9c8ef9f1b00a2ad699b30e62/node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // packages/react/label/src/Label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            props.onMouseDown?.(event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-label@2.1.1_a268e9df9c8ef9f1b00a2ad699b30e62/node_modules/@radix-ui/react-label/dist/index.mjs\n");

/***/ })

};
;