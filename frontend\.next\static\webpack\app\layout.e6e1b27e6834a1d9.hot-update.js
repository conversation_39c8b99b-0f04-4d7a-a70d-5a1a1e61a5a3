"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e1bcb5feeca7\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTFiY2I1ZmVlY2E3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./mcp-library-tab.tsx":
/*!*****************************!*\
  !*** ./mcp-library-tab.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpLibraryTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Calendar,CheckCircle,Code,Download,FileText,Globe,Mail,Package,Search,Settings,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst getInitialMcpServers = ()=>[\n        {\n            id: \"make-it-heavy\",\n            name: \"Make It Heavy Agent\",\n            description: \"Our comprehensive agent system with file operations, terminal commands, web search, calculations, and system management capabilities.\",\n            author: \"Make It Heavy\",\n            version: \"1.0.0\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 59,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"Calculations\"\n            ],\n            status: \"installed\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"playwright\",\n            name: \"Playwright MCP\",\n            description: \"Professional web automation and scraping with Playwright. Navigate websites, take screenshots, extract content, and automate browser interactions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 75,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 61200,\n            tags: [\n                \"Playwright\",\n                \"Web Automation\",\n                \"Screenshots\",\n                \"Browser\"\n            ],\n            status: \"installed\",\n            gradient: \"from-green-500 to-emerald-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"filesystem\",\n            name: \"Filesystem MCP\",\n            description: \"Secure file operations with configurable access controls. Read, write, create, delete, and manage files and directories with advanced permissions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 45300,\n            tags: [\n                \"Files\",\n                \"Directories\",\n                \"Security\",\n                \"Permissions\"\n            ],\n            status: \"available\",\n            gradient: \"from-purple-500 to-pink-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"4\",\n            name: \"Calendar Integration\",\n            description: \"Integrate with Google Calendar, Outlook, and other calendar services. Schedule meetings and manage events.\",\n            author: \"TimeSync\",\n            version: \"1.5.2\",\n            category: \"Productivity\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 107,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.6,\n            downloads: 8700,\n            tags: [\n                \"Calendar\",\n                \"Google\",\n                \"Outlook\",\n                \"Scheduling\"\n            ],\n            status: \"available\",\n            gradient: \"from-orange-500 to-red-500\",\n            lastUpdated: \"5 days ago\"\n        },\n        {\n            id: \"5\",\n            name: \"Email Assistant\",\n            description: \"Send, receive, and manage emails across multiple providers. Smart filtering and automated responses included.\",\n            author: \"MailBot Co\",\n            version: \"2.3.0\",\n            category: \"Communication\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 123,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.5,\n            downloads: 15200,\n            tags: [\n                \"Email\",\n                \"Gmail\",\n                \"Outlook\",\n                \"Automation\"\n            ],\n            status: \"updating\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            lastUpdated: \"1 day ago\"\n        },\n        {\n            id: \"6\",\n            name: \"Code Repository\",\n            description: \"Connect to GitHub, GitLab, and Bitbucket. Manage repositories, create pull requests, and analyze code.\",\n            author: \"DevTools Pro\",\n            version: \"1.9.4\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 139,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 22100,\n            tags: [\n                \"GitHub\",\n                \"GitLab\",\n                \"Git\",\n                \"Code\"\n            ],\n            status: \"available\",\n            gradient: \"from-indigo-500 to-purple-500\",\n            lastUpdated: \"4 days ago\"\n        }\n    ];\nconst categories = [\n    \"All\",\n    \"Database\",\n    \"Web\",\n    \"Documents\",\n    \"Productivity\",\n    \"Communication\",\n    \"Development\"\n];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"bg-green-500\";\n        case \"updating\":\n            return \"bg-yellow-500\";\n        case \"available\":\n            return \"bg-gray-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"Installed\";\n        case \"updating\":\n            return \"Updating\";\n        case \"available\":\n            return \"Available\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction McpLibraryTab() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const filteredServers = mcpServers.filter((server)=>{\n        const matchesSearch = server.name.toLowerCase().includes(searchQuery.toLowerCase()) || server.description.toLowerCase().includes(searchQuery.toLowerCase()) || server.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"All\" || server.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleInstall = (serverId, serverName)=>{\n        console.log(\"Installing MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for installation logic\n    };\n    const handleUninstall = (serverId, serverName)=>{\n        console.log(\"Uninstalling MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for uninstallation logic\n    };\n    const handleServerSettings = (serverId, serverName)=>{\n        console.log(\"Opening settings for MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for settings logic\n    };\n    const getStatusColorNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"bg-green-500\";\n            case \"updating\":\n                return \"bg-yellow-500\";\n            case \"available\":\n                return \"bg-neutral-500\";\n            default:\n                return \"bg-neutral-500\";\n        }\n    };\n    const getStatusTextNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"Installed\";\n            case \"updating\":\n                return \"Updating\";\n            case \"available\":\n                return \"Available\";\n            default:\n                return \"Unknown\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"MCP Library\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400\",\n                                        children: \"Model Context Protocol servers and integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"secondary\",\n                                className: \"bg-gray-800 text-gray-300\",\n                                children: [\n                                    filteredServers.length,\n                                    \" of \",\n                                    mcpServers.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search MCP servers...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"\".concat(selectedCategory === category ? \"border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300\" : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                        children: filteredServers.map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200\",\n                                children: [\n                                    server.status === \"installed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            handleServerSettings(server.id, server.name);\n                                        },\n                                        className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                        title: \"Settings for \".concat(server.name),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(server.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                                children: server.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0 pr-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white truncate\",\n                                                                children: server.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(getStatusColorNew(server.status))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-neutral-400\",\n                                                                        children: getStatusTextNew(server.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-neutral-400\",\n                                                                children: server.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"v\",\n                                                                    server.version\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"by \",\n                                                                    server.author\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.downloads.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Updated \",\n                                                                    server.lastUpdated\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                        children: server.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: server.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: server.status === \"installed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleUninstall(server.id, server.name),\n                                            variant: \"outline\",\n                                            className: \"flex-1 border-neutral-700 text-neutral-300 hover:bg-neutral-800 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Installed\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 19\n                                        }, this) : server.status === \"updating\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            disabled: true,\n                                            className: \"flex-1 bg-yellow-600/20 text-yellow-400 border border-yellow-600/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleInstall(server.id, server.name),\n                                            className: \"flex-1 bg-blue-gradient-hover text-white shadow-lg border-0 transition-all duration-200 group-hover:shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Install\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, server.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    filteredServers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Calendar_CheckCircle_Code_Download_FileText_Globe_Mail_Package_Search_Settings_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-400 mb-2\",\n                                    children: \"No MCP servers found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Try adjusting your search or filter criteria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, this);\n}\n_s(McpLibraryTab, \"qPp0YF131Nuu/8g7tEzKW52PTM0=\");\n_c = McpLibraryTab;\nvar _c;\n$RefreshReg$(_c, \"McpLibraryTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./mcp-library-tab.tsx\n"));

/***/ })

});