'use client';

import { useState, useEffect } from 'react';
import { Brain, Zap, Users, Send, Loader2, CheckCircle, XCircle, Clock } from 'lucide-react';
import { apiClient, type TaskResponse, type ProgressUpdate } from '@/lib/api';
import { cn } from '@/lib/utils';

export default function Home() {
  const [query, setQuery] = useState('');
  const [mode, setMode] = useState<'single' | 'heavy'>('single');
  const [currentTask, setCurrentTask] = useState<TaskResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState<ProgressUpdate | null>(null);
  const [recentTasks, setRecentTasks] = useState<TaskResponse[]>([]);

  // Load recent tasks on mount
  useEffect(() => {
    loadRecentTasks();
  }, []);

  const loadRecentTasks = async () => {
    try {
      const tasks = await apiClient.listTasks();
      setRecentTasks(tasks.slice(0, 5)); // Show last 5 tasks
    } catch (error) {
      console.error('Failed to load recent tasks:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim() || isLoading) return;

    setIsLoading(true);
    setCurrentTask(null);
    setProgress(null);

    try {
      const task = await apiClient.createQuery({ query: query.trim(), mode });
      setCurrentTask(task);

      // Start streaming progress updates
      const cleanup = apiClient.streamTaskProgress(
        task.task_id,
        (update) => {
          setProgress(update);
          if (update.status === 'completed' || update.status === 'failed') {
            setIsLoading(false);
            // Refresh the task to get final result
            apiClient.getTaskStatus(task.task_id).then(setCurrentTask);
            loadRecentTasks();
          }
        },
        (error) => {
          console.error('Progress stream error:', error);
          setIsLoading(false);
        }
      );

      // Cleanup on unmount or new task
      return cleanup;
    } catch (error) {
      console.error('Failed to create query:', error);
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'processing':
        return <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-neutral-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="border-b border-neutral-800 bg-neutral-950">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center gap-3">
            <div className="bg-blue-gradient p-2 rounded-lg">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-white">Make It Heavy</h1>
              <p className="text-sm text-neutral-400">Multi-Agent AI Analysis System</p>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-6 py-8">
        {/* Query Form */}
        <div className="bg-neutral-900 border border-neutral-800 rounded-lg p-6 mb-8">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="query" className="block text-white font-medium mb-2">
                What would you like to analyze?
              </label>
              <textarea
                id="query"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Enter your query here... (e.g., 'Analyze the impact of AI on software development')"
                className="w-full h-32 bg-neutral-800 border border-neutral-700 rounded-lg px-4 py-3 text-white placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Mode Selection */}
            <div className="flex gap-4">
              <button
                type="button"
                onClick={() => setMode('single')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg border transition-all",
                  mode === 'single'
                    ? "bg-blue-500/10 text-blue-400 border-blue-500/20"
                    : "bg-neutral-800 text-neutral-400 border-neutral-700 hover:bg-neutral-700"
                )}
                disabled={isLoading}
              >
                <Zap className="w-4 h-4" />
                Single Agent
              </button>
              <button
                type="button"
                onClick={() => setMode('heavy')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg border transition-all",
                  mode === 'heavy'
                    ? "bg-blue-500/10 text-blue-400 border-blue-500/20"
                    : "bg-neutral-800 text-neutral-400 border-neutral-700 hover:bg-neutral-700"
                )}
                disabled={isLoading}
              >
                <Users className="w-4 h-4" />
                Heavy Mode (Multi-Agent)
              </button>
            </div>

            <button
              type="submit"
              disabled={!query.trim() || isLoading}
              className="bg-blue-gradient-hover text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
              {isLoading ? 'Processing...' : 'Analyze'}
            </button>
          </form>
        </div>

        {/* Progress Display */}
        {(currentTask || progress) && (
          <div className="bg-neutral-900 border border-neutral-800 rounded-lg p-6 mb-8">
            <div className="flex items-center gap-3 mb-4">
              {getStatusIcon(progress?.status || currentTask?.status || 'pending')}
              <h3 className="text-lg font-medium text-white">
                {mode === 'heavy' ? 'Heavy Mode Analysis' : 'Single Agent Analysis'}
              </h3>
            </div>

            {progress?.progress?.message && (
              <div className="mb-4">
                <p className="text-neutral-300">{progress.progress.message}</p>
                {progress.progress.stage && (
                  <div className="mt-2">
                    <div className="flex justify-between text-sm text-neutral-400 mb-1">
                      <span>Stage: {progress.progress.stage}</span>
                    </div>
                    <div className="w-full bg-neutral-800 rounded-full h-2">
                      <div
                        className="bg-blue-gradient h-2 rounded-full transition-all duration-300"
                        style={{
                          width: progress.progress.stage === 'completed' ? '100%' :
                                 progress.progress.stage === 'synthesizing' ? '80%' :
                                 progress.progress.stage === 'executing' ? '60%' :
                                 progress.progress.stage === 'decomposing' ? '40%' :
                                 progress.progress.stage === 'processing' ? '50%' :
                                 progress.progress.stage === 'initializing' ? '20%' : '10%'
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            {progress?.progress?.questions && (
              <div className="mb-4">
                <h4 className="text-white font-medium mb-2">Generated Questions:</h4>
                <ul className="space-y-2">
                  {progress.progress.questions.map((question, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-blue-400 font-medium">{index + 1}.</span>
                      <span className="text-neutral-300">{question}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {currentTask?.result && (
              <div className="mt-4">
                <h4 className="text-white font-medium mb-2">Result:</h4>
                <div className="bg-neutral-800 border border-neutral-700 rounded-lg p-4">
                  <pre className="text-neutral-300 whitespace-pre-wrap font-mono text-sm">
                    {currentTask.result}
                  </pre>
                </div>
              </div>
            )}

            {currentTask?.error && (
              <div className="mt-4">
                <h4 className="text-red-400 font-medium mb-2">Error:</h4>
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                  <p className="text-red-300">{currentTask.error}</p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Recent Tasks */}
        {recentTasks.length > 0 && (
          <div className="bg-neutral-900 border border-neutral-800 rounded-lg p-6">
            <h3 className="text-lg font-medium text-white mb-4">Recent Tasks</h3>
            <div className="space-y-3">
              {recentTasks.map((task) => (
                <div key={task.task_id} className="bg-neutral-800 border border-neutral-700 rounded-lg p-4 card-hover-blue">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getStatusIcon(task.status)}
                        <span className="text-sm text-neutral-400">
                          {task.mode === 'heavy' ? 'Heavy Mode' : 'Single Agent'}
                        </span>
                        <span className="text-sm text-neutral-500">
                          {new Date(task.created_at).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-white text-sm mb-2 line-clamp-2">{task.query}</p>
                      {task.result && (
                        <p className="text-neutral-400 text-xs line-clamp-1">
                          {task.result.substring(0, 100)}...
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
