#!/usr/bin/env python3
"""
Playwright MCP Integration for Make It Heavy
Provides web browsing and scraping capabilities
"""

import asyncio
import json
import os
from typing import Dict, Any, Optional
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

class PlaywrightMCPClient:
    """Client for Playwright MCP server with enhanced functionality"""
    
    def __init__(self):
        self.session = None
        self.read_stream = None
        self.write_stream = None
        self.connected = False
        
    async def connect(self):
        """Connect to Playwright MCP server"""
        if self.connected:
            return
            
        print("🌐 Connecting to Playwright MCP server...")
        
        server_params = StdioServerParameters(
            command="npx",
            args=["-y", "@playwright/mcp@latest"],
            env=os.environ.copy()
        )
        
        try:
            # Create connection
            self.read_stream, self.write_stream = await stdio_client(server_params).__aenter__()
            self.session = await ClientSession(self.read_stream, self.write_stream).__aenter__()
            
            # Initialize
            await self.session.initialize()
            self.connected = True
            print("✅ Playwright MCP server connected!")
            
        except Exception as e:
            print(f"❌ Failed to connect to Playwright: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from Playwright MCP server"""
        if not self.connected:
            return
            
        try:
            if self.session:
                await self.session.__aexit__(None, None, None)
            self.connected = False
            print("🔌 Disconnected from Playwright MCP server")
        except Exception as e:
            print(f"⚠️ Error during disconnect: {e}")
    
    async def get_available_tools(self):
        """Get list of available Playwright tools"""
        if not self.connected:
            await self.connect()
            
        tools = await self.session.list_tools()
        return {tool.name: tool.description for tool in tools.tools}
    
    async def navigate_to_url(self, url: str) -> str:
        """Navigate to a URL"""
        if not self.connected:
            await self.connect()
            
        result = await self.session.call_tool("browser_navigate", arguments={"url": url})
        return self._extract_text_result(result)
    
    async def take_screenshot(self, filename: Optional[str] = None) -> str:
        """Take a screenshot of the current page"""
        if not self.connected:
            await self.connect()
            
        args = {}
        if filename:
            args["filename"] = filename
            
        result = await self.session.call_tool("browser_take_screenshot", arguments=args)
        return self._extract_text_result(result)
    
    async def get_page_snapshot(self) -> str:
        """Get accessibility snapshot of the current page"""
        if not self.connected:
            await self.connect()
            
        result = await self.session.call_tool("browser_snapshot", arguments={})
        return self._extract_text_result(result)
    
    async def click_element(self, selector: str) -> str:
        """Click on an element"""
        if not self.connected:
            await self.connect()
            
        result = await self.session.call_tool("browser_click", arguments={"selector": selector})
        return self._extract_text_result(result)
    
    async def type_text(self, selector: str, text: str) -> str:
        """Type text into an element"""
        if not self.connected:
            await self.connect()
            
        result = await self.session.call_tool("browser_type", arguments={"selector": selector, "text": text})
        return self._extract_text_result(result)
    
    async def evaluate_javascript(self, expression: str) -> str:
        """Evaluate JavaScript on the page"""
        if not self.connected:
            await self.connect()
            
        result = await self.session.call_tool("browser_evaluate", arguments={"expression": expression})
        return self._extract_text_result(result)
    
    async def wait_for_text(self, text: str, timeout: int = 5000) -> str:
        """Wait for text to appear on the page"""
        if not self.connected:
            await self.connect()
            
        result = await self.session.call_tool("browser_wait_for", arguments={"text": text, "timeout": timeout})
        return self._extract_text_result(result)
    
    async def get_network_requests(self) -> str:
        """Get all network requests since loading the page"""
        if not self.connected:
            await self.connect()
            
        result = await self.session.call_tool("browser_network_requests", arguments={})
        return self._extract_text_result(result)
    
    async def get_console_messages(self) -> str:
        """Get all console messages"""
        if not self.connected:
            await self.connect()
            
        result = await self.session.call_tool("browser_console_messages", arguments={})
        return self._extract_text_result(result)
    
    def _extract_text_result(self, result) -> str:
        """Extract text content from MCP result"""
        if result.content and len(result.content) > 0:
            content_block = result.content[0]
            if hasattr(content_block, 'text'):
                return content_block.text
        return "No text content returned"

async def demo_playwright_capabilities():
    """Demonstrate Playwright MCP capabilities"""
    client = PlaywrightMCPClient()
    
    try:
        # Connect to Playwright
        await client.connect()
        
        # Show available tools
        print("\n🔧 Available Playwright tools:")
        tools = await client.get_available_tools()
        for name, description in list(tools.items())[:10]:  # Show first 10
            print(f"  - {name}: {description}")
        print(f"  ... and {len(tools) - 10} more tools")
        
        # Test web navigation
        print("\n🌐 Testing web navigation...")
        result = await client.navigate_to_url("https://httpbin.org/html")
        print(f"Navigation result: {result[:100]}...")
        
        # Test page snapshot
        print("\n📸 Taking page snapshot...")
        snapshot = await client.get_page_snapshot()
        print(f"Page snapshot: {snapshot[:200]}...")
        
        # Test JavaScript evaluation
        print("\n🔧 Testing JavaScript evaluation...")
        js_result = await client.evaluate_javascript("document.title")
        print(f"Page title: {js_result}")
        
        # Test screenshot
        print("\n📷 Taking screenshot...")
        screenshot_result = await client.take_screenshot()
        print(f"Screenshot result: {screenshot_result}")
        
        print("\n🎉 Playwright demonstration completed!")
        
    except Exception as e:
        print(f"❌ Playwright demo failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.disconnect()

async def demo_web_scraping_workflow():
    """Demonstrate a complete web scraping workflow"""
    client = PlaywrightMCPClient()
    
    try:
        await client.connect()
        
        print("\n🕷️ Web Scraping Workflow Demo")
        print("=" * 40)
        
        # Step 1: Navigate to a test page
        print("\n1️⃣ Navigating to test page...")
        await client.navigate_to_url("https://httpbin.org/forms/post")
        
        # Step 2: Get page structure
        print("\n2️⃣ Analyzing page structure...")
        snapshot = await client.get_page_snapshot()
        print(f"Page structure: {snapshot[:300]}...")
        
        # Step 3: Fill out a form (if available)
        print("\n3️⃣ Interacting with page elements...")
        try:
            # Try to fill a form field
            await client.type_text("input[name='custname']", "Test User")
            print("✅ Form field filled successfully")
        except Exception as e:
            print(f"⚠️ Form interaction not available: {e}")
        
        # Step 4: Extract data with JavaScript
        print("\n4️⃣ Extracting data with JavaScript...")
        try:
            form_data = await client.evaluate_javascript("""
                Array.from(document.querySelectorAll('input')).map(input => ({
                    name: input.name,
                    type: input.type,
                    value: input.value
                }))
            """)
            print(f"Form data extracted: {form_data}")
        except Exception as e:
            print(f"⚠️ Data extraction failed: {e}")
        
        # Step 5: Take final screenshot
        print("\n5️⃣ Taking final screenshot...")
        screenshot = await client.take_screenshot("workflow_result.png")
        print(f"Final screenshot: {screenshot}")
        
        print("\n🎉 Web scraping workflow completed!")
        
    except Exception as e:
        print(f"❌ Web scraping workflow failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.disconnect()

if __name__ == "__main__":
    print("🚀 Playwright MCP Integration Demo")
    print("=" * 50)
    
    # Run basic capabilities demo
    asyncio.run(demo_playwright_capabilities())
    
    # Run web scraping workflow demo
    asyncio.run(demo_web_scraping_workflow())
