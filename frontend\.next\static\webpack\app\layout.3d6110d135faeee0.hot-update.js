"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9af35c8d6fc3\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOWFmMzVjOGQ2ZmMzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/**\n * API Client for AG3NT X Backend\n * Handles communication with the FastAPI backend\n */ const API_BASE_URL = \"http://localhost:8000\" || 0;\nclass APIClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers\n            },\n            ...options\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"API request failed: \".concat(endpoint), error);\n            throw error;\n        }\n    }\n    // Health check\n    async getHealth() {\n        return this.request('/health');\n    }\n    // Submit a query to the agent system\n    async submitQuery(query) {\n        return this.request('/query', {\n            method: 'POST',\n            body: JSON.stringify(query)\n        });\n    }\n    // Get all tasks\n    async getTasks() {\n        return this.request('/tasks');\n    }\n    // Get specific task by ID\n    async getTask(taskId) {\n        return this.request(\"/task/\".concat(taskId));\n    }\n    // Get MCP information\n    async getMCPInfo() {\n        return this.request('/mcp-info');\n    }\n    // Stream task progress (Server-Sent Events)\n    async streamTaskProgress(taskId, onProgress, onError, onComplete) {\n        const url = \"\".concat(this.baseURL, \"/task/\").concat(taskId, \"/stream\");\n        try {\n            const eventSource = new EventSource(url);\n            eventSource.onmessage = (event)=>{\n                try {\n                    const update = JSON.parse(event.data);\n                    onProgress(update);\n                    // Close connection if task is complete\n                    if (update.status === 'completed' || update.status === 'failed') {\n                        eventSource.close();\n                        onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                    }\n                } catch (error) {\n                    console.error('Failed to parse progress update:', error);\n                    onError === null || onError === void 0 ? void 0 : onError(error);\n                }\n            };\n            eventSource.onerror = (error)=>{\n                console.error('EventSource error:', error);\n                eventSource.close();\n                onError === null || onError === void 0 ? void 0 : onError(new Error('Connection to progress stream failed'));\n            };\n        } catch (error) {\n            console.error('Failed to start progress stream:', error);\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    }\n    // Submit query and stream response\n    async submitQueryWithStream(query, onProgress, onError, onComplete) {\n        try {\n            // Submit the query\n            const task = await this.submitQuery(query);\n            // Start streaming progress\n            this.streamTaskProgress(task.task_id, onProgress, onError, onComplete);\n            return task;\n        } catch (error) {\n            console.error('Failed to submit query with stream:', error);\n            onError === null || onError === void 0 ? void 0 : onError(error);\n            throw error;\n        }\n    }\n    // File upload helper\n    async uploadFile(file) {\n        const formData = new FormData();\n        formData.append('file', file);\n        const response = await fetch(\"\".concat(this.baseURL, \"/upload\"), {\n            method: 'POST',\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error(\"Upload failed: \".concat(response.status));\n        }\n        return await response.json();\n    }\n    // Batch file upload\n    async uploadFiles(files) {\n        const uploads = files.map((file)=>this.uploadFile(file));\n        return Promise.all(uploads);\n    }\n    constructor(baseURL = API_BASE_URL){\n        this.baseURL = baseURL;\n    }\n}\n// Create singleton instance\nconst apiClient = new APIClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});