"""
Dynamic Tool Router - LangGraph Style Tool Selection
Intelligently selects relevant tools based on user query to minimize context usage.
"""

import re
from typing import List, Dict, Any
from tools import discover_tools

class ToolRouter:
    def __init__(self, config=None):
        # Load all available tools once
        import yaml
        import os
        if config is None:
            # Try to find config.yaml in current directory or parent
            config_paths = ["config.yaml", "../config.yaml", "../../config.yaml"]
            config_path = None
            for path in config_paths:
                if os.path.exists(path):
                    config_path = path
                    break

            if config_path:
                with open(config_path, "r") as f:
                    config = yaml.safe_load(f)
            else:
                # Fallback minimal config
                config = {
                    'openrouter': {'api_key': 'dummy', 'base_url': 'dummy', 'model': 'dummy'},
                    'system_prompt': 'You are a helpful assistant.'
                }

        self.all_tools = discover_tools(config, silent=True)
        
        # Create tool categories for fast routing
        self.tool_categories = {
            'file_operations': ['read_file', 'write_file', 'copy', 'move', 'delete', 'file_info'],
            'directory_operations': ['create_directory', 'list_directory', 'file_info'],
            'web_search': ['search_web'],
            'calculations': ['calculate'],
            'terminal_operations': ['run_terminal_command'],
            'task_management': ['mark_task_complete']
        }
        
        # Keywords that trigger specific tools
        self.tool_keywords = {
            'read_file': ['read', 'open', 'view', 'show', 'display', 'content', 'see'],
            'write_file': ['write', 'create', 'save', 'make', 'generate', 'build'],
            'copy': ['copy', 'duplicate', 'backup', 'clone'],
            'move': ['move', 'rename', 'relocate', 'transfer'],
            'delete': ['delete', 'remove', 'erase', 'clean', 'clear'],
            'create_directory': ['folder', 'directory', 'mkdir', 'create dir'],
            'list_directory': ['list', 'ls', 'browse', 'explore', 'contents'],
            'file_info': ['info', 'details', 'properties', 'stat', 'metadata'],
            'search_web': ['search', 'google', 'find online', 'web', 'internet'],
            'calculate': ['calculate', 'math', 'compute', 'add', 'subtract', 'multiply', 'divide'],
            'run_terminal_command': ['run', 'execute', 'command', 'terminal', 'shell', 'cmd', 'npm', 'node', 'python', 'start', 'serve', 'build', 'compile', 'install'],
            'mark_task_complete': ['done', 'complete', 'finished', 'task complete']
        }
    
    def route_tools(self, user_query: str, max_tools: int = 3) -> List[Dict[str, Any]]:
        """
        Route user query to most relevant tools using keyword matching.
        Returns list of tool definitions (not just names).
        """
        query_lower = user_query.lower()
        tool_scores = {}
        
        # Score tools based on keyword matches
        for tool_name, keywords in self.tool_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in query_lower:
                    # Exact word match gets higher score
                    if re.search(r'\b' + re.escape(keyword) + r'\b', query_lower):
                        score += 2
                    else:
                        score += 1
            
            if score > 0:
                tool_scores[tool_name] = score
        
        # Always include task completion tool
        if 'mark_task_complete' not in tool_scores:
            tool_scores['mark_task_complete'] = 1
        
        # Sort by score and take top tools
        sorted_tools = sorted(tool_scores.items(), key=lambda x: x[1], reverse=True)
        selected_tool_names = [name for name, score in sorted_tools[:max_tools]]
        
        # Convert tool names to full tool definitions
        selected_tools = []
        for tool_name in selected_tool_names:
            if tool_name in self.all_tools:
                tool_instance = self.all_tools[tool_name]
                tool_def = tool_instance.to_openrouter_schema()
                selected_tools.append(tool_def)
        
        # Fallback: if no tools matched, provide basic file operations
        if not selected_tools or len(selected_tools) == 1:  # Only task_complete
            fallback_tools = ['read_file', 'write_file', 'list_directory', 'mark_task_complete']
            selected_tools = []
            for tool_name in fallback_tools:
                if tool_name in self.all_tools:
                    tool_instance = self.all_tools[tool_name]
                    tool_def = tool_instance.to_openrouter_schema()
                    selected_tools.append(tool_def)
        
        return selected_tools
    
    def get_tool_mapping(self, selected_tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create tool mapping for the selected tools only.
        """
        tool_mapping = {}
        for tool_def in selected_tools:
            tool_name = tool_def["function"]["name"]
            if tool_name in self.all_tools:
                tool_instance = self.all_tools[tool_name]
                tool_mapping[tool_name] = tool_instance.execute

        return tool_mapping
    
    def analyze_query(self, user_query: str) -> Dict[str, Any]:
        """
        Analyze user query and return routing information.
        """
        query_lower = user_query.lower()
        
        analysis = {
            'intent': 'general',
            'file_operations': False,
            'web_search': False,
            'calculations': False,
            'complexity': 'simple'
        }
        
        # Detect intent patterns
        if any(word in query_lower for word in ['file', 'folder', 'directory', 'create', 'write', 'read']):
            analysis['intent'] = 'file_management'
            analysis['file_operations'] = True
        
        if any(word in query_lower for word in ['search', 'find', 'google', 'web', 'internet']):
            analysis['intent'] = 'web_search'
            analysis['web_search'] = True
        
        if any(word in query_lower for word in ['calculate', 'math', 'compute', '+', '-', '*', '/']):
            analysis['intent'] = 'calculation'
            analysis['calculations'] = True
        
        # Detect complexity
        if len(user_query.split()) > 20 or any(word in query_lower for word in ['and', 'then', 'also', 'multiple']):
            analysis['complexity'] = 'complex'
        
        return analysis

# Global router instance
tool_router = ToolRouter()
