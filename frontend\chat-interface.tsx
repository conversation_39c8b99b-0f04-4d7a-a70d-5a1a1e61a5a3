"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Copy, ThumbsUp, ThumbsDown, Paperclip, X } from "lucide-react"

interface Message {
  id: string
  role: "user" | "agent"
  content: string
  timestamp: Date
  files?: File[]
}

interface UploadedFile {
  file: File
  id: string
}

export default function ChatInterface() {
  const [input, setInput] = useState("")
  const [messages, setMessages] = useState<Message[]>([])
  const [isTyping, setIsTyping] = useState(false)
  const [isChatMode, setIsChatMode] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector("[data-radix-scroll-area-viewport]")
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }, [input])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      const newFiles = Array.from(files).map((file) => ({
        file,
        id: Math.random().toString(36).substr(2, 9),
      }))
      setUploadedFiles((prev) => [...prev, ...newFiles])
    }
    // Reset the input so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const simulateAgentResponse = async (userMessage: string) => {
    setIsTyping(true)

    // Simulate typing delay
    await new Promise((resolve) => setTimeout(resolve, 1000 + Math.random() * 2000))

    const responses = [
      "I understand your question. Let me help you with that.",
      "That's an interesting point. Here's what I think about it...",
      "I can definitely assist you with that. Let me provide you with some information.",
      "Great question! Here's a comprehensive answer for you.",
      "I'm here to help. Based on what you've asked, I'd suggest...",
    ]

    const randomResponse = responses[Math.floor(Math.random() * responses.length)]

    const agentMessage: Message = {
      id: Date.now().toString() + "-agent",
      role: "agent",
      content: randomResponse,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, agentMessage])
    setIsTyping(false)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() && uploadedFiles.length === 0) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input.trim(),
      timestamp: new Date(),
      files: uploadedFiles.map((f) => f.file),
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setUploadedFiles([])

    if (!isChatMode) {
      setIsChatMode(true)
    }

    // Simulate agent response
    await simulateAgentResponse(input.trim())
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const ChatBubble = ({ message }: { message: Message }) => {
    const isUser = message.role === "user"

    return (
      <div className={`flex gap-3 mb-6 ${isUser ? "justify-end" : "justify-start"}`}>
        {!isUser && (
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1">
            <span className="text-xs font-semibold text-white">AI</span>
          </div>
        )}

        <div className={`max-w-[70%] ${isUser ? "order-first" : ""}`}>
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xs font-medium text-neutral-500">{isUser ? "You" : "AG3NT X"}</span>
            <span className="text-xs text-neutral-500">{formatTime(message.timestamp)}</span>
          </div>

          <div
            className={`
            px-4 py-3 rounded-2xl text-sm leading-relaxed whitespace-pre-wrap
            ${isUser ? "bg-blue-500 text-white ml-auto rounded-br-md" : "bg-neutral-800 text-neutral-100 rounded-bl-md"}
          `}
          >
            {message.content}
          </div>

          {/* File attachments */}
          {message.files && message.files.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.files.map((file, index) => (
                <div
                  key={index}
                  className={`
                  flex items-center gap-2 px-3 py-2 rounded-lg text-xs
                  ${isUser ? "bg-blue-700/50 text-blue-100" : "bg-neutral-700/50 text-neutral-300"}
                `}
                >
                  <Paperclip className="h-3 w-3" />
                  <span className="truncate">{file.name}</span>
                  <span className="text-neutral-500">({formatFileSize(file.size)})</span>
                </div>
              ))}
            </div>
          )}

          {!isUser && (
            <div className="flex items-center gap-1 mt-2">
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <Copy className="h-3 w-3 text-gray-500" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <ThumbsUp className="h-3 w-3 text-gray-500" />
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-neutral-800">
                <ThumbsDown className="h-3 w-3 text-gray-500" />
              </Button>
            </div>
          )}
        </div>

        {isUser && (
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center flex-shrink-0 mt-1">
            <span className="text-xs font-semibold text-white">U</span>
          </div>
        )}
      </div>
    )
  }

  const TypingIndicator = () => (
    <div className="flex gap-3 mb-6">
      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1">
        <span className="text-xs font-semibold text-white">AI</span>
      </div>
      <div className="bg-gray-800 px-4 py-3 rounded-2xl rounded-bl-md">
        <div className="flex gap-1">
          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
        </div>
      </div>
    </div>
  )

  const FileUploadArea = () => (
    <>
      {uploadedFiles.length > 0 && (
        <div className="mb-3 space-y-2">
          {uploadedFiles.map((uploadedFile) => (
            <div
              key={uploadedFile.id}
              className="flex items-center gap-2 px-3 py-2 bg-neutral-800/50 border border-neutral-700 rounded-lg"
            >
              <Paperclip className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-300 truncate flex-1">{uploadedFile.file.name}</span>
              <span className="text-xs text-gray-500">({formatFileSize(uploadedFile.file.size)})</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFile(uploadedFile.id)}
                className="h-6 w-6 p-0 hover:bg-gray-700"
              >
                <X className="h-3 w-3 text-gray-400" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </>
  )

  if (!isChatMode) {
    return (
      <div className="flex flex-col h-full">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-2xl mx-auto px-8">
            <h1 className="text-5xl font-medium text-white mb-12 leading-tight">How can I assist you?</h1>

            <form onSubmit={handleSubmit} className="relative">
              <FileUploadArea />
              <div className="textarea-container relative">
                <Textarea
                  ref={textareaRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Start a new conversation..."
                  className="textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar"
                  rows={1}
                />
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                  accept="*/*"
                />
                <Button
                  type="button"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  className="absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10"
                  title="Upload files"
                >
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  className="absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10"
                  disabled={!input.trim() && uploadedFiles.length === 0}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <div className="h-16 px-4 border-b border-neutral-800 flex items-center">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
            <span className="text-xs font-semibold text-white">AI</span>
          </div>
          <div>
            <h2 className="font-medium text-white">AG3NT X</h2>
            <p className="text-xs text-neutral-500">Online</p>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
        <div className="space-y-0">
          {messages.map((message) => (
            <ChatBubble key={message.id} message={message} />
          ))}
          {isTyping && <TypingIndicator />}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-neutral-800">
        <form onSubmit={handleSubmit} className="relative">
          <FileUploadArea />
          <div className="textarea-container relative">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar"
              rows={1}
              disabled={isTyping}
            />
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileUpload}
              className="hidden"
              accept="*/*"
            />
            <Button
              type="button"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10"
              title="Upload files"
              disabled={isTyping}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button
              type="submit"
              size="sm"
              className="absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10"
              disabled={(!input.trim() && uploadedFiles.length === 0) || isTyping}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
