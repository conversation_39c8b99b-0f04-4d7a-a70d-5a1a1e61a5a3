from .base_tool import BaseTool
import subprocess
import os
import threading
import time
from typing import Dict, Any, Optional

class TerminalTool(BaseTool):
    def __init__(self, config: dict):
        self.config = config
        self.running_processes = {}  # Track background processes
    
    @property
    def name(self) -> str:
        return "run_terminal_command"
    
    @property
    def description(self) -> str:
        return "Execute terminal/shell commands. Can run commands in background or wait for completion."
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "command": {
                    "type": "string",
                    "description": "The terminal command to execute"
                },
                "working_directory": {
                    "type": "string",
                    "description": "Working directory for the command (default: current directory)",
                    "default": "."
                },
                "background": {
                    "type": "boolean",
                    "description": "Whether to run command in background (default: False)",
                    "default": False
                },
                "timeout": {
                    "type": "number",
                    "description": "Timeout in seconds for command execution (default: 30)",
                    "default": 30
                },
                "shell": {
                    "type": "boolean",
                    "description": "Whether to run command through shell (default: True)",
                    "default": True
                }
            },
            "required": ["command"]
        }
    
    def execute(self, command: str, working_directory: str = ".", background: bool = False, 
                timeout: float = 30, shell: bool = True) -> Dict[str, Any]:
        try:
            # Get absolute working directory
            abs_working_dir = os.path.abspath(working_directory)
            
            # Check if working directory exists
            if not os.path.exists(abs_working_dir):
                return {"error": f"Working directory does not exist: {working_directory}"}
            
            if not os.path.isdir(abs_working_dir):
                return {"error": f"Working directory is not a directory: {working_directory}"}
            
            # Security check - prevent dangerous commands
            dangerous_commands = [
                'rm -rf /', 'del /f /s /q C:\\', 'format', 'fdisk', 'mkfs',
                'dd if=', 'shutdown', 'reboot', 'halt', 'poweroff'
            ]
            
            command_lower = command.lower()
            for dangerous in dangerous_commands:
                if dangerous in command_lower:
                    return {"error": f"Dangerous command blocked: {command}"}
            
            if background:
                return self._run_background_command(command, abs_working_dir, shell)
            else:
                return self._run_foreground_command(command, abs_working_dir, timeout, shell)
        
        except Exception as e:
            return {"error": f"Failed to execute command: {str(e)}"}
    
    def _run_foreground_command(self, command: str, working_dir: str, timeout: float, shell: bool) -> Dict[str, Any]:
        """Run command and wait for completion"""
        try:
            # Determine shell based on OS
            if os.name == 'nt':  # Windows
                shell_cmd = ['cmd', '/c', command] if not shell else command
            else:  # Unix/Linux/Mac
                shell_cmd = ['/bin/bash', '-c', command] if not shell else command
            
            # Execute command
            result = subprocess.run(
                shell_cmd if not shell else command,
                cwd=working_dir,
                capture_output=True,
                text=True,
                timeout=timeout,
                shell=shell
            )
            
            return {
                "success": True,
                "command": command,
                "working_directory": working_dir,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "execution_time": "completed",
                "background": False
            }
        
        except subprocess.TimeoutExpired:
            return {
                "error": f"Command timed out after {timeout} seconds",
                "command": command,
                "timeout": timeout
            }
        except subprocess.CalledProcessError as e:
            return {
                "error": f"Command failed with return code {e.returncode}",
                "command": command,
                "return_code": e.returncode,
                "stdout": e.stdout,
                "stderr": e.stderr
            }
        except Exception as e:
            return {"error": f"Command execution failed: {str(e)}"}
    
    def _run_background_command(self, command: str, working_dir: str, shell: bool) -> Dict[str, Any]:
        """Run command in background and return immediately"""
        try:
            # Generate unique process ID
            process_id = f"proc_{int(time.time() * 1000)}"
            
            # Determine shell based on OS
            if os.name == 'nt':  # Windows
                shell_cmd = ['cmd', '/c', command] if not shell else command
            else:  # Unix/Linux/Mac
                shell_cmd = ['/bin/bash', '-c', command] if not shell else command
            
            # Start process
            process = subprocess.Popen(
                shell_cmd if not shell else command,
                cwd=working_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                shell=shell
            )
            
            # Store process info
            self.running_processes[process_id] = {
                "process": process,
                "command": command,
                "working_dir": working_dir,
                "start_time": time.time()
            }
            
            return {
                "success": True,
                "command": command,
                "working_directory": working_dir,
                "process_id": process_id,
                "pid": process.pid,
                "background": True,
                "message": f"Command started in background with process ID: {process_id}"
            }
        
        except Exception as e:
            return {"error": f"Failed to start background command: {str(e)}"}
    
    def get_process_status(self, process_id: str) -> Dict[str, Any]:
        """Get status of a background process"""
        if process_id not in self.running_processes:
            return {"error": f"Process ID not found: {process_id}"}
        
        process_info = self.running_processes[process_id]
        process = process_info["process"]
        
        # Check if process is still running
        return_code = process.poll()
        
        if return_code is None:
            # Still running
            return {
                "process_id": process_id,
                "status": "running",
                "pid": process.pid,
                "command": process_info["command"],
                "runtime": time.time() - process_info["start_time"]
            }
        else:
            # Process completed
            stdout, stderr = process.communicate()
            
            # Remove from tracking
            del self.running_processes[process_id]
            
            return {
                "process_id": process_id,
                "status": "completed",
                "return_code": return_code,
                "command": process_info["command"],
                "stdout": stdout,
                "stderr": stderr,
                "runtime": time.time() - process_info["start_time"]
            }
    
    def kill_process(self, process_id: str) -> Dict[str, Any]:
        """Kill a background process"""
        if process_id not in self.running_processes:
            return {"error": f"Process ID not found: {process_id}"}
        
        try:
            process_info = self.running_processes[process_id]
            process = process_info["process"]
            
            process.terminate()
            time.sleep(1)  # Give it a moment to terminate gracefully
            
            if process.poll() is None:
                process.kill()  # Force kill if still running
            
            # Remove from tracking
            del self.running_processes[process_id]
            
            return {
                "success": True,
                "process_id": process_id,
                "message": f"Process {process_id} terminated"
            }
        
        except Exception as e:
            return {"error": f"Failed to kill process: {str(e)}"}
    
    def list_processes(self) -> Dict[str, Any]:
        """List all running background processes"""
        processes = []
        
        for process_id, process_info in self.running_processes.items():
            process = process_info["process"]
            return_code = process.poll()
            
            processes.append({
                "process_id": process_id,
                "pid": process.pid,
                "command": process_info["command"],
                "status": "running" if return_code is None else "completed",
                "runtime": time.time() - process_info["start_time"]
            })
        
        return {
            "success": True,
            "processes": processes,
            "total_processes": len(processes)
        }
