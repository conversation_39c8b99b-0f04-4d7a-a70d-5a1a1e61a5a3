#!/usr/bin/env python3
"""
Simple test script for the Make It Heavy API
"""
import requests
import time
import json

API_BASE = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the server is running on port 8000")
        return False

def test_single_agent_query():
    """Test a simple single agent query"""
    print("\nTesting single agent query...")

    query_data = {
        "query": "What is artificial intelligence?",
        "mode": "single"
    }

    try:
        # Create query
        response = requests.post(f"{API_BASE}/query", json=query_data)
        if response.status_code != 200:
            print(f"❌ Failed to create query: {response.status_code}")
            return False

        task = response.json()
        task_id = task["task_id"]
        print(f"✅ Query created with task ID: {task_id}")

        # Poll for completion (max 30 seconds)
        for i in range(30):
            response = requests.get(f"{API_BASE}/task/{task_id}")
            if response.status_code != 200:
                print(f"❌ Failed to get task status: {response.status_code}")
                return False

            task_status = response.json()
            status = task_status["status"]
            print(f"Status: {status}")

            if status == "completed":
                print("✅ Single agent query completed successfully")
                print(f"Result preview: {task_status['result'][:100]}...")
                return True
            elif status == "failed":
                print(f"❌ Query failed: {task_status.get('error', 'Unknown error')}")
                return False

            time.sleep(1)

        print("❌ Query timed out")
        return False

    except Exception as e:
        print(f"❌ Error during single agent test: {e}")
        return False

def test_api_documentation():
    """Test that API documentation is accessible"""
    print("\nTesting API documentation...")
    try:
        response = requests.get(f"{API_BASE}/docs")
        if response.status_code == 200:
            print("✅ API documentation is accessible at /docs")
            return True
        else:
            print(f"❌ API documentation failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error accessing documentation: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Make It Heavy API Tests\n")

    tests = [
        test_health_check,
        test_api_documentation,
        test_single_agent_query,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print("-" * 50)

    print(f"\n📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The API is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

    print(f"\n🌐 API is running at: {API_BASE}")
    print(f"📚 Documentation: {API_BASE}/docs")
    print(f"🖥️  Frontend should be at: http://localhost:3000")

if __name__ == "__main__":
    main()