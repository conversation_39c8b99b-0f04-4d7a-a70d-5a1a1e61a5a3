"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"70a24a4ef4d8\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzBhMjRhNGVmNGQ4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./mcp-library-tab.tsx":
/*!*****************************!*\
  !*** ./mcp-library-tab.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpLibraryTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,ExternalLink,FileText,Globe,Package,Plus,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst getInitialMcpServers = ()=>[\n        {\n            id: \"ag3nt-x\",\n            name: \"AG3NT X Agent\",\n            description: \"Our comprehensive agent system with file operations, terminal commands, web search, calculations, and system management capabilities.\",\n            author: \"AG3NT X\",\n            version: \"1.0.0\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 59,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"Calculations\"\n            ],\n            status: \"installed\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"playwright\",\n            name: \"Playwright MCP\",\n            description: \"Professional web automation and scraping with Playwright. Navigate websites, take screenshots, extract content, and automate browser interactions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 75,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 61200,\n            tags: [\n                \"Playwright\",\n                \"Web Automation\",\n                \"Screenshots\",\n                \"Browser\"\n            ],\n            status: \"installed\",\n            gradient: \"from-green-500 to-emerald-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"filesystem\",\n            name: \"Filesystem MCP\",\n            description: \"Secure file operations with configurable access controls. Read, write, create, delete, and manage files and directories with advanced permissions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 45300,\n            tags: [\n                \"Files\",\n                \"Directories\",\n                \"Security\",\n                \"Permissions\"\n            ],\n            status: \"available\",\n            gradient: \"from-purple-500 to-pink-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"git\",\n            name: \"Git MCP\",\n            description: \"Tools to read, search, and manipulate Git repositories. Manage commits, branches, and repository operations through MCP.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 107,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 32100,\n            tags: [\n                \"Git\",\n                \"Version Control\",\n                \"Repository\",\n                \"Commits\"\n            ],\n            status: \"available\",\n            gradient: \"from-orange-500 to-red-500\",\n            lastUpdated: \"3 days ago\"\n        },\n        {\n            id: \"memory\",\n            name: \"Memory MCP\",\n            description: \"Knowledge graph-based persistent memory system. Store and retrieve information across conversations with intelligent context management.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"AI\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 123,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.7,\n            downloads: 28900,\n            tags: [\n                \"Memory\",\n                \"Knowledge Graph\",\n                \"Context\",\n                \"AI\"\n            ],\n            status: \"available\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"fetch\",\n            name: \"Fetch MCP\",\n            description: \"Web content fetching and conversion for efficient LLM usage. Convert web pages to markdown and extract structured content.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 139,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.6,\n            downloads: 19800,\n            tags: [\n                \"Web Fetch\",\n                \"Markdown\",\n                \"Content\",\n                \"Conversion\"\n            ],\n            status: \"available\",\n            gradient: \"from-indigo-500 to-purple-500\",\n            lastUpdated: \"2 days ago\"\n        },\n        {\n            id: \"time\",\n            name: \"Time MCP\",\n            description: \"Time and timezone conversion capabilities. Handle dates, times, and timezone operations with precision.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Utility\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 155,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.5,\n            downloads: 15600,\n            tags: [\n                \"Time\",\n                \"Timezone\",\n                \"Date\",\n                \"Conversion\"\n            ],\n            status: \"available\",\n            gradient: \"from-cyan-500 to-blue-500\",\n            lastUpdated: \"1 week ago\"\n        }\n    ];\nconst categories = [\n    \"All\",\n    \"Development\",\n    \"Web\",\n    \"AI\",\n    \"Utility\",\n    \"Database\",\n    \"Communication\"\n];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"bg-green-500\";\n        case \"updating\":\n            return \"bg-yellow-500\";\n        case \"available\":\n            return \"bg-gray-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"Installed\";\n        case \"updating\":\n            return \"Updating\";\n        case \"available\":\n            return \"Available\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction McpLibraryTab() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [mcpServers, setMcpServers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialMcpServers());\n    const [mcpInfo, setMcpInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [addServerDialog, setAddServerDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newServer, setNewServer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        command: \"\",\n        args: \"\",\n        description: \"\",\n        category: \"Development\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"McpLibraryTab.useEffect\": ()=>{\n            const loadMcpInfo = {\n                \"McpLibraryTab.useEffect.loadMcpInfo\": async ()=>{\n                    try {\n                        const info = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.apiClient.getMCPInfo();\n                        setMcpInfo(info);\n                    } catch (error) {\n                        console.error('Failed to load MCP info:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"McpLibraryTab.useEffect.loadMcpInfo\"];\n            loadMcpInfo();\n        }\n    }[\"McpLibraryTab.useEffect\"], []);\n    const filteredServers = mcpServers.filter((server)=>{\n        const matchesSearch = server.name.toLowerCase().includes(searchQuery.toLowerCase()) || server.description.toLowerCase().includes(searchQuery.toLowerCase()) || server.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"All\" || server.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleInstall = async (serverId, serverName)=>{\n        console.log(\"Installing MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n        // Update server status to installing\n        setMcpServers((prev)=>prev.map((server)=>server.id === serverId ? {\n                    ...server,\n                    status: \"updating\"\n                } : server));\n        // Simulate installation process\n        setTimeout(()=>{\n            setMcpServers((prev)=>prev.map((server)=>server.id === serverId ? {\n                        ...server,\n                        status: \"installed\",\n                        lastUpdated: \"Just now\"\n                    } : server));\n        }, 2000);\n    };\n    const handleUninstall = async (serverId, serverName)=>{\n        console.log(\"Uninstalling MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n        setMcpServers((prev)=>prev.map((server)=>server.id === serverId ? {\n                    ...server,\n                    status: \"available\",\n                    lastUpdated: \"Just now\"\n                } : server));\n    };\n    const handleServerSettings = (serverId, serverName)=>{\n        console.log(\"Opening settings for MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    };\n    const handleAddServer = ()=>{\n        if (!newServer.name || !newServer.command) return;\n        const server = {\n            id: \"custom-\".concat(Date.now()),\n            name: newServer.name,\n            description: newServer.description || \"Custom MCP server: \".concat(newServer.name),\n            author: \"Custom\",\n            version: \"1.0.0\",\n            category: newServer.category,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 278,\n                columnNumber: 13\n            }, this),\n            rating: 4.0,\n            downloads: 0,\n            tags: [\n                \"Custom\",\n                newServer.category\n            ],\n            status: \"available\",\n            gradient: \"from-gray-500 to-gray-600\",\n            lastUpdated: \"Just added\"\n        };\n        setMcpServers((prev)=>[\n                ...prev,\n                server\n            ]);\n        setNewServer({\n            name: \"\",\n            command: \"\",\n            args: \"\",\n            description: \"\",\n            category: \"Development\"\n        });\n        setAddServerDialog(false);\n    };\n    const getStatusColorNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"bg-green-500\";\n            case \"updating\":\n                return \"bg-yellow-500\";\n            case \"available\":\n                return \"bg-neutral-500\";\n            default:\n                return \"bg-neutral-500\";\n        }\n    };\n    const getStatusTextNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"Installed\";\n            case \"updating\":\n                return \"Updating\";\n            case \"available\":\n                return \"Available\";\n            default:\n                return \"Unknown\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"MCP Library\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400\",\n                                        children: \"Model Context Protocol servers and integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"bg-gray-800 text-gray-300\",\n                                        children: [\n                                            filteredServers.length,\n                                            \" of \",\n                                            mcpServers.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this),\n                                    mcpInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        className: \"\".concat(mcpInfo.mcp_enabled ? 'bg-green-500/10 text-green-400 border-green-500/20' : 'bg-red-500/10 text-red-400 border-red-500/20'),\n                                        children: [\n                                            \"MCP \",\n                                            mcpInfo.mcp_enabled ? 'Enabled' : 'Disabled'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                                        open: addServerDialog,\n                                        onOpenChange: setAddServerDialog,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"sm\",\n                                                    className: \"bg-blue-gradient-hover text-white shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Add Server\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                                                className: \"bg-neutral-900 border-neutral-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                                                className: \"text-white\",\n                                                                children: \"Add Custom MCP Server\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogDescription, {\n                                                                className: \"text-neutral-400\",\n                                                                children: \"Add a custom MCP server to your library. You can install community servers or create your own.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                        htmlFor: \"name\",\n                                                                        className: \"text-white\",\n                                                                        children: \"Server Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"name\",\n                                                                        value: newServer.name,\n                                                                        onChange: (e)=>setNewServer((prev)=>({\n                                                                                    ...prev,\n                                                                                    name: e.target.value\n                                                                                })),\n                                                                        placeholder: \"e.g., My Custom Server\",\n                                                                        className: \"bg-neutral-800 border-neutral-700 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                        htmlFor: \"command\",\n                                                                        className: \"text-white\",\n                                                                        children: \"Command\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"command\",\n                                                                        value: newServer.command,\n                                                                        onChange: (e)=>setNewServer((prev)=>({\n                                                                                    ...prev,\n                                                                                    command: e.target.value\n                                                                                })),\n                                                                        placeholder: \"e.g., npx, python, node\",\n                                                                        className: \"bg-neutral-800 border-neutral-700 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                        htmlFor: \"args\",\n                                                                        className: \"text-white\",\n                                                                        children: \"Arguments\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                        id: \"args\",\n                                                                        value: newServer.args,\n                                                                        onChange: (e)=>setNewServer((prev)=>({\n                                                                                    ...prev,\n                                                                                    args: e.target.value\n                                                                                })),\n                                                                        placeholder: \"e.g., -y @my/mcp-server\",\n                                                                        className: \"bg-neutral-800 border-neutral-700 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                        htmlFor: \"description\",\n                                                                        className: \"text-white\",\n                                                                        children: \"Description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                        id: \"description\",\n                                                                        value: newServer.description,\n                                                                        onChange: (e)=>setNewServer((prev)=>({\n                                                                                    ...prev,\n                                                                                    description: e.target.value\n                                                                                })),\n                                                                        placeholder: \"Describe what this server does...\",\n                                                                        className: \"bg-neutral-800 border-neutral-700 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>setAddServerDialog(false),\n                                                                        className: \"flex-1\",\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        onClick: handleAddServer,\n                                                                        className: \"flex-1 bg-blue-gradient-hover text-white\",\n                                                                        children: \"Add Server\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search MCP servers...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"\".concat(selectedCategory === category ? \"border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300\" : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                        children: filteredServers.map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200\",\n                                children: [\n                                    server.status === \"installed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            handleServerSettings(server.id, server.name);\n                                        },\n                                        className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                        title: \"Settings for \".concat(server.name),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(server.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                                children: server.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0 pr-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white truncate\",\n                                                                children: server.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(getStatusColorNew(server.status))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-neutral-400\",\n                                                                        children: getStatusTextNew(server.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-neutral-400\",\n                                                                children: server.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"v\",\n                                                                    server.version\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"by \",\n                                                                    server.author\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.downloads.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Updated \",\n                                                                    server.lastUpdated\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                        children: server.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: server.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: server.status === \"installed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleUninstall(server.id, server.name),\n                                            variant: \"outline\",\n                                            className: \"flex-1 border-neutral-700 text-neutral-300 hover:bg-neutral-800 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Installed\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 19\n                                        }, this) : server.status === \"updating\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            disabled: true,\n                                            className: \"flex-1 bg-yellow-600/20 text-yellow-400 border border-yellow-600/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleInstall(server.id, server.name),\n                                            className: \"flex-1 bg-blue-gradient-hover text-white shadow-lg border-0 transition-all duration-200 group-hover:shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Install\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, server.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this),\n                    filteredServers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-400 mb-2\",\n                                    children: \"No MCP servers found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: \"Try adjusting your search or filter criteria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>window.open('https://github.com/modelcontextprotocol/servers', '_blank'),\n                                    className: \"border-blue-500/20 text-blue-400 hover:bg-blue-500/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Browse Community Servers\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-neutral-800 rounded-lg border border-neutral-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white font-medium mb-2\",\n                                children: \"Discover More MCP Servers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-400 text-sm mb-3\",\n                                children: \"Explore the growing ecosystem of Model Context Protocol servers from the community.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>window.open('https://github.com/modelcontextprotocol/servers', '_blank'),\n                                        className: \"border-blue-500/20 text-blue-400 hover:bg-blue-500/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Official Servers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>window.open('https://mcpservers.org', '_blank'),\n                                        className: \"border-green-500/20 text-green-400 hover:bg-green-500/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Community Directory\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>window.open('https://modelcontextprotocol.io', '_blank'),\n                                        className: \"border-purple-500/20 text-purple-400 hover:bg-purple-500/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_ExternalLink_FileText_Globe_Package_Plus_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Documentation\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(McpLibraryTab, \"JES5t+5BpmI/hc8eqIO6/+S0Kfg=\");\n_c = McpLibraryTab;\nvar _c;\n$RefreshReg$(_c, \"McpLibraryTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL21jcC1saWJyYXJ5LXRhYi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRzJDO0FBQ0k7QUFDUztBQUNYO0FBQ0E7QUFDOEU7QUFDeEU7QUFDTjtBQW9CeEI7QUFDOEI7QUFrQm5ELE1BQU00Qix1QkFBdUIsSUFBbUI7UUFDOUM7WUFDRUMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLGFBQ0U7WUFDRkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFVBQVU7WUFDVkMsb0JBQU0sOERBQUNYLDhLQUFLQTtnQkFBQ1ksV0FBVTs7Ozs7O1lBQ3ZCQyxRQUFRO1lBQ1JDLFdBQVc7WUFDWEMsTUFBTTtnQkFBQztnQkFBbUI7Z0JBQVk7Z0JBQWM7YUFBZTtZQUNuRUMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLGFBQWE7UUFDZjtRQUNBO1lBQ0ViLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxhQUNFO1lBQ0ZDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLG9CQUFNLDhEQUFDckIsOEtBQUtBO2dCQUFDc0IsV0FBVTs7Ozs7O1lBQ3ZCQyxRQUFRO1lBQ1JDLFdBQVc7WUFDWEMsTUFBTTtnQkFBQztnQkFBYztnQkFBa0I7Z0JBQWU7YUFBVTtZQUNoRUMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLGFBQWE7UUFDZjtRQUNBO1lBQ0ViLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxhQUNFO1lBQ0ZDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLG9CQUFNLDhEQUFDcEIsOEtBQVFBO2dCQUFDcUIsV0FBVTs7Ozs7O1lBQzFCQyxRQUFRO1lBQ1JDLFdBQVc7WUFDWEMsTUFBTTtnQkFBQztnQkFBUztnQkFBZTtnQkFBWTthQUFjO1lBQ3pEQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsYUFBYTtRQUNmO1FBQ0E7WUFDRWIsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLGFBQ0U7WUFDRkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFVBQVU7WUFDVkMsb0JBQU0sOERBQUNuQiw4S0FBSUE7Z0JBQUNvQixXQUFVOzs7Ozs7WUFDdEJDLFFBQVE7WUFDUkMsV0FBVztZQUNYQyxNQUFNO2dCQUFDO2dCQUFPO2dCQUFtQjtnQkFBYzthQUFVO1lBQ3pEQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsYUFBYTtRQUNmO1FBQ0E7WUFDRWIsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLGFBQ0U7WUFDRkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFVBQVU7WUFDVkMsb0JBQU0sOERBQUNYLDhLQUFLQTtnQkFBQ1ksV0FBVTs7Ozs7O1lBQ3ZCQyxRQUFRO1lBQ1JDLFdBQVc7WUFDWEMsTUFBTTtnQkFBQztnQkFBVTtnQkFBbUI7Z0JBQVc7YUFBSztZQUNwREMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLGFBQWE7UUFDZjtRQUNBO1lBQ0ViLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxhQUNFO1lBQ0ZDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLG9CQUFNLDhEQUFDakIsOEtBQVFBO2dCQUFDa0IsV0FBVTs7Ozs7O1lBQzFCQyxRQUFRO1lBQ1JDLFdBQVc7WUFDWEMsTUFBTTtnQkFBQztnQkFBYTtnQkFBWTtnQkFBVzthQUFhO1lBQ3hEQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsYUFBYTtRQUNmO1FBQ0E7WUFDRWIsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLGFBQ0U7WUFDRkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFVBQVU7WUFDVkMsb0JBQU0sOERBQUNWLDhLQUFHQTtnQkFBQ1csV0FBVTs7Ozs7O1lBQ3JCQyxRQUFRO1lBQ1JDLFdBQVc7WUFDWEMsTUFBTTtnQkFBQztnQkFBUTtnQkFBWTtnQkFBUTthQUFhO1lBQ2hEQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsYUFBYTtRQUNmO0tBQ0Q7QUFFRCxNQUFNQyxhQUFhO0lBQUM7SUFBTztJQUFlO0lBQU87SUFBTTtJQUFXO0lBQVk7Q0FBZ0I7QUFFOUYsTUFBTUMsaUJBQWlCLENBQUNKO0lBQ3RCLE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBQ1Q7WUFDRSxPQUFPO0lBQ1g7QUFDRjtBQUVBLE1BQU1LLGdCQUFnQixDQUFDTDtJQUNyQixPQUFRQTtRQUNOLEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNUO1lBQ0UsT0FBTztJQUNYO0FBQ0Y7QUFFZSxTQUFTTTs7SUFDdEIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdoRCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNpRCxrQkFBa0JDLG9CQUFvQixHQUFHbEQsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDbUQsWUFBWUMsY0FBYyxHQUFHcEQsK0NBQVFBLENBQWM0QjtJQUMxRCxNQUFNLENBQUN5QixTQUFTQyxXQUFXLEdBQUd0RCwrQ0FBUUEsQ0FBaUI7SUFDdkQsTUFBTSxDQUFDdUQsU0FBU0MsV0FBVyxHQUFHeEQsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDeUQsaUJBQWlCQyxtQkFBbUIsR0FBRzFELCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzJELFdBQVdDLGFBQWEsR0FBRzVELCtDQUFRQSxDQUFDO1FBQ3pDOEIsTUFBTTtRQUNOK0IsU0FBUztRQUNUQyxNQUFNO1FBQ04vQixhQUFhO1FBQ2JHLFVBQVU7SUFDWjtJQUVBakMsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTThEO3VEQUFjO29CQUNsQixJQUFJO3dCQUNGLE1BQU1DLE9BQU8sTUFBTXJDLCtDQUFTQSxDQUFDc0MsVUFBVTt3QkFDdkNYLFdBQVdVO29CQUNiLEVBQUUsT0FBT0UsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7b0JBQzVDLFNBQVU7d0JBQ1JWLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUFPO1FBQ0Y7a0NBQUcsRUFBRTtJQUVMLE1BQU1LLGtCQUFrQmpCLFdBQVdrQixNQUFNLENBQUMsQ0FBQ0M7UUFDekMsTUFBTUMsZ0JBQ0pELE9BQU94QyxJQUFJLENBQUMwQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQzFCLFlBQVl5QixXQUFXLE9BQzFERixPQUFPdkMsV0FBVyxDQUFDeUMsV0FBVyxHQUFHQyxRQUFRLENBQUMxQixZQUFZeUIsV0FBVyxPQUNqRUYsT0FBTy9CLElBQUksQ0FBQ21DLElBQUksQ0FBQyxDQUFDQyxNQUFRQSxJQUFJSCxXQUFXLEdBQUdDLFFBQVEsQ0FBQzFCLFlBQVl5QixXQUFXO1FBRTlFLE1BQU1JLGtCQUFrQjNCLHFCQUFxQixTQUFTcUIsT0FBT3BDLFFBQVEsS0FBS2U7UUFFMUUsT0FBT3NCLGlCQUFpQks7SUFDMUI7SUFFQSxNQUFNQyxnQkFBZ0IsT0FBT0MsVUFBa0JDO1FBQzdDWixRQUFRYSxHQUFHLENBQUMsMEJBQTZDRixPQUFuQkMsWUFBVyxVQUFpQixPQUFURCxVQUFTO1FBRWxFLHFDQUFxQztRQUNyQzFCLGNBQWM2QixDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNaLENBQUFBLFNBQzdCQSxPQUFPekMsRUFBRSxLQUFLaUQsV0FDVjtvQkFBRSxHQUFHUixNQUFNO29CQUFFOUIsUUFBUTtnQkFBb0IsSUFDekM4QjtRQUdOLGdDQUFnQztRQUNoQ2EsV0FBVztZQUNUL0IsY0FBYzZCLENBQUFBLE9BQVFBLEtBQUtDLEdBQUcsQ0FBQ1osQ0FBQUEsU0FDN0JBLE9BQU96QyxFQUFFLEtBQUtpRCxXQUNWO3dCQUFFLEdBQUdSLE1BQU07d0JBQUU5QixRQUFRO3dCQUFzQkUsYUFBYTtvQkFBVyxJQUNuRTRCO1FBRVIsR0FBRztJQUNMO0lBRUEsTUFBTWMsa0JBQWtCLE9BQU9OLFVBQWtCQztRQUMvQ1osUUFBUWEsR0FBRyxDQUFDLDRCQUErQ0YsT0FBbkJDLFlBQVcsVUFBaUIsT0FBVEQsVUFBUztRQUVwRTFCLGNBQWM2QixDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNaLENBQUFBLFNBQzdCQSxPQUFPekMsRUFBRSxLQUFLaUQsV0FDVjtvQkFBRSxHQUFHUixNQUFNO29CQUFFOUIsUUFBUTtvQkFBc0JFLGFBQWE7Z0JBQVcsSUFDbkU0QjtJQUVSO0lBRUEsTUFBTWUsdUJBQXVCLENBQUNQLFVBQWtCQztRQUM5Q1osUUFBUWEsR0FBRyxDQUFDLG9DQUF1REYsT0FBbkJDLFlBQVcsVUFBaUIsT0FBVEQsVUFBUztJQUM5RTtJQUVBLE1BQU1RLGtCQUFrQjtRQUN0QixJQUFJLENBQUMzQixVQUFVN0IsSUFBSSxJQUFJLENBQUM2QixVQUFVRSxPQUFPLEVBQUU7UUFFM0MsTUFBTVMsU0FBb0I7WUFDeEJ6QyxJQUFJLFVBQXFCLE9BQVgwRCxLQUFLQyxHQUFHO1lBQ3RCMUQsTUFBTTZCLFVBQVU3QixJQUFJO1lBQ3BCQyxhQUFhNEIsVUFBVTVCLFdBQVcsSUFBSSxzQkFBcUMsT0FBZjRCLFVBQVU3QixJQUFJO1lBQzFFRSxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVXlCLFVBQVV6QixRQUFRO1lBQzVCQyxvQkFBTSw4REFBQ2IsOEtBQU9BO2dCQUFDYyxXQUFVOzs7Ozs7WUFDekJDLFFBQVE7WUFDUkMsV0FBVztZQUNYQyxNQUFNO2dCQUFDO2dCQUFVb0IsVUFBVXpCLFFBQVE7YUFBQztZQUNwQ00sUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLGFBQWE7UUFDZjtRQUVBVSxjQUFjNkIsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1YO2FBQU87UUFDdkNWLGFBQWE7WUFBRTlCLE1BQU07WUFBSStCLFNBQVM7WUFBSUMsTUFBTTtZQUFJL0IsYUFBYTtZQUFJRyxVQUFVO1FBQWM7UUFDekZ3QixtQkFBbUI7SUFDckI7SUFFQSxNQUFNK0Isb0JBQW9CLENBQUNqRDtRQUN6QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTWtELG1CQUFtQixDQUFDbEQ7UUFDeEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDbUQ7UUFBSXZELFdBQVU7OzBCQUViLDhEQUFDdUQ7Z0JBQUl2RCxXQUFVOztrQ0FDYiw4REFBQ3VEO3dCQUFJdkQsV0FBVTs7MENBQ2IsOERBQUN1RDs7a0RBQ0MsOERBQUNDO3dDQUFHeEQsV0FBVTtrREFBbUM7Ozs7OztrREFDakQsOERBQUN5RDt3Q0FBRXpELFdBQVU7a0RBQTJCOzs7Ozs7Ozs7Ozs7MENBRTFDLDhEQUFDdUQ7Z0NBQUl2RCxXQUFVOztrREFDYiw4REFBQ2hDLHVEQUFLQTt3Q0FBQzBGLFNBQVE7d0NBQVkxRCxXQUFVOzs0Q0FDbENnQyxnQkFBZ0IyQixNQUFNOzRDQUFDOzRDQUFLNUMsV0FBVzRDLE1BQU07Ozs7Ozs7b0NBRS9DMUMseUJBQ0MsOERBQUNqRCx1REFBS0E7d0NBQUNnQyxXQUFXLEdBQStILE9BQTVIaUIsUUFBUTJDLFdBQVcsR0FBRyx1REFBdUQ7OzRDQUFrRDs0Q0FDN0kzQyxRQUFRMkMsV0FBVyxHQUFHLFlBQVk7Ozs7Ozs7a0RBRzNDLDhEQUFDMUYseURBQU1BO3dDQUFDMkYsTUFBTXhDO3dDQUFpQnlDLGNBQWN4Qzs7MERBQzNDLDhEQUFDL0MsZ0VBQWFBO2dEQUFDd0YsT0FBTzswREFDcEIsNEVBQUNqRyx5REFBTUE7b0RBQUNrRyxNQUFLO29EQUFLaEUsV0FBVTs7c0VBQzFCLDhEQUFDYiw4S0FBSUE7NERBQUNhLFdBQVU7Ozs7Ozt3REFBaUI7Ozs7Ozs7Ozs7OzswREFJckMsOERBQUM3QixnRUFBYUE7Z0RBQUM2QixXQUFVOztrRUFDdkIsOERBQUMzQiwrREFBWUE7OzBFQUNYLDhEQUFDQyw4REFBV0E7Z0VBQUMwQixXQUFVOzBFQUFhOzs7Ozs7MEVBQ3BDLDhEQUFDNUIsb0VBQWlCQTtnRUFBQzRCLFdBQVU7MEVBQW1COzs7Ozs7Ozs7Ozs7a0VBSWxELDhEQUFDdUQ7d0RBQUl2RCxXQUFVOzswRUFDYiw4REFBQ3VEOztrRkFDQyw4REFBQzlFLHVEQUFLQTt3RUFBQ3dGLFNBQVE7d0VBQU9qRSxXQUFVO2tGQUFhOzs7Ozs7a0ZBQzdDLDhEQUFDL0IsdURBQUtBO3dFQUNKd0IsSUFBRzt3RUFDSHlFLE9BQU8zQyxVQUFVN0IsSUFBSTt3RUFDckJ5RSxVQUFVLENBQUNDLElBQU01QyxhQUFhcUIsQ0FBQUEsT0FBUztvRkFBRSxHQUFHQSxJQUFJO29GQUFFbkQsTUFBTTBFLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnRkFBQzt3RUFDdkVJLGFBQVk7d0VBQ1p0RSxXQUFVOzs7Ozs7Ozs7Ozs7MEVBR2QsOERBQUN1RDs7a0ZBQ0MsOERBQUM5RSx1REFBS0E7d0VBQUN3RixTQUFRO3dFQUFVakUsV0FBVTtrRkFBYTs7Ozs7O2tGQUNoRCw4REFBQy9CLHVEQUFLQTt3RUFDSndCLElBQUc7d0VBQ0h5RSxPQUFPM0MsVUFBVUUsT0FBTzt3RUFDeEIwQyxVQUFVLENBQUNDLElBQU01QyxhQUFhcUIsQ0FBQUEsT0FBUztvRkFBRSxHQUFHQSxJQUFJO29GQUFFcEIsU0FBUzJDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnRkFBQzt3RUFDMUVJLGFBQVk7d0VBQ1p0RSxXQUFVOzs7Ozs7Ozs7Ozs7MEVBR2QsOERBQUN1RDs7a0ZBQ0MsOERBQUM5RSx1REFBS0E7d0VBQUN3RixTQUFRO3dFQUFPakUsV0FBVTtrRkFBYTs7Ozs7O2tGQUM3Qyw4REFBQy9CLHVEQUFLQTt3RUFDSndCLElBQUc7d0VBQ0h5RSxPQUFPM0MsVUFBVUcsSUFBSTt3RUFDckJ5QyxVQUFVLENBQUNDLElBQU01QyxhQUFhcUIsQ0FBQUEsT0FBUztvRkFBRSxHQUFHQSxJQUFJO29GQUFFbkIsTUFBTTBDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnRkFBQzt3RUFDdkVJLGFBQVk7d0VBQ1p0RSxXQUFVOzs7Ozs7Ozs7Ozs7MEVBR2QsOERBQUN1RDs7a0ZBQ0MsOERBQUM5RSx1REFBS0E7d0VBQUN3RixTQUFRO3dFQUFjakUsV0FBVTtrRkFBYTs7Ozs7O2tGQUNwRCw4REFBQ3hCLDZEQUFRQTt3RUFDUGlCLElBQUc7d0VBQ0h5RSxPQUFPM0MsVUFBVTVCLFdBQVc7d0VBQzVCd0UsVUFBVSxDQUFDQyxJQUFNNUMsYUFBYXFCLENBQUFBLE9BQVM7b0ZBQUUsR0FBR0EsSUFBSTtvRkFBRWxELGFBQWF5RSxFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0ZBQUM7d0VBQzlFSSxhQUFZO3dFQUNadEUsV0FBVTs7Ozs7Ozs7Ozs7OzBFQUdkLDhEQUFDdUQ7Z0VBQUl2RCxXQUFVOztrRkFDYiw4REFBQ2xDLHlEQUFNQTt3RUFBQzRGLFNBQVE7d0VBQVVhLFNBQVMsSUFBTWpELG1CQUFtQjt3RUFBUXRCLFdBQVU7a0ZBQVM7Ozs7OztrRkFHdkYsOERBQUNsQyx5REFBTUE7d0VBQUN5RyxTQUFTckI7d0VBQWlCbEQsV0FBVTtrRkFBMkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FXbkcsOERBQUN1RDt3QkFBSXZELFdBQVU7OzBDQUNiLDhEQUFDdUQ7Z0NBQUl2RCxXQUFVOztrREFDYiw4REFBQ25CLDhLQUFNQTt3Q0FBQ21CLFdBQVU7Ozs7OztrREFDbEIsOERBQUMvQix1REFBS0E7d0NBQ0pxRyxhQUFZO3dDQUNaSixPQUFPdkQ7d0NBQ1B3RCxVQUFVLENBQUNDLElBQU14RCxlQUFld0QsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dDQUM5Q2xFLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FJZCw4REFBQ3VEO2dDQUFJdkQsV0FBVTswQ0FDWk8sV0FBV3VDLEdBQUcsQ0FBQyxDQUFDaEQseUJBQ2YsOERBQUNoQyx5REFBTUE7d0NBRUw0RixTQUFRO3dDQUNSTSxNQUFLO3dDQUNMTyxTQUFTLElBQU16RCxvQkFBb0JoQjt3Q0FDbkNFLFdBQVcsR0FJVixPQUhDYSxxQkFBcUJmLFdBQ2pCLDhFQUNBO2tEQUdMQTt1Q0FWSUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBa0JmLDhEQUFDL0Isa0VBQVVBO2dCQUFDaUMsV0FBVTs7a0NBQ3BCLDhEQUFDdUQ7d0JBQUl2RCxXQUFVO2tDQUNaZ0MsZ0JBQWdCYyxHQUFHLENBQUMsQ0FBQ1osdUJBQ3BCLDhEQUFDcUI7Z0NBRUN2RCxXQUFVOztvQ0FHVGtDLE9BQU85QixNQUFNLEtBQUssNkJBQ2pCLDhEQUFDb0U7d0NBQ0NELFNBQVMsQ0FBQ0g7NENBQ1JBLEVBQUVLLGVBQWU7NENBQ2pCeEIscUJBQXFCZixPQUFPekMsRUFBRSxFQUFFeUMsT0FBT3hDLElBQUk7d0NBQzdDO3dDQUNBTSxXQUFVO3dDQUNWMEUsT0FBTyxnQkFBNEIsT0FBWnhDLE9BQU94QyxJQUFJO2tEQUVsQyw0RUFBQ1QsOEtBQVFBOzRDQUFDZSxXQUFVOzs7Ozs7Ozs7OztrREFLeEIsOERBQUN1RDt3Q0FBSXZELFdBQVU7OzBEQUNiLDhEQUFDdUQ7Z0RBQ0N2RCxXQUFXLDBDQUEwRCxPQUFoQmtDLE9BQU83QixRQUFRLEVBQUM7MERBRXBFNkIsT0FBT25DLElBQUk7Ozs7OzswREFHZCw4REFBQ3dEO2dEQUFJdkQsV0FBVTs7a0VBQ2IsOERBQUN1RDt3REFBSXZELFdBQVU7OzBFQUNiLDhEQUFDMkU7Z0VBQUczRSxXQUFVOzBFQUFxQ2tDLE9BQU94QyxJQUFJOzs7Ozs7MEVBQzlELDhEQUFDNkQ7Z0VBQUl2RCxXQUFVOztrRkFDYiw4REFBQ3VEO3dFQUFJdkQsV0FBVyx3QkFBeUQsT0FBakNxRCxrQkFBa0JuQixPQUFPOUIsTUFBTTs7Ozs7O2tGQUN2RSw4REFBQ3dFO3dFQUFLNUUsV0FBVTtrRkFBNEJzRCxpQkFBaUJwQixPQUFPOUIsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUk5RSw4REFBQ21EO3dEQUFJdkQsV0FBVTs7MEVBQ2IsOERBQUN5RDtnRUFBRXpELFdBQVU7MEVBQTRCa0MsT0FBT3BDLFFBQVE7Ozs7OzswRUFDeEQsOERBQUM4RTtnRUFBSzVFLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3hDLDhEQUFDeUQ7Z0VBQUV6RCxXQUFVOztvRUFBd0I7b0VBQUVrQyxPQUFPckMsT0FBTzs7Ozs7OzswRUFDckQsOERBQUMrRTtnRUFBSzVFLFdBQVU7MEVBQXdCOzs7Ozs7MEVBQ3hDLDhEQUFDeUQ7Z0VBQUV6RCxXQUFVOztvRUFBd0I7b0VBQUlrQyxPQUFPdEMsTUFBTTs7Ozs7Ozs7Ozs7OztrRUFHeEQsOERBQUMyRDt3REFBSXZELFdBQVU7OzBFQUNiLDhEQUFDdUQ7Z0VBQUl2RCxXQUFVOztrRkFDYiw4REFBQ2hCLDhLQUFJQTt3RUFBQ2dCLFdBQVU7Ozs7OztrRkFDaEIsOERBQUM0RTtrRkFBTTFDLE9BQU9qQyxNQUFNOzs7Ozs7Ozs7Ozs7MEVBRXRCLDhEQUFDc0Q7Z0VBQUl2RCxXQUFVOztrRkFDYiw4REFBQ2xCLDhLQUFRQTt3RUFBQ2tCLFdBQVU7Ozs7OztrRkFDcEIsOERBQUM0RTtrRkFBTTFDLE9BQU9oQyxTQUFTLENBQUMyRSxjQUFjOzs7Ozs7Ozs7Ozs7MEVBRXhDLDhEQUFDRDs7b0VBQUs7b0VBQVMxQyxPQUFPNUIsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNdkMsOERBQUNtRDt3Q0FBRXpELFdBQVU7a0RBQWlEa0MsT0FBT3ZDLFdBQVc7Ozs7OztrREFHaEYsOERBQUM0RDt3Q0FBSXZELFdBQVU7a0RBQ1prQyxPQUFPL0IsSUFBSSxDQUFDMkMsR0FBRyxDQUFDLENBQUNQLG9CQUNoQiw4REFBQ3ZFLHVEQUFLQTtnREFFSjBGLFNBQVE7Z0RBQ1IxRCxXQUFVOzBEQUVUdUM7K0NBSklBOzs7Ozs7Ozs7O2tEQVVYLDhEQUFDZ0I7d0NBQUl2RCxXQUFVO2tEQUNaa0MsT0FBTzlCLE1BQU0sS0FBSyw0QkFDakIsOERBQUN0Qyx5REFBTUE7NENBQ0x5RyxTQUFTLElBQU12QixnQkFBZ0JkLE9BQU96QyxFQUFFLEVBQUV5QyxPQUFPeEMsSUFBSTs0Q0FDckRnRSxTQUFROzRDQUNSMUQsV0FBVTs7OERBRVYsOERBQUNqQiw4S0FBV0E7b0RBQUNpQixXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7bURBR3hDa0MsT0FBTzlCLE1BQU0sS0FBSywyQkFDcEIsOERBQUN0Qyx5REFBTUE7NENBQUNnSCxRQUFROzRDQUFDOUUsV0FBVTs7OERBQ3pCLDhEQUFDZCw4S0FBT0E7b0RBQUNjLFdBQVU7Ozs7OztnREFBOEI7Ozs7OztpRUFJbkQsOERBQUNsQyx5REFBTUE7NENBQ0x5RyxTQUFTLElBQU05QixjQUFjUCxPQUFPekMsRUFBRSxFQUFFeUMsT0FBT3hDLElBQUk7NENBQ25ETSxXQUFZOzs4REFFWiw4REFBQ2xCLDhLQUFRQTtvREFBQ2tCLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7Ozs7OztrREFPM0MsOERBQUN1RDt3Q0FBSXZELFdBQVU7Ozs7Ozs7K0JBcEdWa0MsT0FBT3pDLEVBQUU7Ozs7Ozs7Ozs7b0JBeUduQnVDLGdCQUFnQjJCLE1BQU0sS0FBSyxtQkFDMUIsOERBQUNKO3dCQUFJdkQsV0FBVTtrQ0FDYiw0RUFBQ3VEOzRCQUFJdkQsV0FBVTs7OENBQ2IsOERBQUNkLDhLQUFPQTtvQ0FBQ2MsV0FBVTs7Ozs7OzhDQUNuQiw4REFBQzJFO29DQUFHM0UsV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUN5RDtvQ0FBRXpELFdBQVU7OENBQTZCOzs7Ozs7OENBQzFDLDhEQUFDbEMseURBQU1BO29DQUNMNEYsU0FBUTtvQ0FDUmEsU0FBUyxJQUFNUSxPQUFPbEIsSUFBSSxDQUFDLG1EQUFtRDtvQ0FDOUU3RCxXQUFVOztzREFFViw4REFBQ1YsOEtBQVlBOzRDQUFDVSxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUWpELDhEQUFDdUQ7d0JBQUl2RCxXQUFVOzswQ0FDYiw4REFBQzJFO2dDQUFHM0UsV0FBVTswQ0FBOEI7Ozs7OzswQ0FDNUMsOERBQUN5RDtnQ0FBRXpELFdBQVU7MENBQWdDOzs7Ozs7MENBRzdDLDhEQUFDdUQ7Z0NBQUl2RCxXQUFVOztrREFDYiw4REFBQ2xDLHlEQUFNQTt3Q0FDTDRGLFNBQVE7d0NBQ1JNLE1BQUs7d0NBQ0xPLFNBQVMsSUFBTVEsT0FBT2xCLElBQUksQ0FBQyxtREFBbUQ7d0NBQzlFN0QsV0FBVTs7MERBRVYsOERBQUNWLDhLQUFZQTtnREFBQ1UsV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7OztrREFHM0MsOERBQUNsQyx5REFBTUE7d0NBQ0w0RixTQUFRO3dDQUNSTSxNQUFLO3dDQUNMTyxTQUFTLElBQU1RLE9BQU9sQixJQUFJLENBQUMsMEJBQTBCO3dDQUNyRDdELFdBQVU7OzBEQUVWLDhEQUFDViw4S0FBWUE7Z0RBQUNVLFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7a0RBRzNDLDhEQUFDbEMseURBQU1BO3dDQUNMNEYsU0FBUTt3Q0FDUk0sTUFBSzt3Q0FDTE8sU0FBUyxJQUFNUSxPQUFPbEIsSUFBSSxDQUFDLG1DQUFtQzt3Q0FDOUQ3RCxXQUFVOzswREFFViw4REFBQ1YsOEtBQVlBO2dEQUFDVSxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXZEO0dBM1p3QlU7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxtY3AtbGlicmFyeS10YWIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IFNjcm9sbEFyZWEgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3Njcm9sbC1hcmVhXCJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgRGlhbG9nLCBEaWFsb2dDb250ZW50LCBEaWFsb2dEZXNjcmlwdGlvbiwgRGlhbG9nSGVhZGVyLCBEaWFsb2dUaXRsZSwgRGlhbG9nVHJpZ2dlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZGlhbG9nXCJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbGFiZWxcIlxuaW1wb3J0IHtcbiAgRGF0YWJhc2UsXG4gIEdsb2JlLFxuICBGaWxlVGV4dCxcbiAgQ2FsZW5kYXIsXG4gIE1haWwsXG4gIENvZGUsXG4gIFNlYXJjaCxcbiAgRG93bmxvYWQsXG4gIENoZWNrQ2lyY2xlLFxuICBTdGFyLFxuICBTZXR0aW5ncyxcbiAgUGFja2FnZSxcbiAgUGx1cyxcbiAgVGVybWluYWwsXG4gIEJyYWluLFxuICBaYXAsXG4gIEV4dGVybmFsTGluayxcbiAgTG9hZGVyMlxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcbmltcG9ydCB7IGFwaUNsaWVudCwgdHlwZSBNQ1BJbmZvIH0gZnJvbSBcIkAvbGliL2FwaVwiXG5cbmludGVyZmFjZSBNY3BTZXJ2ZXIge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIGF1dGhvcjogc3RyaW5nXG4gIHZlcnNpb246IHN0cmluZ1xuICBjYXRlZ29yeTogc3RyaW5nXG4gIGljb246IFJlYWN0LlJlYWN0Tm9kZVxuICByYXRpbmc6IG51bWJlclxuICBkb3dubG9hZHM6IG51bWJlclxuICB0YWdzOiBzdHJpbmdbXVxuICBzdGF0dXM6IFwiaW5zdGFsbGVkXCIgfCBcImF2YWlsYWJsZVwiIHwgXCJ1cGRhdGluZ1wiXG4gIGdyYWRpZW50OiBzdHJpbmdcbiAgbGFzdFVwZGF0ZWQ6IHN0cmluZ1xufVxuXG5jb25zdCBnZXRJbml0aWFsTWNwU2VydmVycyA9ICgpOiBNY3BTZXJ2ZXJbXSA9PiBbXG4gIHtcbiAgICBpZDogXCJhZzNudC14XCIsXG4gICAgbmFtZTogXCJBRzNOVCBYIEFnZW50XCIsXG4gICAgZGVzY3JpcHRpb246XG4gICAgICBcIk91ciBjb21wcmVoZW5zaXZlIGFnZW50IHN5c3RlbSB3aXRoIGZpbGUgb3BlcmF0aW9ucywgdGVybWluYWwgY29tbWFuZHMsIHdlYiBzZWFyY2gsIGNhbGN1bGF0aW9ucywgYW5kIHN5c3RlbSBtYW5hZ2VtZW50IGNhcGFiaWxpdGllcy5cIixcbiAgICBhdXRob3I6IFwiQUczTlQgWFwiLFxuICAgIHZlcnNpb246IFwiMS4wLjBcIixcbiAgICBjYXRlZ29yeTogXCJEZXZlbG9wbWVudFwiLFxuICAgIGljb246IDxCcmFpbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4sXG4gICAgcmF0aW5nOiA0LjksXG4gICAgZG93bmxvYWRzOiAxLFxuICAgIHRhZ3M6IFtcIkZpbGUgT3BlcmF0aW9uc1wiLCBcIlRlcm1pbmFsXCIsIFwiV2ViIFNlYXJjaFwiLCBcIkNhbGN1bGF0aW9uc1wiXSxcbiAgICBzdGF0dXM6IFwiaW5zdGFsbGVkXCIsXG4gICAgZ3JhZGllbnQ6IFwiZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMFwiLFxuICAgIGxhc3RVcGRhdGVkOiBcIkFjdGl2ZVwiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwicGxheXdyaWdodFwiLFxuICAgIG5hbWU6IFwiUGxheXdyaWdodCBNQ1BcIixcbiAgICBkZXNjcmlwdGlvbjpcbiAgICAgIFwiUHJvZmVzc2lvbmFsIHdlYiBhdXRvbWF0aW9uIGFuZCBzY3JhcGluZyB3aXRoIFBsYXl3cmlnaHQuIE5hdmlnYXRlIHdlYnNpdGVzLCB0YWtlIHNjcmVlbnNob3RzLCBleHRyYWN0IGNvbnRlbnQsIGFuZCBhdXRvbWF0ZSBicm93c2VyIGludGVyYWN0aW9ucy5cIixcbiAgICBhdXRob3I6IFwiQW50aHJvcGljXCIsXG4gICAgdmVyc2lvbjogXCJsYXRlc3RcIixcbiAgICBjYXRlZ29yeTogXCJXZWJcIixcbiAgICBpY29uOiA8R2xvYmUgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+LFxuICAgIHJhdGluZzogNC44LFxuICAgIGRvd25sb2FkczogNjEyMDAsXG4gICAgdGFnczogW1wiUGxheXdyaWdodFwiLCBcIldlYiBBdXRvbWF0aW9uXCIsIFwiU2NyZWVuc2hvdHNcIiwgXCJCcm93c2VyXCJdLFxuICAgIHN0YXR1czogXCJpbnN0YWxsZWRcIixcbiAgICBncmFkaWVudDogXCJmcm9tLWdyZWVuLTUwMCB0by1lbWVyYWxkLTUwMFwiLFxuICAgIGxhc3RVcGRhdGVkOiBcIkFjdGl2ZVwiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiZmlsZXN5c3RlbVwiLFxuICAgIG5hbWU6IFwiRmlsZXN5c3RlbSBNQ1BcIixcbiAgICBkZXNjcmlwdGlvbjpcbiAgICAgIFwiU2VjdXJlIGZpbGUgb3BlcmF0aW9ucyB3aXRoIGNvbmZpZ3VyYWJsZSBhY2Nlc3MgY29udHJvbHMuIFJlYWQsIHdyaXRlLCBjcmVhdGUsIGRlbGV0ZSwgYW5kIG1hbmFnZSBmaWxlcyBhbmQgZGlyZWN0b3JpZXMgd2l0aCBhZHZhbmNlZCBwZXJtaXNzaW9ucy5cIixcbiAgICBhdXRob3I6IFwiQW50aHJvcGljXCIsXG4gICAgdmVyc2lvbjogXCJsYXRlc3RcIixcbiAgICBjYXRlZ29yeTogXCJEZXZlbG9wbWVudFwiLFxuICAgIGljb246IDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4sXG4gICAgcmF0aW5nOiA0LjksXG4gICAgZG93bmxvYWRzOiA0NTMwMCxcbiAgICB0YWdzOiBbXCJGaWxlc1wiLCBcIkRpcmVjdG9yaWVzXCIsIFwiU2VjdXJpdHlcIiwgXCJQZXJtaXNzaW9uc1wiXSxcbiAgICBzdGF0dXM6IFwiYXZhaWxhYmxlXCIsXG4gICAgZ3JhZGllbnQ6IFwiZnJvbS1wdXJwbGUtNTAwIHRvLXBpbmstNTAwXCIsXG4gICAgbGFzdFVwZGF0ZWQ6IFwiMSB3ZWVrIGFnb1wiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiZ2l0XCIsXG4gICAgbmFtZTogXCJHaXQgTUNQXCIsXG4gICAgZGVzY3JpcHRpb246XG4gICAgICBcIlRvb2xzIHRvIHJlYWQsIHNlYXJjaCwgYW5kIG1hbmlwdWxhdGUgR2l0IHJlcG9zaXRvcmllcy4gTWFuYWdlIGNvbW1pdHMsIGJyYW5jaGVzLCBhbmQgcmVwb3NpdG9yeSBvcGVyYXRpb25zIHRocm91Z2ggTUNQLlwiLFxuICAgIGF1dGhvcjogXCJBbnRocm9waWNcIixcbiAgICB2ZXJzaW9uOiBcImxhdGVzdFwiLFxuICAgIGNhdGVnb3J5OiBcIkRldmVsb3BtZW50XCIsXG4gICAgaWNvbjogPENvZGUgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+LFxuICAgIHJhdGluZzogNC44LFxuICAgIGRvd25sb2FkczogMzIxMDAsXG4gICAgdGFnczogW1wiR2l0XCIsIFwiVmVyc2lvbiBDb250cm9sXCIsIFwiUmVwb3NpdG9yeVwiLCBcIkNvbW1pdHNcIl0sXG4gICAgc3RhdHVzOiBcImF2YWlsYWJsZVwiLFxuICAgIGdyYWRpZW50OiBcImZyb20tb3JhbmdlLTUwMCB0by1yZWQtNTAwXCIsXG4gICAgbGFzdFVwZGF0ZWQ6IFwiMyBkYXlzIGFnb1wiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwibWVtb3J5XCIsXG4gICAgbmFtZTogXCJNZW1vcnkgTUNQXCIsXG4gICAgZGVzY3JpcHRpb246XG4gICAgICBcIktub3dsZWRnZSBncmFwaC1iYXNlZCBwZXJzaXN0ZW50IG1lbW9yeSBzeXN0ZW0uIFN0b3JlIGFuZCByZXRyaWV2ZSBpbmZvcm1hdGlvbiBhY3Jvc3MgY29udmVyc2F0aW9ucyB3aXRoIGludGVsbGlnZW50IGNvbnRleHQgbWFuYWdlbWVudC5cIixcbiAgICBhdXRob3I6IFwiQW50aHJvcGljXCIsXG4gICAgdmVyc2lvbjogXCJsYXRlc3RcIixcbiAgICBjYXRlZ29yeTogXCJBSVwiLFxuICAgIGljb246IDxCcmFpbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4sXG4gICAgcmF0aW5nOiA0LjcsXG4gICAgZG93bmxvYWRzOiAyODkwMCxcbiAgICB0YWdzOiBbXCJNZW1vcnlcIiwgXCJLbm93bGVkZ2UgR3JhcGhcIiwgXCJDb250ZXh0XCIsIFwiQUlcIl0sXG4gICAgc3RhdHVzOiBcImF2YWlsYWJsZVwiLFxuICAgIGdyYWRpZW50OiBcImZyb20teWVsbG93LTUwMCB0by1vcmFuZ2UtNTAwXCIsXG4gICAgbGFzdFVwZGF0ZWQ6IFwiMSB3ZWVrIGFnb1wiLFxuICB9LFxuICB7XG4gICAgaWQ6IFwiZmV0Y2hcIixcbiAgICBuYW1lOiBcIkZldGNoIE1DUFwiLFxuICAgIGRlc2NyaXB0aW9uOlxuICAgICAgXCJXZWIgY29udGVudCBmZXRjaGluZyBhbmQgY29udmVyc2lvbiBmb3IgZWZmaWNpZW50IExMTSB1c2FnZS4gQ29udmVydCB3ZWIgcGFnZXMgdG8gbWFya2Rvd24gYW5kIGV4dHJhY3Qgc3RydWN0dXJlZCBjb250ZW50LlwiLFxuICAgIGF1dGhvcjogXCJBbnRocm9waWNcIixcbiAgICB2ZXJzaW9uOiBcImxhdGVzdFwiLFxuICAgIGNhdGVnb3J5OiBcIldlYlwiLFxuICAgIGljb246IDxEb3dubG9hZCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4sXG4gICAgcmF0aW5nOiA0LjYsXG4gICAgZG93bmxvYWRzOiAxOTgwMCxcbiAgICB0YWdzOiBbXCJXZWIgRmV0Y2hcIiwgXCJNYXJrZG93blwiLCBcIkNvbnRlbnRcIiwgXCJDb252ZXJzaW9uXCJdLFxuICAgIHN0YXR1czogXCJhdmFpbGFibGVcIixcbiAgICBncmFkaWVudDogXCJmcm9tLWluZGlnby01MDAgdG8tcHVycGxlLTUwMFwiLFxuICAgIGxhc3RVcGRhdGVkOiBcIjIgZGF5cyBhZ29cIixcbiAgfSxcbiAge1xuICAgIGlkOiBcInRpbWVcIixcbiAgICBuYW1lOiBcIlRpbWUgTUNQXCIsXG4gICAgZGVzY3JpcHRpb246XG4gICAgICBcIlRpbWUgYW5kIHRpbWV6b25lIGNvbnZlcnNpb24gY2FwYWJpbGl0aWVzLiBIYW5kbGUgZGF0ZXMsIHRpbWVzLCBhbmQgdGltZXpvbmUgb3BlcmF0aW9ucyB3aXRoIHByZWNpc2lvbi5cIixcbiAgICBhdXRob3I6IFwiQW50aHJvcGljXCIsXG4gICAgdmVyc2lvbjogXCJsYXRlc3RcIixcbiAgICBjYXRlZ29yeTogXCJVdGlsaXR5XCIsXG4gICAgaWNvbjogPFphcCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4sXG4gICAgcmF0aW5nOiA0LjUsXG4gICAgZG93bmxvYWRzOiAxNTYwMCxcbiAgICB0YWdzOiBbXCJUaW1lXCIsIFwiVGltZXpvbmVcIiwgXCJEYXRlXCIsIFwiQ29udmVyc2lvblwiXSxcbiAgICBzdGF0dXM6IFwiYXZhaWxhYmxlXCIsXG4gICAgZ3JhZGllbnQ6IFwiZnJvbS1jeWFuLTUwMCB0by1ibHVlLTUwMFwiLFxuICAgIGxhc3RVcGRhdGVkOiBcIjEgd2VlayBhZ29cIixcbiAgfVxuXVxuXG5jb25zdCBjYXRlZ29yaWVzID0gW1wiQWxsXCIsIFwiRGV2ZWxvcG1lbnRcIiwgXCJXZWJcIiwgXCJBSVwiLCBcIlV0aWxpdHlcIiwgXCJEYXRhYmFzZVwiLCBcIkNvbW11bmljYXRpb25cIl1cblxuY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICBjYXNlIFwiaW5zdGFsbGVkXCI6XG4gICAgICByZXR1cm4gXCJiZy1ncmVlbi01MDBcIlxuICAgIGNhc2UgXCJ1cGRhdGluZ1wiOlxuICAgICAgcmV0dXJuIFwiYmcteWVsbG93LTUwMFwiXG4gICAgY2FzZSBcImF2YWlsYWJsZVwiOlxuICAgICAgcmV0dXJuIFwiYmctZ3JheS01MDBcIlxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gXCJiZy1ncmF5LTUwMFwiXG4gIH1cbn1cblxuY29uc3QgZ2V0U3RhdHVzVGV4dCA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICBzd2l0Y2ggKHN0YXR1cykge1xuICAgIGNhc2UgXCJpbnN0YWxsZWRcIjpcbiAgICAgIHJldHVybiBcIkluc3RhbGxlZFwiXG4gICAgY2FzZSBcInVwZGF0aW5nXCI6XG4gICAgICByZXR1cm4gXCJVcGRhdGluZ1wiXG4gICAgY2FzZSBcImF2YWlsYWJsZVwiOlxuICAgICAgcmV0dXJuIFwiQXZhaWxhYmxlXCJcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIFwiVW5rbm93blwiXG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWNwTGlicmFyeVRhYigpIHtcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZShcIlwiKVxuICBjb25zdCBbc2VsZWN0ZWRDYXRlZ29yeSwgc2V0U2VsZWN0ZWRDYXRlZ29yeV0gPSB1c2VTdGF0ZShcIkFsbFwiKVxuICBjb25zdCBbbWNwU2VydmVycywgc2V0TWNwU2VydmVyc10gPSB1c2VTdGF0ZTxNY3BTZXJ2ZXJbXT4oZ2V0SW5pdGlhbE1jcFNlcnZlcnMoKSlcbiAgY29uc3QgW21jcEluZm8sIHNldE1jcEluZm9dID0gdXNlU3RhdGU8TUNQSW5mbyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFthZGRTZXJ2ZXJEaWFsb2csIHNldEFkZFNlcnZlckRpYWxvZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW25ld1NlcnZlciwgc2V0TmV3U2VydmVyXSA9IHVzZVN0YXRlKHtcbiAgICBuYW1lOiBcIlwiLFxuICAgIGNvbW1hbmQ6IFwiXCIsXG4gICAgYXJnczogXCJcIixcbiAgICBkZXNjcmlwdGlvbjogXCJcIixcbiAgICBjYXRlZ29yeTogXCJEZXZlbG9wbWVudFwiXG4gIH0pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkTWNwSW5mbyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGluZm8gPSBhd2FpdCBhcGlDbGllbnQuZ2V0TUNQSW5mbygpXG4gICAgICAgIHNldE1jcEluZm8oaW5mbylcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIE1DUCBpbmZvOicsIGVycm9yKVxuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICBsb2FkTWNwSW5mbygpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IGZpbHRlcmVkU2VydmVycyA9IG1jcFNlcnZlcnMuZmlsdGVyKChzZXJ2ZXIpID0+IHtcbiAgICBjb25zdCBtYXRjaGVzU2VhcmNoID1cbiAgICAgIHNlcnZlci5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgIHNlcnZlci5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICBzZXJ2ZXIudGFncy5zb21lKCh0YWcpID0+IHRhZy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpKVxuXG4gICAgY29uc3QgbWF0Y2hlc0NhdGVnb3J5ID0gc2VsZWN0ZWRDYXRlZ29yeSA9PT0gXCJBbGxcIiB8fCBzZXJ2ZXIuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnlcblxuICAgIHJldHVybiBtYXRjaGVzU2VhcmNoICYmIG1hdGNoZXNDYXRlZ29yeVxuICB9KVxuXG4gIGNvbnN0IGhhbmRsZUluc3RhbGwgPSBhc3luYyAoc2VydmVySWQ6IHN0cmluZywgc2VydmVyTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coYEluc3RhbGxpbmcgTUNQIHNlcnZlcjogJHtzZXJ2ZXJOYW1lfSAoSUQ6ICR7c2VydmVySWR9KWApXG5cbiAgICAvLyBVcGRhdGUgc2VydmVyIHN0YXR1cyB0byBpbnN0YWxsaW5nXG4gICAgc2V0TWNwU2VydmVycyhwcmV2ID0+IHByZXYubWFwKHNlcnZlciA9PlxuICAgICAgc2VydmVyLmlkID09PSBzZXJ2ZXJJZFxuICAgICAgICA/IHsgLi4uc2VydmVyLCBzdGF0dXM6IFwidXBkYXRpbmdcIiBhcyBjb25zdCB9XG4gICAgICAgIDogc2VydmVyXG4gICAgKSlcblxuICAgIC8vIFNpbXVsYXRlIGluc3RhbGxhdGlvbiBwcm9jZXNzXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBzZXRNY3BTZXJ2ZXJzKHByZXYgPT4gcHJldi5tYXAoc2VydmVyID0+XG4gICAgICAgIHNlcnZlci5pZCA9PT0gc2VydmVySWRcbiAgICAgICAgICA/IHsgLi4uc2VydmVyLCBzdGF0dXM6IFwiaW5zdGFsbGVkXCIgYXMgY29uc3QsIGxhc3RVcGRhdGVkOiBcIkp1c3Qgbm93XCIgfVxuICAgICAgICAgIDogc2VydmVyXG4gICAgICApKVxuICAgIH0sIDIwMDApXG4gIH1cblxuICBjb25zdCBoYW5kbGVVbmluc3RhbGwgPSBhc3luYyAoc2VydmVySWQ6IHN0cmluZywgc2VydmVyTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coYFVuaW5zdGFsbGluZyBNQ1Agc2VydmVyOiAke3NlcnZlck5hbWV9IChJRDogJHtzZXJ2ZXJJZH0pYClcblxuICAgIHNldE1jcFNlcnZlcnMocHJldiA9PiBwcmV2Lm1hcChzZXJ2ZXIgPT5cbiAgICAgIHNlcnZlci5pZCA9PT0gc2VydmVySWRcbiAgICAgICAgPyB7IC4uLnNlcnZlciwgc3RhdHVzOiBcImF2YWlsYWJsZVwiIGFzIGNvbnN0LCBsYXN0VXBkYXRlZDogXCJKdXN0IG5vd1wiIH1cbiAgICAgICAgOiBzZXJ2ZXJcbiAgICApKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2VydmVyU2V0dGluZ3MgPSAoc2VydmVySWQ6IHN0cmluZywgc2VydmVyTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coYE9wZW5pbmcgc2V0dGluZ3MgZm9yIE1DUCBzZXJ2ZXI6ICR7c2VydmVyTmFtZX0gKElEOiAke3NlcnZlcklkfSlgKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQWRkU2VydmVyID0gKCkgPT4ge1xuICAgIGlmICghbmV3U2VydmVyLm5hbWUgfHwgIW5ld1NlcnZlci5jb21tYW5kKSByZXR1cm5cblxuICAgIGNvbnN0IHNlcnZlcjogTWNwU2VydmVyID0ge1xuICAgICAgaWQ6IGBjdXN0b20tJHtEYXRlLm5vdygpfWAsXG4gICAgICBuYW1lOiBuZXdTZXJ2ZXIubmFtZSxcbiAgICAgIGRlc2NyaXB0aW9uOiBuZXdTZXJ2ZXIuZGVzY3JpcHRpb24gfHwgYEN1c3RvbSBNQ1Agc2VydmVyOiAke25ld1NlcnZlci5uYW1lfWAsXG4gICAgICBhdXRob3I6IFwiQ3VzdG9tXCIsXG4gICAgICB2ZXJzaW9uOiBcIjEuMC4wXCIsXG4gICAgICBjYXRlZ29yeTogbmV3U2VydmVyLmNhdGVnb3J5LFxuICAgICAgaWNvbjogPFBhY2thZ2UgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+LFxuICAgICAgcmF0aW5nOiA0LjAsXG4gICAgICBkb3dubG9hZHM6IDAsXG4gICAgICB0YWdzOiBbXCJDdXN0b21cIiwgbmV3U2VydmVyLmNhdGVnb3J5XSxcbiAgICAgIHN0YXR1czogXCJhdmFpbGFibGVcIixcbiAgICAgIGdyYWRpZW50OiBcImZyb20tZ3JheS01MDAgdG8tZ3JheS02MDBcIixcbiAgICAgIGxhc3RVcGRhdGVkOiBcIkp1c3QgYWRkZWRcIlxuICAgIH1cblxuICAgIHNldE1jcFNlcnZlcnMocHJldiA9PiBbLi4ucHJldiwgc2VydmVyXSlcbiAgICBzZXROZXdTZXJ2ZXIoeyBuYW1lOiBcIlwiLCBjb21tYW5kOiBcIlwiLCBhcmdzOiBcIlwiLCBkZXNjcmlwdGlvbjogXCJcIiwgY2F0ZWdvcnk6IFwiRGV2ZWxvcG1lbnRcIiB9KVxuICAgIHNldEFkZFNlcnZlckRpYWxvZyhmYWxzZSlcbiAgfVxuXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yTmV3ID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgXCJpbnN0YWxsZWRcIjpcbiAgICAgICAgcmV0dXJuIFwiYmctZ3JlZW4tNTAwXCJcbiAgICAgIGNhc2UgXCJ1cGRhdGluZ1wiOlxuICAgICAgICByZXR1cm4gXCJiZy15ZWxsb3ctNTAwXCJcbiAgICAgIGNhc2UgXCJhdmFpbGFibGVcIjpcbiAgICAgICAgcmV0dXJuIFwiYmctbmV1dHJhbC01MDBcIlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIFwiYmctbmV1dHJhbC01MDBcIlxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFN0YXR1c1RleHROZXcgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSBcImluc3RhbGxlZFwiOlxuICAgICAgICByZXR1cm4gXCJJbnN0YWxsZWRcIlxuICAgICAgY2FzZSBcInVwZGF0aW5nXCI6XG4gICAgICAgIHJldHVybiBcIlVwZGF0aW5nXCJcbiAgICAgIGNhc2UgXCJhdmFpbGFibGVcIjpcbiAgICAgICAgcmV0dXJuIFwiQXZhaWxhYmxlXCJcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBcIlVua25vd25cIlxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTgwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPk1DUCBMaWJyYXJ5PC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1uZXV0cmFsLTQwMFwiPk1vZGVsIENvbnRleHQgUHJvdG9jb2wgc2VydmVycyBhbmQgaW50ZWdyYXRpb25zPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICB7ZmlsdGVyZWRTZXJ2ZXJzLmxlbmd0aH0gb2Yge21jcFNlcnZlcnMubGVuZ3RofVxuICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgIHttY3BJbmZvICYmIChcbiAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT17YCR7bWNwSW5mby5tY3BfZW5hYmxlZCA/ICdiZy1ncmVlbi01MDAvMTAgdGV4dC1ncmVlbi00MDAgYm9yZGVyLWdyZWVuLTUwMC8yMCcgOiAnYmctcmVkLTUwMC8xMCB0ZXh0LXJlZC00MDAgYm9yZGVyLXJlZC01MDAvMjAnfWB9PlxuICAgICAgICAgICAgICAgIE1DUCB7bWNwSW5mby5tY3BfZW5hYmxlZCA/ICdFbmFibGVkJyA6ICdEaXNhYmxlZCd9XG4gICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgPERpYWxvZyBvcGVuPXthZGRTZXJ2ZXJEaWFsb2d9IG9uT3BlbkNoYW5nZT17c2V0QWRkU2VydmVyRGlhbG9nfT5cbiAgICAgICAgICAgICAgPERpYWxvZ1RyaWdnZXIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cImJnLWJsdWUtZ3JhZGllbnQtaG92ZXIgdGV4dC13aGl0ZSBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBBZGQgU2VydmVyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvRGlhbG9nVHJpZ2dlcj5cbiAgICAgICAgICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwiYmctbmV1dHJhbC05MDAgYm9yZGVyLW5ldXRyYWwtODAwXCI+XG4gICAgICAgICAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+QWRkIEN1c3RvbSBNQ1AgU2VydmVyPC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgICAgICAgIDxEaWFsb2dEZXNjcmlwdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LW5ldXRyYWwtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgIEFkZCBhIGN1c3RvbSBNQ1Agc2VydmVyIHRvIHlvdXIgbGlicmFyeS4gWW91IGNhbiBpbnN0YWxsIGNvbW11bml0eSBzZXJ2ZXJzIG9yIGNyZWF0ZSB5b3VyIG93bi5cbiAgICAgICAgICAgICAgICAgIDwvRGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibmFtZVwiIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj5TZXJ2ZXIgTmFtZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld1NlcnZlci5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3U2VydmVyKHByZXYgPT4gKHsgLi4ucHJldiwgbmFtZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgTXkgQ3VzdG9tIFNlcnZlclwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctbmV1dHJhbC04MDAgYm9yZGVyLW5ldXRyYWwtNzAwIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbW1hbmRcIiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+Q29tbWFuZDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY29tbWFuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld1NlcnZlci5jb21tYW5kfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3U2VydmVyKHByZXYgPT4gKHsgLi4ucHJldiwgY29tbWFuZDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgbnB4LCBweXRob24sIG5vZGVcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLW5ldXRyYWwtODAwIGJvcmRlci1uZXV0cmFsLTcwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJhcmdzXCIgY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiPkFyZ3VtZW50czwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiYXJnc1wiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld1NlcnZlci5hcmdzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3U2VydmVyKHByZXYgPT4gKHsgLi4ucHJldiwgYXJnczogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgLXkgQG15L21jcC1zZXJ2ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLW5ldXRyYWwtODAwIGJvcmRlci1uZXV0cmFsLTcwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJkZXNjcmlwdGlvblwiIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj5EZXNjcmlwdGlvbjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdTZXJ2ZXIuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdTZXJ2ZXIocHJldiA9PiAoeyAuLi5wcmV2LCBkZXNjcmlwdGlvbjogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGVzY3JpYmUgd2hhdCB0aGlzIHNlcnZlciBkb2VzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1uZXV0cmFsLTgwMCBib3JkZXItbmV1dHJhbC03MDAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gc2V0QWRkU2VydmVyRGlhbG9nKGZhbHNlKX0gY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUFkZFNlcnZlcn0gY2xhc3NOYW1lPVwiZmxleC0xIGJnLWJsdWUtZ3JhZGllbnQtaG92ZXIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIEFkZCBTZXJ2ZXJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgICAgICAgICAgPC9EaWFsb2c+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTZWFyY2ggYW5kIEZpbHRlcnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggTUNQIHNlcnZlcnMuLi5cIlxuICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCBiZy1uZXV0cmFsLTkwMCBib3JkZXItbmV1dHJhbC04MDAgdGV4dC13aGl0ZSBwbGFjZWhvbGRlcjp0ZXh0LW5ldXRyYWwtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMC80MFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICB7Y2F0ZWdvcmllcy5tYXAoKGNhdGVnb3J5KSA9PiAoXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2NhdGVnb3J5fVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZENhdGVnb3J5KGNhdGVnb3J5KX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake1xuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXRlZ29yeSA9PT0gY2F0ZWdvcnlcbiAgICAgICAgICAgICAgICAgICAgPyBcImJvcmRlci1ibHVlLTUwMC8yMCB0ZXh0LWJsdWUtNDAwIGhvdmVyOmJnLWJsdWUtNTAwLzEwIGhvdmVyOnRleHQtYmx1ZS0zMDBcIlxuICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLWdyYXktODAwLzUwXCJcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtjYXRlZ29yeX1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1DUCBTZXJ2ZXJzIEdyaWQgKi99XG4gICAgICA8U2Nyb2xsQXJlYSBjbGFzc05hbWU9XCJmbGV4LTEgcC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgIHtmaWx0ZXJlZFNlcnZlcnMubWFwKChzZXJ2ZXIpID0+IChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXtzZXJ2ZXIuaWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIHJlbGF0aXZlIGJnLW5ldXRyYWwtOTAwIGJvcmRlciBib3JkZXItbmV1dHJhbC04MDAgcm91bmRlZC14bCBjYXJkLWhvdmVyLWJsdWUgcC01IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHsvKiBTZXR0aW5ncyBHZWFyIEljb24gKi99XG4gICAgICAgICAgICAgIHtzZXJ2ZXIuc3RhdHVzID09PSBcImluc3RhbGxlZFwiICYmIChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZVNlcnZlclNldHRpbmdzKHNlcnZlci5pZCwgc2VydmVyLm5hbWUpXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgcmlnaHQtNCB3LTcgaC03IHJvdW5kZWQtbGcgYmctbmV1dHJhbC05MDAvNzAgaG92ZXI6YmctbmV1dHJhbC04MDAvNzAgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTcwMC81MCBob3Zlcjpib3JkZXItbmV1dHJhbC02MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB6LTEwXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPXtgU2V0dGluZ3MgZm9yICR7c2VydmVyLm5hbWV9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwiaC0zLjUgdy0zLjUgdGV4dC1uZXV0cmFsLTQwMCBob3Zlcjp0ZXh0LW5ldXRyYWwtMzAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7LyogU2VydmVyIEhlYWRlciAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC00IG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTEyIGgtMTIgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1iciAke3NlcnZlci5ncmFkaWVudH0gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC13aGl0ZSBmbGV4LXNocmluay0wYH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7c2VydmVyLmljb259XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wIHByLThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIHRydW5jYXRlXCI+e3NlcnZlci5uYW1lfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMiBoLTIgcm91bmRlZC1mdWxsICR7Z2V0U3RhdHVzQ29sb3JOZXcoc2VydmVyLnN0YXR1cyl9YH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbmV1dHJhbC00MDBcIj57Z2V0U3RhdHVzVGV4dE5ldyhzZXJ2ZXIuc3RhdHVzKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbmV1dHJhbC00MDBcIj57c2VydmVyLmNhdGVnb3J5fTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj52e3NlcnZlci52ZXJzaW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5ieSB7c2VydmVyLmF1dGhvcn08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxTdGFyIGNsYXNzTmFtZT1cImgtMyB3LTMgZmlsbC15ZWxsb3ctNTAwIHRleHQteWVsbG93LTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3NlcnZlci5yYXRpbmd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57c2VydmVyLmRvd25sb2Fkcy50b0xvY2FsZVN0cmluZygpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPlVwZGF0ZWQge3NlcnZlci5sYXN0VXBkYXRlZH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIERlc2NyaXB0aW9uICovfVxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbmV1dHJhbC00MDAgbWItNCBsZWFkaW5nLXJlbGF4ZWRcIj57c2VydmVyLmRlc2NyaXB0aW9ufTwvcD5cblxuICAgICAgICAgICAgICB7LyogVGFncyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMiBtYi00XCI+XG4gICAgICAgICAgICAgICAge3NlcnZlci50YWdzLm1hcCgodGFnKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8QmFkZ2VcbiAgICAgICAgICAgICAgICAgICAga2V5PXt0YWd9XG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1uZXV0cmFsLTgwMCB0ZXh0LW5ldXRyYWwtNDAwIGJvcmRlci1uZXV0cmFsLTcwMCB0ZXh0LXhzIHB4LTIgcHktMSBob3ZlcjpiZy1ncmF5LTcwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHt0YWd9XG4gICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAge3NlcnZlci5zdGF0dXMgPT09IFwiaW5zdGFsbGVkXCIgPyAoXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVVuaW5zdGFsbChzZXJ2ZXIuaWQsIHNlcnZlci5uYW1lKX1cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYm9yZGVyLW5ldXRyYWwtNzAwIHRleHQtbmV1dHJhbC0zMDAgaG92ZXI6YmctbmV1dHJhbC04MDAgaG92ZXI6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICBJbnN0YWxsZWRcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICkgOiBzZXJ2ZXIuc3RhdHVzID09PSBcInVwZGF0aW5nXCIgPyAoXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uIGRpc2FibGVkIGNsYXNzTmFtZT1cImZsZXgtMSBiZy15ZWxsb3ctNjAwLzIwIHRleHQteWVsbG93LTQwMCBib3JkZXIgYm9yZGVyLXllbGxvdy02MDAvMzBcIj5cbiAgICAgICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgIFVwZGF0aW5nLi4uXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVJbnN0YWxsKHNlcnZlci5pZCwgc2VydmVyLm5hbWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgYmctYmx1ZS1ncmFkaWVudC1ob3ZlciB0ZXh0LXdoaXRlIHNoYWRvdy1sZyBib3JkZXItMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZ3JvdXAtaG92ZXI6c2hhZG93LWxnYH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIEluc3RhbGxcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBIb3ZlciBFZmZlY3QgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCByb3VuZGVkLXhsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS10cmFuc3BhcmVudCB2aWEtd2hpdGUvNSB0by10cmFuc3BhcmVudCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMCBwb2ludGVyLWV2ZW50cy1ub25lXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7ZmlsdGVyZWRTZXJ2ZXJzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTY0XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNTAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS00MDAgbWItMlwiPk5vIE1DUCBzZXJ2ZXJzIGZvdW5kPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG1iLTRcIj5UcnkgYWRqdXN0aW5nIHlvdXIgc2VhcmNoIG9yIGZpbHRlciBjcml0ZXJpYTwvcD5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cub3BlbignaHR0cHM6Ly9naXRodWIuY29tL21vZGVsY29udGV4dHByb3RvY29sL3NlcnZlcnMnLCAnX2JsYW5rJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWJsdWUtNTAwLzIwIHRleHQtYmx1ZS00MDAgaG92ZXI6YmctYmx1ZS01MDAvMTBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEV4dGVybmFsTGluayBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIEJyb3dzZSBDb21tdW5pdHkgU2VydmVyc1xuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBDb21tdW5pdHkgUmVzb3VyY2VzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggcC00IGJnLW5ldXRyYWwtODAwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTcwMFwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIG1iLTJcIj5EaXNjb3ZlciBNb3JlIE1DUCBTZXJ2ZXJzPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW5ldXRyYWwtNDAwIHRleHQtc20gbWItM1wiPlxuICAgICAgICAgICAgRXhwbG9yZSB0aGUgZ3Jvd2luZyBlY29zeXN0ZW0gb2YgTW9kZWwgQ29udGV4dCBQcm90b2NvbCBzZXJ2ZXJzIGZyb20gdGhlIGNvbW11bml0eS5cbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMlwiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKCdodHRwczovL2dpdGh1Yi5jb20vbW9kZWxjb250ZXh0cHJvdG9jb2wvc2VydmVycycsICdfYmxhbmsnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWJsdWUtNTAwLzIwIHRleHQtYmx1ZS00MDAgaG92ZXI6YmctYmx1ZS01MDAvMTBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8RXh0ZXJuYWxMaW5rIGNsYXNzTmFtZT1cImgtMyB3LTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgIE9mZmljaWFsIFNlcnZlcnNcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKCdodHRwczovL21jcHNlcnZlcnMub3JnJywgJ19ibGFuaycpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JlZW4tNTAwLzIwIHRleHQtZ3JlZW4tNDAwIGhvdmVyOmJnLWdyZWVuLTUwMC8xMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxFeHRlcm5hbExpbmsgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgQ29tbXVuaXR5IERpcmVjdG9yeVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oJ2h0dHBzOi8vbW9kZWxjb250ZXh0cHJvdG9jb2wuaW8nLCAnX2JsYW5rJyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1wdXJwbGUtNTAwLzIwIHRleHQtcHVycGxlLTQwMCBob3ZlcjpiZy1wdXJwbGUtNTAwLzEwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEV4dGVybmFsTGluayBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICBEb2N1bWVudGF0aW9uXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L1Njcm9sbEFyZWE+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkJ1dHRvbiIsIlNjcm9sbEFyZWEiLCJCYWRnZSIsIklucHV0IiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJEaWFsb2dUcmlnZ2VyIiwiVGV4dGFyZWEiLCJMYWJlbCIsIkdsb2JlIiwiRmlsZVRleHQiLCJDb2RlIiwiU2VhcmNoIiwiRG93bmxvYWQiLCJDaGVja0NpcmNsZSIsIlN0YXIiLCJTZXR0aW5ncyIsIlBhY2thZ2UiLCJQbHVzIiwiQnJhaW4iLCJaYXAiLCJFeHRlcm5hbExpbmsiLCJhcGlDbGllbnQiLCJnZXRJbml0aWFsTWNwU2VydmVycyIsImlkIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiYXV0aG9yIiwidmVyc2lvbiIsImNhdGVnb3J5IiwiaWNvbiIsImNsYXNzTmFtZSIsInJhdGluZyIsImRvd25sb2FkcyIsInRhZ3MiLCJzdGF0dXMiLCJncmFkaWVudCIsImxhc3RVcGRhdGVkIiwiY2F0ZWdvcmllcyIsImdldFN0YXR1c0NvbG9yIiwiZ2V0U3RhdHVzVGV4dCIsIk1jcExpYnJhcnlUYWIiLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5Iiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJtY3BTZXJ2ZXJzIiwic2V0TWNwU2VydmVycyIsIm1jcEluZm8iLCJzZXRNY3BJbmZvIiwibG9hZGluZyIsInNldExvYWRpbmciLCJhZGRTZXJ2ZXJEaWFsb2ciLCJzZXRBZGRTZXJ2ZXJEaWFsb2ciLCJuZXdTZXJ2ZXIiLCJzZXROZXdTZXJ2ZXIiLCJjb21tYW5kIiwiYXJncyIsImxvYWRNY3BJbmZvIiwiaW5mbyIsImdldE1DUEluZm8iLCJlcnJvciIsImNvbnNvbGUiLCJmaWx0ZXJlZFNlcnZlcnMiLCJmaWx0ZXIiLCJzZXJ2ZXIiLCJtYXRjaGVzU2VhcmNoIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInNvbWUiLCJ0YWciLCJtYXRjaGVzQ2F0ZWdvcnkiLCJoYW5kbGVJbnN0YWxsIiwic2VydmVySWQiLCJzZXJ2ZXJOYW1lIiwibG9nIiwicHJldiIsIm1hcCIsInNldFRpbWVvdXQiLCJoYW5kbGVVbmluc3RhbGwiLCJoYW5kbGVTZXJ2ZXJTZXR0aW5ncyIsImhhbmRsZUFkZFNlcnZlciIsIkRhdGUiLCJub3ciLCJnZXRTdGF0dXNDb2xvck5ldyIsImdldFN0YXR1c1RleHROZXciLCJkaXYiLCJoMSIsInAiLCJ2YXJpYW50IiwibGVuZ3RoIiwibWNwX2VuYWJsZWQiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwiYXNDaGlsZCIsInNpemUiLCJodG1sRm9yIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsIm9uQ2xpY2siLCJidXR0b24iLCJzdG9wUHJvcGFnYXRpb24iLCJ0aXRsZSIsImgzIiwic3BhbiIsInRvTG9jYWxlU3RyaW5nIiwiZGlzYWJsZWQiLCJ3aW5kb3ciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./mcp-library-tab.tsx\n"));

/***/ })

});