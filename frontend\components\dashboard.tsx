"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Activity, 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  Server, 
  Zap, 
  FileText,
  Terminal,
  Globe,
  Calculator,
  Refresh
} from "lucide-react"
import { apiClient, type TaskResponse, type HealthResponse, type MCPInfo } from "@/lib/api"

export default function Dashboard() {
  const [health, setHealth] = useState<HealthResponse | null>(null)
  const [tasks, setTasks] = useState<TaskResponse[]>([])
  const [mcpInfo, setMCPInfo] = useState<MCPInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      const [healthData, tasksData] = await Promise.all([
        apiClient.getHealth(),
        apiClient.getTasks()
      ])
      
      setHealth(healthData)
      setTasks(tasksData)
      
      // Try to get MCP info (might not be available)
      try {
        const mcpData = await apiClient.getMCPInfo()
        setMCPInfo(mcpData)
      } catch (error) {
        console.log('MCP info not available:', error)
      }
      
      setLastUpdate(new Date())
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadDashboardData()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Activity className="h-4 w-4 text-blue-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/10 text-green-400 border-green-500/20'
      case 'failed':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      case 'processing':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      default:
        return 'bg-blue-500/10 text-blue-400 border-blue-500/20'
    }
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">System Dashboard</h1>
          <p className="text-neutral-400">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </p>
        </div>
        <Button 
          onClick={loadDashboardData} 
          disabled={loading}
          variant="outline"
          size="sm"
        >
          <Refresh className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">API Status</CardTitle>
            <Server className="h-4 w-4 text-neutral-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {health?.status === 'healthy' ? 'Online' : 'Offline'}
            </div>
            <p className="text-xs text-neutral-500">
              {health ? 'Connected' : 'Disconnected'}
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Total Tasks</CardTitle>
            <Activity className="h-4 w-4 text-neutral-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{tasks.length}</div>
            <p className="text-xs text-neutral-500">
              {tasks.filter(t => t.status === 'processing').length} active
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-neutral-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {tasks.length > 0 
                ? Math.round((tasks.filter(t => t.status === 'completed').length / tasks.length) * 100)
                : 0}%
            </div>
            <p className="text-xs text-neutral-500">
              {tasks.filter(t => t.status === 'completed').length} completed
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neutral-900 border-neutral-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">MCP Status</CardTitle>
            <Zap className="h-4 w-4 text-neutral-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {mcpInfo?.mcp_enabled ? 'Enabled' : 'Disabled'}
            </div>
            <p className="text-xs text-neutral-500">
              {mcpInfo?.protocol_version || 'Not available'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="tasks" className="space-y-4">
        <TabsList className="bg-neutral-900 border-blue-500/20">
          <TabsTrigger 
            value="tasks" 
            className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400"
          >
            Recent Tasks
          </TabsTrigger>
          <TabsTrigger 
            value="mcp" 
            className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400"
          >
            MCP Integration
          </TabsTrigger>
          <TabsTrigger 
            value="tools" 
            className="text-neutral-400 data-[state=active]:bg-blue-500/10 data-[state=active]:text-blue-400"
          >
            Available Tools
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tasks" className="space-y-4">
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">Recent Tasks</CardTitle>
              <CardDescription>Latest agent tasks and their status</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <div className="space-y-3">
                  {tasks.length === 0 ? (
                    <p className="text-neutral-500 text-center py-8">No tasks yet</p>
                  ) : (
                    tasks.slice(0, 20).map((task) => (
                      <div key={task.task_id} className="flex items-center justify-between p-3 bg-neutral-800 rounded-lg">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(task.status)}
                          <div>
                            <p className="text-white text-sm font-medium">
                              {task.query.slice(0, 60)}...
                            </p>
                            <p className="text-neutral-500 text-xs">
                              {formatDate(task.created_at)} at {formatTime(task.created_at)}
                            </p>
                          </div>
                        </div>
                        <Badge className={getStatusColor(task.status)}>
                          {task.status}
                        </Badge>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mcp" className="space-y-4">
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader>
              <CardTitle className="text-white">MCP Integration Status</CardTitle>
              <CardDescription>Model Context Protocol configuration and status</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {mcpInfo ? (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-white">Server Name</p>
                      <p className="text-neutral-400">{mcpInfo.server_name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">Protocol Version</p>
                      <p className="text-neutral-400">{mcpInfo.protocol_version}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">Transport</p>
                      <p className="text-neutral-400">{mcpInfo.transport}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">Status</p>
                      <Badge className="bg-green-500/10 text-green-400 border-green-500/20">
                        {mcpInfo.mcp_enabled ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-white mb-2">Available Endpoints</p>
                    <div className="space-y-2">
                      {Object.entries(mcpInfo.endpoints).map(([key, value]) => (
                        <div key={key} className="flex justify-between items-center p-2 bg-neutral-800 rounded">
                          <span className="text-neutral-300">{key}</span>
                          <span className="text-neutral-500 text-sm">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              ) : (
                <p className="text-neutral-500">MCP information not available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tools" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { name: "File Operations", icon: FileText, description: "Read, write, create, delete files and directories" },
              { name: "Terminal Commands", icon: Terminal, description: "Execute shell commands and scripts" },
              { name: "Web Search", icon: Globe, description: "Search the web for information" },
              { name: "Calculator", icon: Calculator, description: "Perform mathematical calculations" },
              { name: "Web Browsing", icon: Globe, description: "Navigate websites with Playwright" },
              { name: "System Info", icon: Server, description: "Get system and process information" }
            ].map((tool) => (
              <Card key={tool.name} className="bg-neutral-900 border-neutral-800">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <tool.icon className="h-5 w-5 text-blue-400" />
                    {tool.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-neutral-400 text-sm">{tool.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
