/* Global Variables for Color Scheme */
:root {
    --primary-black: #000000;
    --primary-charcoal: #1a1a1a;
    --secondary-charcoal: #2d2d2d;
    --dark-grey: #3a3a3a;
    --medium-grey: #555555;
    --light-grey: #888888;
    --very-light-grey: #cccccc;
    --primary-blue: #005dc8;
    --bright-blue: #007bff;
    --electric-blue: #00a2ff;
    --neon-blue: #0099cc;
    --soft-blue: #6c757d;
    --glow-blue: rgba(0, 157, 255, 0.8);
    --text-white: #ffffff;
    --text-light: #e0e0e0;
}

/* Base Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-charcoal) 50%, var(--primary-charcoal) 100%);
    color: var(--text-white);
    min-height: 100vh;
    padding: 20px;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    padding: 30px 0;
    background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 157, 255, 0.3);
}

header h1 {
    font-family: 'Orbitron', monospace;
    font-weight: 900;
    font-size: 2.5rem;
    color: var(--electric-blue);
    text-shadow: 0 0 10px var(--glow-blue);
    margin-bottom: 10px;
}

.current-year {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    color: var(--primary-blue);
    font-weight: 700;
}

/* Month Navigation */
.month-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    background: var(--secondary-charcoal);
    border-radius: 25px;
    padding: 15px 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    border: 1px solid var(--primary-blue);
}

.month-selector h2 {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    color: var(--electric-blue);
    margin: 0 25px;
    text-shadow: 0 0 5px var(--glow-blue);
}

.nav-btn {
    background: linear-gradient(45deg, var(--primary-blue), var(--bright-blue));
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: 0 2px 10px rgba(0, 157, 255, 0.4);
}

.nav-btn:hover {
    background: linear-gradient(45deg, var(--electric-blue), var(--neon-blue));
    transform: scale(1.1);
    box-shadow: 0 0 15px var(--glow-blue);
}

.nav-btn:active {
    transform: scale(0.95);
}

/* Calendar Structure */
.calendar {
    background: var(--secondary-charcoal);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 40px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border: 2px solid var(--primary-blue);
}

.weekday-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 15px;
}

.weekday-header div {
    text-align: center;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    color: var(--electric-blue);
    padding: 15px 8px;
    background: var(--primary-charcoal);
    border-radius: 8px;
    border-bottom: 2px solid var(--primary-blue);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 3px;
    min-height: 400px;
}

/* Calendar Day Cells */
.calendar-day {
    background: var(--primary-charcoal);
    border: 1px solid var(--dark-grey);
    border-radius: 10px;
    padding: 12px 8px;
    min-height: 90px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.calendar-day:hover {
    background: var(--secondary-charcoal);
    border-color: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 157, 255, 0.3);
}

.calendar-day.selected {
    background: var(--primary-blue);
    border-color: var(--electric-blue);
    box-shadow: 0 0 20px var(--glow-blue);
    transform: scale(1.05);
}

.calendar-day.active {
    background: var(--electric-blue);
    color: var(--primary-black);
}

.date-number {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--text-white);
}

.event-icons {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.event-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin: 1px 0;
    animation: pulse 2s infinite;
}

.major-event { background: var(--electric-blue); }
.pump-event { background: #22c55e; }
.dip-event { background: #ef4444; }
.listing { background: #a855f7; }

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.calendar-day.other-month {
    opacity: 0.4;
    background: var(--dark-grey);
}

.calendar-day.other-month:hover {
    opacity: 0.7;
    background: var(--medium-grey);
}

/* Legend Section */
.legend {
    background: var(--secondary-charcoal);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--medium-grey);
}

.legend h3 {
    color: var(--electric-blue);
    font-family: 'Orbitron', monospace;
    margin-bottom: 15px;
    text-align: center;
}

.legend-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* Event Details Panel */
.event-details {
    background: var(--secondary-charcoal);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--primary-blue);
    max-width: 600px;
    margin: 0 auto;
}

.event-details h3 {
    color: var(--electric-blue);
    font-family: 'Orbitron', monospace;
    margin-bottom: 15px;
    text-align: center;
}

#selectedDate {
    text-align: center;
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.event-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.event-item {
    background: var(--primary-charcoal);
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid var(--electric-blue);
    color: var(--text-light);
}

.event-item.major { border-left-color: var(--electric-blue); }
.event-item.pump { border-left-color: #22c55e; }
.event-item.dip { border-left-color: #ef4444; }
.event-item.listing { border-left-color: #a855f7; }

.event-time {
    font-size: 0.8em;
    color: var(--light-grey);
    margin-bottom: 5px;
}

.event-title {
    font-weight: 500;
    margin-bottom: 5px;
}

.event-description {
    font-size: 0.9em;
    color: var(--very-light-grey);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        margin: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .calendar {
        padding: 15px;
    }
    
    .weekday-header div {
        font-size: 0.8rem;
        padding: 8px 4px;
    }
    
    .calendar-day {
        padding: 8px 4px;
        min-height: 70px;
    }
    
    .legend-items {
        flex-direction: column;
        align-items: center;
    }
    
    .nav-btn {
        width: 35px;
        height: 35px;
        font-size: 1.3rem;
    }
    
    .month-selector h2 {
        font-size: 1.5rem;
        margin: 0 15px;
    }
}

@media (max-width: 480px) {
    .weekday-header div {
        font-size: 0.7rem;
        padding: 6px 2px;
    }
    
    .calendar-day {
        min-height: 60px;
    }
    
    .date-number {
        font-size: 0.9rem;
    }
    
    header h1 {
        font-size: 1.7rem;
    }
}