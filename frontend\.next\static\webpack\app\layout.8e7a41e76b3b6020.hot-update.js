"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./agents-tab.tsx":
/*!************************!*\
  !*** ./agents-tab.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AgentsTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Loader2,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst getInitialAgents = ()=>[\n        {\n            id: \"ag3nt-x-core\",\n            name: \"AG3NT X Core\",\n            description: \"Comprehensive AI agent with file operations, terminal commands, web search, calculations, and MCP integration. Your primary assistant for development and automation tasks.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Full-Stack Development\",\n            rating: 4.9,\n            users: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"MCP\",\n                \"Automation\"\n            ],\n            status: \"online\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            tools: [\n                \"read_file\",\n                \"write_file\",\n                \"run_terminal_command\",\n                \"search_web\",\n                \"calculate\"\n            ],\n            capabilities: [\n                \"File Management\",\n                \"System Operations\",\n                \"Web Research\",\n                \"Mathematical Calculations\"\n            ]\n        },\n        {\n            id: \"playwright-agent\",\n            name: \"Playwright Web Agent\",\n            description: \"Advanced web automation and scraping agent powered by Playwright. Navigate websites, take screenshots, extract content, and automate web interactions.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 47,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Web Automation\",\n            rating: 4.8,\n            users: 1,\n            tags: [\n                \"Web Scraping\",\n                \"Screenshots\",\n                \"Automation\",\n                \"Browser Control\"\n            ],\n            status: \"online\",\n            gradient: \"from-purple-500 to-pink-500\",\n            tools: [\n                \"browser_navigate\",\n                \"browser_screenshot\",\n                \"browser_click\",\n                \"browser_type\"\n            ],\n            capabilities: [\n                \"Web Navigation\",\n                \"Content Extraction\",\n                \"Form Automation\",\n                \"Visual Capture\"\n            ]\n        },\n        {\n            id: \"file-manager\",\n            name: \"File System Manager\",\n            description: \"Specialized agent for comprehensive file and directory operations. Create, read, write, move, copy, and manage files with advanced permissions and metadata handling.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"File Management\",\n            rating: 4.9,\n            users: 1,\n            tags: [\n                \"Files\",\n                \"Directories\",\n                \"CRUD\",\n                \"Permissions\"\n            ],\n            status: \"online\",\n            gradient: \"from-green-500 to-emerald-500\",\n            tools: [\n                \"create_directory\",\n                \"delete_file_or_directory\",\n                \"move_file_or_directory\",\n                \"copy_file_or_directory\"\n            ],\n            capabilities: [\n                \"File Operations\",\n                \"Directory Management\",\n                \"Permission Control\",\n                \"Metadata Access\"\n            ]\n        },\n        {\n            id: \"terminal-agent\",\n            name: \"Terminal Commander\",\n            description: \"Powerful terminal and system operations agent. Execute commands, manage processes, monitor system health, and automate shell-based workflows.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 76,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"System Operations\",\n            rating: 4.7,\n            users: 1,\n            tags: [\n                \"Terminal\",\n                \"Shell\",\n                \"Commands\",\n                \"System\"\n            ],\n            status: \"online\",\n            gradient: \"from-orange-500 to-red-500\",\n            tools: [\n                \"run_terminal_command\",\n                \"list_directory\",\n                \"get_file_info\"\n            ],\n            capabilities: [\n                \"Command Execution\",\n                \"Process Management\",\n                \"System Monitoring\",\n                \"Shell Automation\"\n            ]\n        },\n        {\n            id: \"research-agent\",\n            name: \"Research Assistant\",\n            description: \"Intelligent research agent with web search capabilities and mathematical computation. Perfect for gathering information, analyzing data, and performing calculations.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Research & Analysis\",\n            rating: 4.6,\n            users: 1,\n            tags: [\n                \"Research\",\n                \"Web Search\",\n                \"Calculations\",\n                \"Analysis\"\n            ],\n            status: \"online\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            tools: [\n                \"search_web\",\n                \"calculate\"\n            ],\n            capabilities: [\n                \"Web Research\",\n                \"Mathematical Calculations\",\n                \"Data Analysis\",\n                \"Information Gathering\"\n            ]\n        }\n    ];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"online\":\n            return \"bg-green-500\";\n        case \"busy\":\n            return \"bg-yellow-500\";\n        case \"offline\":\n            return \"bg-neutral-500\";\n        default:\n            return \"bg-neutral-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"online\":\n            return \"Online\";\n        case \"busy\":\n            return \"Busy\";\n        case \"offline\":\n            return \"Offline\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction AgentsTab() {\n    _s();\n    const [agents, setAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialAgents());\n    const [apiHealth, setApiHealth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgentsTab.useEffect\": ()=>{\n            const checkAgentStatus = {\n                \"AgentsTab.useEffect.checkAgentStatus\": async ()=>{\n                    try {\n                        const health = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.apiClient.getHealth();\n                        setApiHealth(health);\n                        // Update agent status based on API health\n                        setAgents({\n                            \"AgentsTab.useEffect.checkAgentStatus\": (prev)=>prev.map({\n                                    \"AgentsTab.useEffect.checkAgentStatus\": (agent)=>({\n                                            ...agent,\n                                            status: health.status === 'healthy' ? 'online' : 'offline'\n                                        })\n                                }[\"AgentsTab.useEffect.checkAgentStatus\"])\n                        }[\"AgentsTab.useEffect.checkAgentStatus\"]);\n                    } catch (error) {\n                        console.error('Failed to check agent status:', error);\n                        setAgents({\n                            \"AgentsTab.useEffect.checkAgentStatus\": (prev)=>prev.map({\n                                    \"AgentsTab.useEffect.checkAgentStatus\": (agent)=>({\n                                            ...agent,\n                                            status: 'offline'\n                                        })\n                                }[\"AgentsTab.useEffect.checkAgentStatus\"])\n                        }[\"AgentsTab.useEffect.checkAgentStatus\"]);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AgentsTab.useEffect.checkAgentStatus\"];\n            checkAgentStatus();\n            // Check status every 30 seconds\n            const interval = setInterval(checkAgentStatus, 30000);\n            return ({\n                \"AgentsTab.useEffect\": ()=>clearInterval(interval)\n            })[\"AgentsTab.useEffect\"];\n        }\n    }[\"AgentsTab.useEffect\"], []);\n    const handleAgentSettings = (agentId, agentName)=>{\n        console.log(\"Opening settings for \".concat(agentName, \" (ID: \").concat(agentId, \")\"));\n    };\n    const handleStartChat = (agentId, agentName)=>{\n        console.log(\"Starting chat with \".concat(agentName, \" (ID: \").concat(agentId, \")\"));\n    // This could navigate to the chat interface with a specific agent context\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 px-6 border-b border-neutral-800 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: \"AI Agents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-neutral-400\",\n                                children: \"Choose your specialized assistant\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"secondary\",\n                                className: \"bg-neutral-500/10 text-neutral-400 border-neutral-500/20\",\n                                children: [\n                                    agents.filter((a)=>a.status === 'online').length,\n                                    \" Online\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            apiHealth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                className: \"\".concat(apiHealth.status === 'healthy' ? 'bg-green-500/10 text-green-400 border-green-500/20' : 'bg-red-500/10 text-red-400 border-red-500/20'),\n                                children: [\n                                    \"API \",\n                                    apiHealth.status === 'healthy' ? 'Healthy' : 'Offline'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                    children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAgentSettings(agent.id, agent.name);\n                                    },\n                                    className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-800/50 hover:bg-neutral-700/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                    title: \"Settings for \".concat(agent.name),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-3.5 w-3.5 text-gray-400 hover:text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(agent.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                            children: agent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0 pr-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white truncate\",\n                                                            children: agent.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 rounded-full \".concat(getStatusColor(agent.status))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-neutral-400\",\n                                                                    children: getStatusText(agent.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-neutral-400 mb-2\",\n                                                    children: agent.specialty\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: agent.rating\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: agent.users.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                    children: agent.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-3\",\n                                    children: agent.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this),\n                                agent.tools && agent.tools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs font-medium text-neutral-300 mb-2\",\n                                            children: \"Available Tools:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: [\n                                                agent.tools.slice(0, 3).map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: \"bg-blue-500/10 text-blue-400 border-blue-500/20 text-xs px-2 py-1\",\n                                                        children: tool\n                                                    }, tool, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this)),\n                                                agent.tools.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    className: \"bg-neutral-700 text-neutral-400 text-xs px-2 py-1\",\n                                                    children: [\n                                                        \"+\",\n                                                        agent.tools.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>handleStartChat(agent.id, agent.name),\n                                    className: \"w-full bg-gradient-to-r \".concat(agent.gradient, \" hover:opacity-90 text-white border-0 transition-all duration-200 group-hover:shadow-lg bg-blue-gradient-hover text-white shadow-lg\"),\n                                    disabled: agent.status === \"offline\" || loading,\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Loader2_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Checking...\"\n                                        ]\n                                    }, void 0, true) : agent.status === \"offline\" ? \"Unavailable\" : \"Start Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, agent.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n_s(AgentsTab, \"I+yao7FIp7AsNQfUeBrjLXlQ+QM=\");\n_c = AgentsTab;\nvar _c;\n$RefreshReg$(_c, \"AgentsTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./agents-tab.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"edfc40587819\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZWRmYzQwNTg3ODE5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ })

});