from .base_tool import BaseTool
import asyncio
import csv
import json
import re
from typing import List, Dict, Any
from bs4 import BeautifulSoup
import requests

class WebScraperTool(BaseTool):
    def __init__(self, config: dict):
        self.config = config
    
    @property
    def name(self) -> str:
        return "scrape_website"
    
    @property
    def description(self) -> str:
        return "Scrape data from websites and extract structured information. Can handle both static and dynamic content."
    
    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string",
                    "description": "URL of the website to scrape"
                },
                "data_type": {
                    "type": "string",
                    "description": "Type of data to extract (e.g., 'businesses', 'contacts', 'products', 'general')",
                    "default": "general"
                },
                "output_format": {
                    "type": "string",
                    "description": "Output format: 'csv', 'json', or 'text'",
                    "default": "csv"
                },
                "output_file": {
                    "type": "string",
                    "description": "Output file name (optional)",
                    "default": "scraped_data"
                },
                "selectors": {
                    "type": "object",
                    "description": "CSS selectors for specific data extraction (optional)",
                    "default": {}
                }
            },
            "required": ["url"]
        }
    
    def execute(self, url: str, data_type: str = "general", output_format: str = "csv", 
                output_file: str = "scraped_data", selectors: Dict = None) -> str:
        """Scrape website data and save to file"""
        try:
            # First try with requests for static content
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract data based on type
            if data_type.lower() == "businesses":
                data = self._extract_business_data(soup, url)
            elif data_type.lower() == "contacts":
                data = self._extract_contact_data(soup, url)
            else:
                data = self._extract_general_data(soup, url, selectors or {})
            
            if not data:
                return "No data found on the page. The content might be loaded dynamically via JavaScript."
            
            # Save data to file
            filename = self._save_data(data, output_format, output_file)
            
            return f"Successfully scraped {len(data)} items from {url}. Data saved to {filename}"
            
        except Exception as e:
            return f"Error scraping website: {str(e)}"
    
    def _extract_business_data(self, soup: BeautifulSoup, url: str) -> List[Dict]:
        """Extract business/company information"""
        businesses = []
        
        # Common patterns for business listings
        business_selectors = [
            '[class*="business"]', '[class*="company"]', '[class*="listing"]',
            '[class*="directory"]', '[class*="card"]', '[class*="item"]',
            '.business-card', '.company-info', '.listing-item', '.directory-entry'
        ]
        
        for selector in business_selectors:
            elements = soup.select(selector)
            if elements:
                for element in elements:
                    business = self._extract_business_info(element)
                    if business and business.get('name'):
                        businesses.append(business)
                break  # Use first successful selector
        
        # If no structured data found, try to find text patterns
        if not businesses:
            businesses = self._extract_business_from_text(soup)
        
        return businesses
    
    def _extract_business_info(self, element) -> Dict:
        """Extract business information from an element"""
        business = {}
        
        # Try to find business name
        name_selectors = ['h1', 'h2', 'h3', 'h4', '.name', '.title', '.company-name', '[class*="name"]']
        for selector in name_selectors:
            name_elem = element.select_one(selector)
            if name_elem and name_elem.get_text(strip=True):
                business['name'] = name_elem.get_text(strip=True)
                break
        
        # Extract contact information
        text = element.get_text()
        
        # Phone numbers
        phone_pattern = r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})'
        phones = re.findall(phone_pattern, text)
        if phones:
            business['phone'] = phones[0]
        
        # Email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        if emails:
            business['email'] = emails[0]
        
        # Website URLs
        links = element.find_all('a', href=True)
        for link in links:
            href = link['href']
            if href.startswith('http') and 'mailto:' not in href and 'tel:' not in href:
                business['website'] = href
                break
        
        # Address (basic pattern)
        address_pattern = r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Drive|Dr|Lane|Ln|Way|Court|Ct|Place|Pl)'
        addresses = re.findall(address_pattern, text, re.IGNORECASE)
        if addresses:
            business['address'] = addresses[0]
        
        return business
    
    def _extract_business_from_text(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract business info from unstructured text"""
        businesses = []
        text = soup.get_text()
        
        # Look for phone numbers and try to find associated business names
        phone_pattern = r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})'
        phones = re.findall(phone_pattern, text)
        
        for phone in phones[:10]:  # Limit to first 10 to avoid too much data
            business = {'phone': phone}
            businesses.append(business)
        
        return businesses
    
    def _extract_contact_data(self, soup: BeautifulSoup, url: str) -> List[Dict]:
        """Extract contact information"""
        contacts = []
        text = soup.get_text()
        
        # Find all phone numbers
        phone_pattern = r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})'
        phones = re.findall(phone_pattern, text)
        
        # Find all email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        
        # Combine into contact records
        max_items = max(len(phones), len(emails))
        for i in range(max_items):
            contact = {}
            if i < len(phones):
                contact['phone'] = phones[i]
            if i < len(emails):
                contact['email'] = emails[i]
            if contact:
                contacts.append(contact)
        
        return contacts
    
    def _extract_general_data(self, soup: BeautifulSoup, url: str, selectors: Dict) -> List[Dict]:
        """Extract general data using provided selectors"""
        data = []
        
        if selectors:
            # Use custom selectors
            for key, selector in selectors.items():
                elements = soup.select(selector)
                for element in elements:
                    data.append({key: element.get_text(strip=True)})
        else:
            # Default extraction - find all text content in structured elements
            elements = soup.find_all(['div', 'span', 'p', 'li', 'td'])
            for element in elements:
                text = element.get_text(strip=True)
                if text and len(text) > 10:  # Only meaningful text
                    data.append({'content': text})
        
        return data[:100]  # Limit to first 100 items
    
    def _save_data(self, data: List[Dict], format: str, filename: str) -> str:
        """Save data to file in specified format"""
        if format.lower() == 'csv':
            filename = f"{filename}.csv"
            if data:
                fieldnames = set()
                for item in data:
                    fieldnames.update(item.keys())
                
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=list(fieldnames))
                    writer.writeheader()
                    writer.writerows(data)
        
        elif format.lower() == 'json':
            filename = f"{filename}.json"
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, indent=2, ensure_ascii=False)
        
        else:  # text format
            filename = f"{filename}.txt"
            with open(filename, 'w', encoding='utf-8') as textfile:
                for item in data:
                    textfile.write(str(item) + '\n')
        
        return filename
