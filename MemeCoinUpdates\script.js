// Calendar data and functionality
let currentDate = new Date();
let selectedDate = null;

// Sample meme coin events data
const memeCoinEvents = {
    "2024-12-03": [
        {
            type: "major",
            title: "DOGE Coin Rocket Launch",
            description: "Major promotional campaign with Elon Musk tweet scheduled",
            time: "14:30 UTC"
        },
        {
            type: "listing",
            title: "SHIB Listed on Binance US",
            description: "Shiba Inu gets listed on major US exchange",
            time: "08:00 UTC"
        }
    ],
    "2024-12-10": [
        {
            type: "pump",
            title: "PEPE Community Pump",
            description: "Coordinated buying event bringing strong upward momentum",
            time: "20:00 UTC"
        }
    ],
    "2024-12-15": [
        {
            type: "dip",
            title: "Market Correction Alert",
            description: "Whale selling pressure expected on meme coin sector",
            time: "12:00 UTC"
        }
    ],
    "2024-12-18": [
        {
            type: "listing",
            title: "FLOKI DeFi Integration",
            description: "<PERSON><PERSON><PERSON> launches new DeFi staking platform",
            time: "09:00 UTC"
        }
    ],
    "2024-12-20": [
        {
            type: "major",
            title: "BONK Holiday Campaign",
            description: "Massive Christmas marketing campaign launching",
            time: "16:00 UTC"
        }
    ],
    "2024-12-25": [
        {
            type: "pump",
            title: "Christmas Eve Pump",
            description: "Historical data shows strong holiday meme coin season",
            time: "All Day"
        }
    ],
    "2024-12-28": [
        {
            type: "dip",
            title: "Post-Holiday Dip",
            description: "Profit-taking expected after Christmas rally",
            time: "10:00 UTC"
        }
    ]
};

// Initialize calendar
document.addEventListener('DOMContentLoaded', function() {
    generateCalendar();
});

function generateCalendar() {
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    
    // Update month display
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                       'July', 'August', 'September', 'October', 'November', 'December'];
    document.getElementById('currentMonth').textContent = monthNames[currentMonth];
    document.querySelector('.current-year').textContent = currentYear;
    
    // Get first day of month
    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    
    const startingDay = firstDay.getDay();
    const endingDay = lastDay.getDate();
    
    // Get last day of previous month
    const prevMonthLastDay = new Date(currentYear, currentMonth, 0).getDate();
    
    const calendarGrid = document.getElementById('calendarGrid');
    calendarGrid.innerHTML = '';
    
    // Previous month days
    for (let i = startingDay - 1; i >= 0; i--) {
        const dayNumber = prevMonthLastDay - i;
        const dayElement = createDayElement(dayNumber, true, new Date(currentYear, currentMonth - 1, dayNumber));
        calendarGrid.appendChild(dayElement);
    }
    
    // Current month days
    for (let i = 1; i <= endingDay; i++) {
        const dayElement = createDayElement(i, false, new Date(currentYear, currentMonth, i));
        calendarGrid.appendChild(dayElement);
    }
    
    // Next month days to fill grid
    const totalCells = Math.ceil((endingDay + startingDay) / 7) * 7;
    const remainingCells = totalCells - (endingDay + startingDay);
    
    for (let i = 1; i <= remainingCells; i++) {
        const dayElement = createDayElement(i, true, new Date(currentYear, currentMonth + 1, i));
        calendarGrid.appendChild(dayElement);
    }
}

function createDayElement(dayNumber, isOtherMonth, date) {
    const dayElement = document.createElement('div');
    dayElement.className = isOtherMonth ? 'calendar-day other-month' : 'calendar-day';
    
    const dateNumber = document.createElement('div');
    dateNumber.className = 'date-number';
    dateNumber.textContent = dayNumber;
    
    dayElement.appendChild(dateNumber);
    
    // Check for events on this date
    const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(dayNumber).padStart(2, '0')}`;
    const events = memeCoinEvents[dateKey];
    
    if (events) {
        const eventIcons = document.createElement('div');
        eventIcons.className = 'event-icons';
        
        events.forEach(event => {
            const dot = document.createElement('div');
            dot.className = `event-dot ${event.type}-event`;
            eventIcons.appendChild(dot);
        });
        
        dayElement.appendChild(eventIcons);
        dayElement.setAttribute('data-events', 'true');
    } else {
        dayElement.setAttribute('data-events', 'false');
    }
    
    // Add click event
    dayElement.addEventListener('click', function() {
        selectDate(dayElement, date);
    });
    
    // Store date data attribute
    dayElement.setAttribute('data-date', dateKey);
    
    return dayElement;
}

function selectDate(dayElement, date) {
    // Remove previous selection
    const previousSelection = document.querySelector('.calendar-day.selected');
    if (previousSelection) {
        previousSelection.classList.remove('selected');
    }
    
    // Add selection to clicked day
    dayElement.classList.add('selected');
    selectedDate = date;
    
    // Format and display selected date
    const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    document.getElementById('selectedDate').textContent = date.toLocaleDateString('en-US', options);
    
    displayEvents(dateKey);
}

function displayEvents(dateKey) {
    const eventList = document.getElementById('eventList');
    const events = memeCoinEvents[dateKey];
    
    if (events && events.length > 0) {
        let html = '';
        events.forEach(event => {
            html += `
                <div class="event-item ${event.type}">
                    <div class="event-time">${event.time}</div>
                    <div class="event-title">${event.title}</div>
                    <div class="event-description">${event.description}</div>
                </div>
            `;
        });
        eventList.innerHTML = html;
    } else {
        eventList.innerHTML = '\n                <div style="text-align: center; color: var(--light-grey); padding: 20px;">
                    📉 No meme coin events scheduled for this date
                </div>
            ';
    }
}

function previousMonth() {
    currentDate.setMonth(currentDate.getMonth() - 1);
    generateCalendar();
}

function nextMonth() {
    currentDate.setMonth(currentDate.getMonth() + 1);
    generateCalendar();
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft' && !e.target.matches('input')) {
        previousMonth();
    } else if (e.key === 'ArrowRight' && !e.target.matches('input')) {
        nextMonth();
    }
});

// Auto-refresh every 5 minutes for real-time data
setInterval(() => {
    console.log('Calendar refreshed for real-time data');
}, 300000);