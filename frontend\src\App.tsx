import React from 'react';
import { Home, <PERSON>, Zap, Send, <PERSON>ting<PERSON>, Bot } from 'lucide-react';
import { SettingsDialog } from './components/SettingsDialog';
import { AgentsPage } from './pages/Agents';
import { useSettings, useChat, useAgents } from './lib/store';

type Page = 'home' | 'agents';

const suggestions = [
  {
    title: "Any updates from @phantom recently?",
    description: "summarize the latest tweets from @phantom"
  },
  {
    title: "What has toly been doing recently?",
    description: "summarize his recent tweets"
  },
  {
    title: "Launch a new token",
    description: "deploy a new token on pump.fun"
  },
  {
    title: "Swap 1 SOL for USDC",
    description: "using Jupiter to swap on Solana"
  }
];

const integrations = [
  {
    name: "pump.fun",
    description: "Discover new tokens, launch tokens",
    icon: "💊"
  },
  {
    name: "Jupiter",
    description: "Swap tokens & DCA, Limit orders",
    icon: "🌌"
  },
  {
    name: "Magic Eden",
    description: "Explore the best NFT collections",
    icon: "✨"
  },
  {
    name: "Dialect",
    description: "Create and share blinks",
    icon: "💬"
  }
];

function App() {
  const [isSettingsOpen, setIsSettingsOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');
  const [currentPage, setCurrentPage] = React.useState<Page>('home');
  const [error, setError] = React.useState<string | null>(null);
  const { apiKey } = useSettings();
  const { showUserAgent } = useSettings();
  const { messages, sendMessage, currentAgent } = useChat();
  const { agents } = useAgents();

  const currentAgentData = React.useMemo(() => 
    agents.find(a => a.id === currentAgent),
    [agents, currentAgent]
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;
    
    try {
      setError(null);
      await sendMessage(inputValue);
      setInputValue('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0A0A0A] to-[#111] text-white flex">
      {/* Sidebar */}
      <div className="w-72 bg-[#141414]/95 backdrop-blur-xl p-6 flex flex-col border-r border-white/5">
        <div className="flex items-center space-x-3 mb-10">
          <h1 className="text-2xl font-bold">
            <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">AG3NT</span>
            <span className="bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent ml-1.5">X</span>
          </h1>
          <span className="px-2 py-0.5 text-xs bg-white/10 rounded-md font-medium">BETA</span>
          <span className="text-xs text-gray-400">0.1.3</span>
        </div>
        
        <div className="space-y-6">
          <div>
            <h2 className="text-sm font-medium text-gray-400 mb-3 px-3">Explore</h2>
            <nav className="space-y-1">
              <button
                onClick={() => setCurrentPage('home')}
                className={`nav-link w-full text-left ${currentPage === 'home' ? 'active' : ''}`}
              >
                <Home size={18} />
                <span>Home</span>
              </button>
              <button
                onClick={() => setCurrentPage('agents')}
                className={`nav-link w-full text-left ${currentPage === 'agents' ? 'active' : ''}`}
              >
                <Users size={18} />
                <span>Agents</span>
              </button>
              <a href="#" className="nav-link">
                <Zap size={18} />
                <span>Automations</span>
              </a>
              <a
                href="#"
                onClick={() => setIsSettingsOpen(true)}
                className="nav-link"
              >
                <Settings size={18} />
                <span>Settings</span>
              </a>
            </nav>
          </div>
          
          <div>
            <h2 className="text-sm font-medium text-gray-400 mb-3 px-3">Conversations</h2>
            {messages.length > 0 ? (
              <div className="space-y-1">
                <button className="w-full text-left px-3 py-2 text-sm text-gray-400 hover:bg-white/5 rounded-lg transition-colors">
                  New Chat {messages.length > 0 && `(${messages.length})`}
                </button>
              </div>
            ) : (
              <p className="text-sm text-gray-500 px-3">No conversations</p>
            )}
          </div>
        </div>
        
        <div className="mt-auto">
          <div className="flex items-center space-x-3 p-3 rounded-xl bg-gradient-to-r from-[#1A1A1A] to-[#222] border border-white/5">
            <div className="w-9 h-9 rounded-lg bg-gradient-to-br from-[#2A2A2A] to-[#333] flex items-center justify-center font-medium shadow-inner">X</div>
            <div className="flex-1">
              <div className="text-sm">@ag3nt_x</div>
              <div className="text-xs text-gray-500 truncate">cm4zcuiw4047ty30ksams...</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col h-screen">
        <div className="p-10 flex-1 overflow-y-auto">
          {currentPage === 'agents' ? (
            <AgentsPage />
          ) : messages.length > 0 ? (
            <div className="space-y-6">
              {/* Chat Messages */}
              {messages.map((message, index) => (
                <div
                  key={index}
                  className={`flex items-start space-x-4 ${
                    message.role === 'assistant' ? 'justify-start' : 'justify-end'
                  } ${!showUserAgent && message.role === 'user' ? 'opacity-80' : ''}`}
                >
                  {message.role === 'assistant' && (
                    <div className="relative flex-shrink-0">
                      <img
                        src={currentAgentData?.avatar || 'https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=400&auto=format&fit=crop&q=80'}
                        alt={currentAgentData?.name || 'AI Assistant'}
                        className="w-8 h-8 rounded-lg object-cover"
                      />
                      {currentAgentData && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-md bg-[#1A1A1A] border border-white/5 flex items-center justify-center">
                          <currentAgentData.icon size={12} className="text-white/80" />
                        </div>
                      )}
                    </div>
                  )}
                  <div
                    className={`max-w-2xl rounded-2xl px-4 py-3 ${
                      message.role === 'assistant'
                        ? 'bg-[#1A1A1A] text-white'
                        : 'bg-white/10 text-white'
                    }`}
                  >
                    {message.content}
                  </div>
                  {message.role === 'user' && (
                    showUserAgent && (
                    <div className="relative flex-shrink-0">
                      <img
                        src={agents[0].avatar}
                        alt="User Agent"
                        className="w-8 h-8 rounded-lg object-cover"
                      />
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-md bg-[#1A1A1A] border border-white/5 flex items-center justify-center">
                        {React.createElement(agents[0].icon, { size: 12, className: "text-white/80" })}
                      </div>
                    </div>
                    )
                  )}
                </div>
              ))}
            </div>
          ) : (
            <>
              <h1 className="text-6xl font-bold mb-16 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">How can I assist you?</h1>
              {/* Chat Input (Initial position) */}
              <div className="mb-16 max-w-4xl">
                <form onSubmit={handleSubmit} className="relative">
                  <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="Start a new conversation..."
                    className="chat-input"
                  />
                  <button
                    type="submit"
                    className="absolute right-3 top-1/2 -translate-y-1/2 p-1.5 rounded-lg hover:bg-white/5 transition-colors"
                  >
                    <Send size={20} className="text-gray-400" />
                  </button>
                  {error && (
                    <div className="absolute left-0 top-full mt-8 w-full p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400">
                      {error}
                    </div>
                  )}
                </form>
              </div>

              {/* Suggestions */}
              <div className="mb-16">
                <h2 className="text-gray-400 font-medium mb-6">Suggestions</h2>
                <div className="grid grid-cols-2 gap-4">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      className="suggestion-card"
                      onClick={() => {
                        setInputValue(suggestion.title);
                      }}
                    >
                      <h3 className="font-medium mb-1">{suggestion.title}</h3>
                      <p className="text-sm text-gray-500">{suggestion.description}</p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Integrations */}
              <div>
                <h2 className="text-gray-400 font-medium mb-6">Integrations</h2>
                <div className="grid grid-cols-2 gap-4">
                  {integrations.map((integration, index) => (
                    <button key={index} className="integration-card">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[#1A1A1A] to-[#222] flex items-center justify-center text-2xl shadow-lg">
                        {integration.icon}
                      </div>
                      <div className="text-left">
                        <h3 className="font-medium mb-1">{integration.name}</h3>
                        <p className="text-sm text-gray-500">{integration.description}</p>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
        
        {/* Chat Input (Fixed at bottom) */}
        {messages.length > 0 && (
          <div className="p-6 border-t border-white/5 bg-[#0A0A0A]">
            <form onSubmit={handleSubmit} className="relative max-w-4xl mx-auto">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Type your message..."
                className="chat-input"
              />
              <button
                type="submit"
                className="absolute right-3 top-1/2 -translate-y-1/2 p-1.5 rounded-lg hover:bg-white/5 transition-colors"
              >
                <Send size={20} className="text-gray-400" />
              </button>
            </form>
            </div>
        )}
      </div>
      
      <SettingsDialog isOpen={isSettingsOpen} onClose={() => setIsSettingsOpen(false)} />
    </div>
  );
}

export default App;