(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1135:()=>{},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\make-it-heavy-main\\make-it-heavy-main\\frontend\\src\\app\\page.tsx","default")},2982:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3299:(a,b,c)=>{Promise.resolve().then(c.bind(c,1204))},3450:()=>{},3873:a=>{"use strict";a.exports=require("path")},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(7413),e=c(2376),f=c.n(e),g=c(8726),h=c.n(g);c(1135);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},4526:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},5298:()=>{},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7257:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>af});var d=c(687),e=c(3210),f={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},g=(a,b)=>{let c=(0,e.forwardRef)(({color:c="currentColor",size:d=24,strokeWidth:g=2,absoluteStrokeWidth:h,children:i,...j},k)=>(0,e.createElement)("svg",{ref:k,...f,width:d,height:d,stroke:c,strokeWidth:h?24*Number(g)/Number(d):g,className:`lucide lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,...j},[...b.map(([a,b])=>(0,e.createElement)(a,b)),...(Array.isArray(i)?i:[i])||[]]));return c.displayName=`${a}`,c};let h=g("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),i=g("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),j=g("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),k=g("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),l=g("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),m=g("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),n=g("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),o=g("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),p=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000";class q{constructor(a=p){this.baseUrl=a}async createQuery(a){let b=await fetch(`${this.baseUrl}/query`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!b.ok)throw Error(`HTTP error! status: ${b.status}`);return b.json()}async getTaskStatus(a){let b=await fetch(`${this.baseUrl}/task/${a}`);if(!b.ok)throw Error(`HTTP error! status: ${b.status}`);return b.json()}async listTasks(){let a=await fetch(`${this.baseUrl}/tasks`);if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);return a.json()}async deleteTask(a){let b=await fetch(`${this.baseUrl}/task/${a}`,{method:"DELETE"});if(!b.ok)throw Error(`HTTP error! status: ${b.status}`)}streamTaskProgress(a,b,c){let d=new EventSource(`${this.baseUrl}/task/${a}/stream`);return d.onmessage=a=>{try{let c=JSON.parse(a.data);b(c)}catch(a){console.error("Error parsing progress update:",a),c?.(Error("Failed to parse progress update"))}},d.onerror=a=>{console.error("EventSource error:",a),c?.(Error("Connection error"))},()=>{d.close()}}async healthCheck(){let a=await fetch(`${this.baseUrl}/health`);if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);return a.json()}}let r=new q,s=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?s(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},t=/^\[(.+)\]$/,u=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:v(b,a)).classGroupId=c;return}if("function"==typeof a)return w(a)?void u(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{u(e,v(b,a),c,d)})})},v=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},w=a=>a.isThemeGetter,x=(a,b)=>b?a.map(([a,c])=>[a,c.map(a=>"string"==typeof a?b+a:"object"==typeof a?Object.fromEntries(Object.entries(a).map(([a,c])=>[b+a,c])):a)]):a,y=a=>{if(a.length<=1)return a;let b=[],c=[];return a.forEach(a=>{"["===a[0]?(b.push(...c.sort(),a),c=[]):c.push(a)}),b.push(...c.sort()),b},z=/\s+/;function A(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=B(a))&&(d&&(d+=" "),d+=b);return d}let B=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=B(a[d]))&&(c&&(c+=" "),c+=b);return c},C=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},D=/^\[(?:([a-z-]+):)?(.+)\]$/i,E=/^\d+\/\d+$/,F=new Set(["px","full","screen"]),G=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,H=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,I=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,J=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,K=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,L=a=>N(a)||F.has(a)||E.test(a),M=a=>$(a,"length",_),N=a=>!!a&&!Number.isNaN(Number(a)),O=a=>$(a,"number",N),P=a=>!!a&&Number.isInteger(Number(a)),Q=a=>a.endsWith("%")&&N(a.slice(0,-1)),R=a=>D.test(a),S=a=>G.test(a),T=new Set(["length","size","percentage"]),U=a=>$(a,T,aa),V=a=>$(a,"position",aa),W=new Set(["image","url"]),X=a=>$(a,W,ac),Y=a=>$(a,"",ab),Z=()=>!0,$=(a,b,c)=>{let d=D.exec(a);return!!d&&(d[1]?"string"==typeof b?d[1]===b:b.has(d[1]):c(d[2]))},_=a=>H.test(a)&&!I.test(a),aa=()=>!1,ab=a=>J.test(a),ac=a=>K.test(a);Symbol.toStringTag;let ad=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{separator:b,experimentalParseClassName:c}=a,d=1===b.length,e=b[0],f=b.length,g=a=>{let c,g=[],h=0,i=0;for(let j=0;j<a.length;j++){let k=a[j];if(0===h){if(k===e&&(d||a.slice(j,j+f)===b)){g.push(a.slice(i,j)),i=j+f;continue}if("/"===k){c=j;continue}}"["===k?h++:"]"===k&&h--}let j=0===g.length?a:a.substring(i),k=j.startsWith("!"),l=k?j.substring(1):j;return{modifiers:g,hasImportantModifier:k,baseClassName:l,maybePostfixModifierPosition:c&&c>i?c-i:void 0}};return c?a=>c({className:a,parseClassName:g}):g})(i),...(a=>{let b=(a=>{let{theme:b,prefix:c}=a,d={nextPart:new Map,validators:[]};return x(Object.entries(a.classGroups),c).forEach(([a,c])=>{u(c,d,a,b)}),d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),s(c,b)||(a=>{if(t.test(a)){let b=t.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e}=b,f=[],g=a.trim().split(z),h="";for(let a=g.length-1;a>=0;a-=1){let b=g[a],{modifiers:i,hasImportantModifier:j,baseClassName:k,maybePostfixModifierPosition:l}=c(b),m=!!l,n=d(m?k.substring(0,l):k);if(!n){if(!m||!(n=d(k))){h=b+(h.length>0?" "+h:h);continue}m=!1}let o=y(i).join(":"),p=j?o+"!":o,q=p+n;if(f.includes(q))continue;f.push(q);let r=e(n,m);for(let a=0;a<r.length;++a){let b=r[a];f.push(p+b)}h=b+(h.length>0?" "+h:h)}return h})(a,c);return e(a,f),f}return function(){return f(A.apply(null,arguments))}}(()=>{let a=C("colors"),b=C("spacing"),c=C("blur"),d=C("brightness"),e=C("borderColor"),f=C("borderRadius"),g=C("borderSpacing"),h=C("borderWidth"),i=C("contrast"),j=C("grayscale"),k=C("hueRotate"),l=C("invert"),m=C("gap"),n=C("gradientColorStops"),o=C("gradientColorStopPositions"),p=C("inset"),q=C("margin"),r=C("opacity"),s=C("padding"),t=C("saturate"),u=C("scale"),v=C("sepia"),w=C("skew"),x=C("space"),y=C("translate"),z=()=>["auto","contain","none"],A=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto",R,b],D=()=>[R,b],E=()=>["",L,M],F=()=>["auto",N,R],G=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],H=()=>["solid","dashed","dotted","double","none"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],J=()=>["start","end","center","between","around","evenly","stretch"],K=()=>["","0",R],T=()=>["auto","avoid","all","avoid-page","page","left","right","column"],W=()=>[N,R];return{cacheSize:500,separator:":",theme:{colors:[Z],spacing:[L,M],blur:["none","",S,R],brightness:W(),borderColor:[a],borderRadius:["none","","full",S,R],borderSpacing:D(),borderWidth:E(),contrast:W(),grayscale:K(),hueRotate:W(),invert:K(),gap:D(),gradientColorStops:[a],gradientColorStopPositions:[Q,M],inset:B(),margin:B(),opacity:W(),padding:D(),saturate:W(),scale:W(),sepia:K(),skew:W(),space:D(),translate:D()},classGroups:{aspect:[{aspect:["auto","square","video",R]}],container:["container"],columns:[{columns:[S]}],"break-after":[{"break-after":T()}],"break-before":[{"break-before":T()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...G(),R]}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[p]}],"inset-x":[{"inset-x":[p]}],"inset-y":[{"inset-y":[p]}],start:[{start:[p]}],end:[{end:[p]}],top:[{top:[p]}],right:[{right:[p]}],bottom:[{bottom:[p]}],left:[{left:[p]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",P,R]}],basis:[{basis:B()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",R]}],grow:[{grow:K()}],shrink:[{shrink:K()}],order:[{order:["first","last","none",P,R]}],"grid-cols":[{"grid-cols":[Z]}],"col-start-end":[{col:["auto",{span:["full",P,R]},R]}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":[Z]}],"row-start-end":[{row:["auto",{span:[P,R]},R]}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",R]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",R]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...J()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...J(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...J(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[s]}],px:[{px:[s]}],py:[{py:[s]}],ps:[{ps:[s]}],pe:[{pe:[s]}],pt:[{pt:[s]}],pr:[{pr:[s]}],pb:[{pb:[s]}],pl:[{pl:[s]}],m:[{m:[q]}],mx:[{mx:[q]}],my:[{my:[q]}],ms:[{ms:[q]}],me:[{me:[q]}],mt:[{mt:[q]}],mr:[{mr:[q]}],mb:[{mb:[q]}],ml:[{ml:[q]}],"space-x":[{"space-x":[x]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[x]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",R,b]}],"min-w":[{"min-w":[R,b,"min","max","fit"]}],"max-w":[{"max-w":[R,b,"none","full","min","max","fit","prose",{screen:[S]},S]}],h:[{h:[R,b,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[R,b,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[R,b,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[R,b,"auto","min","max","fit"]}],"font-size":[{text:["base",S,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",O]}],"font-family":[{font:[Z]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",R]}],"line-clamp":[{"line-clamp":["none",N,O]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",L,R]}],"list-image":[{"list-image":["none",R]}],"list-style-type":[{list:["none","disc","decimal",R]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[a]}],"placeholder-opacity":[{"placeholder-opacity":[r]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[a]}],"text-opacity":[{"text-opacity":[r]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",L,M]}],"underline-offset":[{"underline-offset":["auto",L,R]}],"text-decoration-color":[{decoration:[a]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:D()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",R]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",R]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[r]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...G(),V]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",U]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},X]}],"bg-color":[{bg:[a]}],"gradient-from-pos":[{from:[o]}],"gradient-via-pos":[{via:[o]}],"gradient-to-pos":[{to:[o]}],"gradient-from":[{from:[n]}],"gradient-via":[{via:[n]}],"gradient-to":[{to:[n]}],rounded:[{rounded:[f]}],"rounded-s":[{"rounded-s":[f]}],"rounded-e":[{"rounded-e":[f]}],"rounded-t":[{"rounded-t":[f]}],"rounded-r":[{"rounded-r":[f]}],"rounded-b":[{"rounded-b":[f]}],"rounded-l":[{"rounded-l":[f]}],"rounded-ss":[{"rounded-ss":[f]}],"rounded-se":[{"rounded-se":[f]}],"rounded-ee":[{"rounded-ee":[f]}],"rounded-es":[{"rounded-es":[f]}],"rounded-tl":[{"rounded-tl":[f]}],"rounded-tr":[{"rounded-tr":[f]}],"rounded-br":[{"rounded-br":[f]}],"rounded-bl":[{"rounded-bl":[f]}],"border-w":[{border:[h]}],"border-w-x":[{"border-x":[h]}],"border-w-y":[{"border-y":[h]}],"border-w-s":[{"border-s":[h]}],"border-w-e":[{"border-e":[h]}],"border-w-t":[{"border-t":[h]}],"border-w-r":[{"border-r":[h]}],"border-w-b":[{"border-b":[h]}],"border-w-l":[{"border-l":[h]}],"border-opacity":[{"border-opacity":[r]}],"border-style":[{border:[...H(),"hidden"]}],"divide-x":[{"divide-x":[h]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[h]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[r]}],"divide-style":[{divide:H()}],"border-color":[{border:[e]}],"border-color-x":[{"border-x":[e]}],"border-color-y":[{"border-y":[e]}],"border-color-s":[{"border-s":[e]}],"border-color-e":[{"border-e":[e]}],"border-color-t":[{"border-t":[e]}],"border-color-r":[{"border-r":[e]}],"border-color-b":[{"border-b":[e]}],"border-color-l":[{"border-l":[e]}],"divide-color":[{divide:[e]}],"outline-style":[{outline:["",...H()]}],"outline-offset":[{"outline-offset":[L,R]}],"outline-w":[{outline:[L,M]}],"outline-color":[{outline:[a]}],"ring-w":[{ring:E()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[a]}],"ring-opacity":[{"ring-opacity":[r]}],"ring-offset-w":[{"ring-offset":[L,M]}],"ring-offset-color":[{"ring-offset":[a]}],shadow:[{shadow:["","inner","none",S,Y]}],"shadow-color":[{shadow:[Z]}],opacity:[{opacity:[r]}],"mix-blend":[{"mix-blend":[...I(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":I()}],filter:[{filter:["","none"]}],blur:[{blur:[c]}],brightness:[{brightness:[d]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",S,R]}],grayscale:[{grayscale:[j]}],"hue-rotate":[{"hue-rotate":[k]}],invert:[{invert:[l]}],saturate:[{saturate:[t]}],sepia:[{sepia:[v]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[c]}],"backdrop-brightness":[{"backdrop-brightness":[d]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[j]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[k]}],"backdrop-invert":[{"backdrop-invert":[l]}],"backdrop-opacity":[{"backdrop-opacity":[r]}],"backdrop-saturate":[{"backdrop-saturate":[t]}],"backdrop-sepia":[{"backdrop-sepia":[v]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[g]}],"border-spacing-x":[{"border-spacing-x":[g]}],"border-spacing-y":[{"border-spacing-y":[g]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",R]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",R]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",R]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[u]}],"scale-x":[{"scale-x":[u]}],"scale-y":[{"scale-y":[u]}],rotate:[{rotate:[P,R]}],"translate-x":[{"translate-x":[y]}],"translate-y":[{"translate-y":[y]}],"skew-x":[{"skew-x":[w]}],"skew-y":[{"skew-y":[w]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",R]}],accent:[{accent:["auto",a]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",R]}],"caret-color":[{caret:[a]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",R]}],fill:[{fill:[a,"none"]}],"stroke-w":[{stroke:[L,M,O]}],stroke:[{stroke:[a,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function ae(...a){return ad(function(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}(a))}function af(){let[a,b]=(0,e.useState)(""),[c,f]=(0,e.useState)("single"),[g,p]=(0,e.useState)(null),[q,s]=(0,e.useState)(!1),[t,u]=(0,e.useState)(null),[v,w]=(0,e.useState)([]),x=async()=>{try{let a=await r.listTasks();w(a.slice(0,5))}catch(a){console.error("Failed to load recent tasks:",a)}},y=async b=>{if(b.preventDefault(),a.trim()&&!q){s(!0),p(null),u(null);try{let b=await r.createQuery({query:a.trim(),mode:c});p(b),(async()=>{let a=0;for(;a<60;)try{let c=await r.getTaskStatus(b.task_id);if(p(c),"completed"===c.status||"failed"===c.status){s(!1),x();break}u({task_id:b.task_id,status:c.status,progress:c.progress}),await new Promise(a=>setTimeout(a,2e3)),a++}catch(a){console.error("Error polling task status:",a),s(!1);break}a>=60&&(s(!1),console.warn("Task polling timed out"))})()}catch(a){console.error("Failed to create query:",a),s(!1)}}},z=a=>{switch(a){case"completed":return(0,d.jsx)(h,{className:"w-4 h-4 text-green-400"});case"failed":return(0,d.jsx)(i,{className:"w-4 h-4 text-red-400"});case"processing":return(0,d.jsx)(j,{className:"w-4 h-4 text-blue-400 animate-spin"});default:return(0,d.jsx)(k,{className:"w-4 h-4 text-neutral-400"})}};return(0,d.jsxs)("div",{className:"min-h-screen bg-black text-white",children:[(0,d.jsx)("header",{className:"border-b border-neutral-800 bg-neutral-950",children:(0,d.jsx)("div",{className:"max-w-6xl mx-auto px-6 py-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"bg-blue-gradient p-2 rounded-lg",children:(0,d.jsx)(l,{className:"w-6 h-6 text-white"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-xl font-semibold text-white",children:"Make It Heavy"}),(0,d.jsx)("p",{className:"text-sm text-neutral-400",children:"Multi-Agent AI Analysis System"})]})]})})}),(0,d.jsxs)("main",{className:"max-w-6xl mx-auto px-6 py-8",children:[(0,d.jsx)("div",{className:"bg-neutral-900 border border-neutral-800 rounded-lg p-6 mb-8",children:(0,d.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"query",className:"block text-white font-medium mb-2",children:"What would you like to analyze?"}),(0,d.jsx)("textarea",{id:"query",value:a,onChange:a=>b(a.target.value),placeholder:"Enter your query here... (e.g., 'Analyze the impact of AI on software development')",className:"w-full h-32 bg-neutral-800 border border-neutral-700 rounded-lg px-4 py-3 text-white placeholder:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",disabled:q})]}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsxs)("button",{type:"button",onClick:()=>f("single"),className:ae("flex items-center gap-2 px-4 py-2 rounded-lg border transition-all","single"===c?"bg-blue-500/10 text-blue-400 border-blue-500/20":"bg-neutral-800 text-neutral-400 border-neutral-700 hover:bg-neutral-700"),disabled:q,children:[(0,d.jsx)(m,{className:"w-4 h-4"}),"Single Agent"]}),(0,d.jsxs)("button",{type:"button",onClick:()=>f("heavy"),className:ae("flex items-center gap-2 px-4 py-2 rounded-lg border transition-all","heavy"===c?"bg-blue-500/10 text-blue-400 border-blue-500/20":"bg-neutral-800 text-neutral-400 border-neutral-700 hover:bg-neutral-700"),disabled:q,children:[(0,d.jsx)(n,{className:"w-4 h-4"}),"Heavy Mode (Multi-Agent)"]})]}),(0,d.jsxs)("button",{type:"submit",disabled:!a.trim()||q,className:"bg-blue-gradient-hover text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2",children:[q?(0,d.jsx)(j,{className:"w-4 h-4 animate-spin"}):(0,d.jsx)(o,{className:"w-4 h-4"}),q?"Processing...":"Analyze"]})]})}),(g||t)&&(0,d.jsxs)("div",{className:"bg-neutral-900 border border-neutral-800 rounded-lg p-6 mb-8",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[z(t?.status||g?.status||"pending"),(0,d.jsx)("h3",{className:"text-lg font-medium text-white",children:"heavy"===c?"Heavy Mode Analysis":"Single Agent Analysis"})]}),t?.progress?.message&&(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("p",{className:"text-neutral-300",children:t.progress.message}),t.progress.stage&&(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("div",{className:"flex justify-between text-sm text-neutral-400 mb-1",children:(0,d.jsxs)("span",{children:["Stage: ",t.progress.stage]})}),(0,d.jsx)("div",{className:"w-full bg-neutral-800 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-gradient h-2 rounded-full transition-all duration-300",style:{width:"completed"===t.progress.stage?"100%":"synthesizing"===t.progress.stage?"80%":"executing"===t.progress.stage?"60%":"decomposing"===t.progress.stage?"40%":"processing"===t.progress.stage?"50%":"initializing"===t.progress.stage?"20%":"10%"}})})]})]}),t?.progress?.questions&&(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("h4",{className:"text-white font-medium mb-2",children:"Generated Questions:"}),(0,d.jsx)("ul",{className:"space-y-2",children:t.progress.questions.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsxs)("span",{className:"text-blue-400 font-medium",children:[b+1,"."]}),(0,d.jsx)("span",{className:"text-neutral-300",children:a})]},b))})]}),g?.result&&(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsx)("h4",{className:"text-white font-medium mb-2",children:"Result:"}),(0,d.jsx)("div",{className:"bg-neutral-800 border border-neutral-700 rounded-lg p-4",children:(0,d.jsx)("pre",{className:"text-neutral-300 whitespace-pre-wrap font-mono text-sm",children:g.result})})]}),g?.error&&(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsx)("h4",{className:"text-red-400 font-medium mb-2",children:"Error:"}),(0,d.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:(0,d.jsx)("p",{className:"text-red-300",children:g.error})})]})]}),v.length>0&&(0,d.jsxs)("div",{className:"bg-neutral-900 border border-neutral-800 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Recent Tasks"}),(0,d.jsx)("div",{className:"space-y-3",children:v.map(a=>(0,d.jsx)("div",{className:"bg-neutral-800 border border-neutral-700 rounded-lg p-4 card-hover-blue",children:(0,d.jsx)("div",{className:"flex items-start justify-between gap-4",children:(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[z(a.status),(0,d.jsx)("span",{className:"text-sm text-neutral-400",children:"heavy"===a.mode?"Heavy Mode":"Single Agent"}),(0,d.jsx)("span",{className:"text-sm text-neutral-500",children:new Date(a.created_at).toLocaleString()})]}),(0,d.jsx)("p",{className:"text-white text-sm mb-2 line-clamp-2",children:a.query}),a.result&&(0,d.jsxs)("p",{className:"text-neutral-400 text-xs line-clamp-1",children:[a.result.substring(0,100),"..."]})]})})},a.task_id))})]})]})]})}},7723:(a,b,c)=>{Promise.resolve().then(c.bind(c,7257))},7804:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"C:\\Users\\<USER>\\Downloads\\make-it-heavy-main\\make-it-heavy-main\\frontend\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Users\\<USER>\\Downloads\\make-it-heavy-main\\make-it-heavy-main\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Downloads\\make-it-heavy-main\\make-it-heavy-main\\frontend\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,400],()=>b(b.s=7804));module.exports=c})();