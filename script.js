// Ensure DOM is loaded before executing JavaScript
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initApp);
} else {
    initApp();
}

function initApp() {
    console.log('JavaScript file properly connected!');
    
    // Button interaction
    const button = document.getElementById('myButton');
    if (button) {
        button.addEventListener('click', handleButtonClick);
    }

    // Form submission
    const form = document.getElementById('myForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }

    // Add some dynamic styling
    addDynamicStyles();
}

function handleButtonClick() {
    const output = document.getElementById('output');
    const responses = [
        'Hello from your connected JavaScript file!',
        'The connection is working perfectly!',
        'Great job connecting your files!',
        'JavaScript is successfully running!',
        'Your page is fully functional!'
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    output.textContent = randomResponse;
    output.style.color = '#2c3e50';
    output.style.fontWeight = 'bold';
}

function handleFormSubmit(event) {
    event.preventDefault(); // Prevent form from actually submitting
    
    const input = document.getElementById('userInput');
    const greeting = document.getElementById('greeting');
    
    if (input.value.trim()) {
        greeting.innerHTML = `<p>Hello, <strong>${input.value}</strong>! Welcome to the page.</p>`;
        greeting.style.backgroundColor = '#e8f5e8';
        greeting.style.padding = '10px';
        greeting.style.borderRadius = '5px';
        greeting.style.marginTop = '10px';
        
        // Clear the input
        input.value = '';
    }
}

function addDynamicStyles() {
    // Add a simple CSS class via JavaScript
    const header = document.querySelector('header');
    if (header) {
        header.style.backgroundColor = '#3498db';
        header.style.color = 'white';
        header.style.padding = '20px';
        header.style.borderRadius = '5px';
    }
}

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initApp, handleButtonClick, handleFormSubmit };
}