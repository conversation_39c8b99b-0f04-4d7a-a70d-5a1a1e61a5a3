{"mcpServers": {"ag3nt-x": {"command": "python", "args": ["mcp_server.py"], "description": "AG3NT X agent system with file operations, terminal commands, web search, and calculations", "capabilities": ["file_operations", "terminal_commands", "web_search", "calculations", "directory_management"]}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "description": "Web browsing and scraping with Playwright - navigate websites, take screenshots, extract content", "capabilities": ["web_browsing", "web_scraping", "screenshots", "form_interaction", "page_navigation"]}}, "settings": {"default_timeout": 30, "max_concurrent_servers": 5, "log_level": "info"}}