"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./agents-tab.tsx":
/*!************************!*\
  !*** ./agents-tab.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AgentsTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Globe,Settings,Star,Terminal,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst getInitialAgents = ()=>[\n        {\n            id: \"make-it-heavy\",\n            name: \"Make It Heavy Agent\",\n            description: \"Comprehensive AI agent with file operations, terminal commands, web search, calculations, and MCP integration. Your primary assistant for development and automation tasks.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Full-Stack Development\",\n            rating: 4.9,\n            users: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"MCP\",\n                \"Automation\"\n            ],\n            status: \"online\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            tools: [\n                \"read_file\",\n                \"write_file\",\n                \"run_terminal_command\",\n                \"search_web\",\n                \"calculate\"\n            ],\n            capabilities: [\n                \"File Management\",\n                \"System Operations\",\n                \"Web Research\",\n                \"Mathematical Calculations\"\n            ]\n        },\n        {\n            id: \"playwright-agent\",\n            name: \"Playwright Web Agent\",\n            description: \"Advanced web automation and scraping agent powered by Playwright. Navigate websites, take screenshots, extract content, and automate web interactions.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 47,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Web Automation\",\n            rating: 4.8,\n            users: 1,\n            tags: [\n                \"Web Scraping\",\n                \"Screenshots\",\n                \"Automation\",\n                \"Browser Control\"\n            ],\n            status: \"online\",\n            gradient: \"from-purple-500 to-pink-500\",\n            tools: [\n                \"browser_navigate\",\n                \"browser_screenshot\",\n                \"browser_click\",\n                \"browser_type\"\n            ],\n            capabilities: [\n                \"Web Navigation\",\n                \"Content Extraction\",\n                \"Form Automation\",\n                \"Visual Capture\"\n            ]\n        },\n        {\n            id: \"file-manager\",\n            name: \"File System Manager\",\n            description: \"Specialized agent for comprehensive file and directory operations. Create, read, write, move, copy, and manage files with advanced permissions and metadata handling.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"File Management\",\n            rating: 4.9,\n            users: 1,\n            tags: [\n                \"Files\",\n                \"Directories\",\n                \"CRUD\",\n                \"Permissions\"\n            ],\n            status: \"online\",\n            gradient: \"from-green-500 to-emerald-500\",\n            tools: [\n                \"create_directory\",\n                \"delete_file_or_directory\",\n                \"move_file_or_directory\",\n                \"copy_file_or_directory\"\n            ],\n            capabilities: [\n                \"File Operations\",\n                \"Directory Management\",\n                \"Permission Control\",\n                \"Metadata Access\"\n            ]\n        },\n        {\n            id: \"terminal-agent\",\n            name: \"Terminal Commander\",\n            description: \"Powerful terminal and system operations agent. Execute commands, manage processes, monitor system health, and automate shell-based workflows.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 76,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"System Operations\",\n            rating: 4.7,\n            users: 1,\n            tags: [\n                \"Terminal\",\n                \"Shell\",\n                \"Commands\",\n                \"System\"\n            ],\n            status: \"online\",\n            gradient: \"from-orange-500 to-red-500\",\n            tools: [\n                \"run_terminal_command\",\n                \"list_directory\",\n                \"get_file_info\"\n            ],\n            capabilities: [\n                \"Command Execution\",\n                \"Process Management\",\n                \"System Monitoring\",\n                \"Shell Automation\"\n            ]\n        },\n        {\n            id: \"research-agent\",\n            name: \"Research Assistant\",\n            description: \"Intelligent research agent with web search capabilities and mathematical computation. Perfect for gathering information, analyzing data, and performing calculations.\",\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 13\n            }, undefined),\n            specialty: \"Research & Analysis\",\n            rating: 4.6,\n            users: 1,\n            tags: [\n                \"Research\",\n                \"Web Search\",\n                \"Calculations\",\n                \"Analysis\"\n            ],\n            status: \"online\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            tools: [\n                \"search_web\",\n                \"calculate\"\n            ],\n            capabilities: [\n                \"Web Research\",\n                \"Mathematical Calculations\",\n                \"Data Analysis\",\n                \"Information Gathering\"\n            ]\n        }\n    ];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"online\":\n            return \"bg-green-500\";\n        case \"busy\":\n            return \"bg-yellow-500\";\n        case \"offline\":\n            return \"bg-neutral-500\";\n        default:\n            return \"bg-neutral-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"online\":\n            return \"Online\";\n        case \"busy\":\n            return \"Busy\";\n        case \"offline\":\n            return \"Offline\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction AgentsTab() {\n    const handleAgentSettings = (agentId, agentName)=>{\n        // Placeholder for future settings functionality\n        console.log(\"Opening settings for \".concat(agentName, \" (ID: \").concat(agentId, \")\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 px-6 border-b border-neutral-800 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-white\",\n                                children: \"AI Agents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-neutral-400\",\n                                children: \"Choose your specialized assistant\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                        variant: \"secondary\",\n                        className: \"bg-neutral-500/10 text-neutral-400 border-neutral-500/20\",\n                        children: [\n                            agents.length,\n                            \" Available\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_2__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                    children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleAgentSettings(agent.id, agent.name);\n                                    },\n                                    className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-800/50 hover:bg-neutral-700/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                    title: \"Settings for \".concat(agent.name),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-3.5 w-3.5 text-gray-400 hover:text-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(agent.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                            children: agent.avatar\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0 pr-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white truncate\",\n                                                            children: agent.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 rounded-full \".concat(getStatusColor(agent.status))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-neutral-400\",\n                                                                    children: getStatusText(agent.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-neutral-400 mb-2\",\n                                                    children: agent.specialty\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: agent.rating\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Globe_Settings_Star_Terminal_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: agent.users.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                    children: agent.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-4\",\n                                    children: agent.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    className: \"w-full bg-gradient-to-r \".concat(agent.gradient, \" hover:opacity-90 text-white border-0 transition-all duration-200 group-hover:shadow-lg bg-blue-gradient-hover text-white shadow-lg\"),\n                                    disabled: agent.status === \"offline\",\n                                    children: agent.status === \"offline\" ? \"Unavailable\" : \"Start Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, agent.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\agents-tab.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_c = AgentsTab;\nvar _c;\n$RefreshReg$(_c, \"AgentsTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./agents-tab.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f88520c95ceb\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjg4NTIwYzk1Y2ViXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ })

});