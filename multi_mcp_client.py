#!/usr/bin/env python3
"""
Multi-MCP Client for Make It Heavy + Playwright Integration
Connects to multiple MCP servers and provides unified access
"""

import asyncio
import json
import os
from typing import Dict, List, Any
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

class MultiMCPClient:
    """Client that manages multiple MCP server connections"""
    
    def __init__(self, config_path: str = "mcp_config.json"):
        self.config_path = config_path
        self.servers = {}
        self.sessions = {}
        self.load_config()
    
    def load_config(self):
        """Load MCP server configuration"""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
                self.servers = config.get('mcpServers', {})
        except FileNotFoundError:
            print(f"Config file {self.config_path} not found")
            self.servers = {}
    
    async def connect_all_servers(self):
        """Connect to all configured MCP servers"""
        print("🚀 Connecting to MCP servers...")
        
        for server_name, server_config in self.servers.items():
            try:
                print(f"📡 Connecting to {server_name}...")
                
                # Create server parameters
                server_params = StdioServerParameters(
                    command=server_config['command'],
                    args=server_config['args'],
                    env=os.environ.copy()
                )
                
                # Connect to server
                read, write = await stdio_client(server_params).__aenter__()
                session = await ClientSession(read, write).__aenter__()
                
                # Initialize connection
                await session.initialize()
                
                # Store session
                self.sessions[server_name] = {
                    'session': session,
                    'config': server_config,
                    'read': read,
                    'write': write
                }
                
                print(f"✅ Connected to {server_name}")
                
            except Exception as e:
                print(f"❌ Failed to connect to {server_name}: {e}")
    
    async def discover_all_capabilities(self):
        """Discover capabilities from all connected servers"""
        print("\n🔍 Discovering server capabilities...")
        
        all_tools = {}
        all_resources = {}
        all_prompts = {}
        
        for server_name, server_info in self.sessions.items():
            session = server_info['session']
            
            try:
                print(f"\n📋 {server_name.upper()} Server:")
                
                # Discover tools
                tools = await session.list_tools()
                server_tools = {}
                for tool in tools.tools:
                    tool_key = f"{server_name}:{tool.name}"
                    server_tools[tool_key] = {
                        'server': server_name,
                        'name': tool.name,
                        'description': tool.description,
                        'tool_obj': tool
                    }
                    print(f"  🔧 {tool.name}: {tool.description}")
                
                all_tools.update(server_tools)
                
                # Discover resources
                resources = await session.list_resources()
                server_resources = {}
                for resource in resources.resources:
                    resource_key = f"{server_name}:{resource.uri}"
                    server_resources[resource_key] = {
                        'server': server_name,
                        'uri': resource.uri,
                        'name': resource.name,
                        'resource_obj': resource
                    }
                    print(f"  📚 {resource.uri}: {resource.name}")
                
                all_resources.update(server_resources)
                
                # Discover prompts
                prompts = await session.list_prompts()
                server_prompts = {}
                for prompt in prompts.prompts:
                    prompt_key = f"{server_name}:{prompt.name}"
                    server_prompts[prompt_key] = {
                        'server': server_name,
                        'name': prompt.name,
                        'description': prompt.description,
                        'prompt_obj': prompt
                    }
                    print(f"  💬 {prompt.name}: {prompt.description}")
                
                all_prompts.update(server_prompts)
                
            except Exception as e:
                print(f"❌ Error discovering capabilities for {server_name}: {e}")
        
        return all_tools, all_resources, all_prompts
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]):
        """Call a tool on a specific server"""
        if server_name not in self.sessions:
            raise Exception(f"Server {server_name} not connected")
        
        session = self.sessions[server_name]['session']
        return await session.call_tool(tool_name, arguments=arguments)
    
    async def read_resource(self, server_name: str, resource_uri: str):
        """Read a resource from a specific server"""
        if server_name not in self.sessions:
            raise Exception(f"Server {server_name} not connected")
        
        session = self.sessions[server_name]['session']
        return await session.read_resource(resource_uri)
    
    async def get_prompt(self, server_name: str, prompt_name: str, arguments: Dict[str, str] = None):
        """Get a prompt from a specific server"""
        if server_name not in self.sessions:
            raise Exception(f"Server {server_name} not connected")
        
        session = self.sessions[server_name]['session']
        return await session.get_prompt(prompt_name, arguments=arguments)
    
    async def close_all_connections(self):
        """Close all server connections"""
        for server_name, server_info in self.sessions.items():
            try:
                await server_info['session'].__aexit__(None, None, None)
                print(f"🔌 Disconnected from {server_name}")
            except Exception as e:
                print(f"❌ Error disconnecting from {server_name}: {e}")

async def demo_multi_mcp():
    """Demonstrate multi-MCP server capabilities"""
    client = MultiMCPClient()
    
    try:
        # Connect to all servers
        await client.connect_all_servers()
        
        # Discover capabilities
        tools, resources, prompts = await client.discover_all_capabilities()
        
        print(f"\n📊 Summary:")
        print(f"   🔧 Total tools: {len(tools)}")
        print(f"   📚 Total resources: {len(resources)}")
        print(f"   💬 Total prompts: {len(prompts)}")
        
        # Test Make It Heavy tools
        print("\n🧮 Testing Make It Heavy calculator...")
        try:
            result = await client.call_tool("make-it-heavy", "calculate", {"expression": "15 * 7"})
            result_content = result.content[0]
            if hasattr(result_content, 'text'):
                print(f"✅ Calculator result: {result_content.text}")
        except Exception as e:
            print(f"❌ Calculator test failed: {e}")
        
        # Test Playwright tools (if connected)
        if "playwright" in client.sessions:
            print("\n🌐 Testing Playwright web browsing...")
            try:
                # Navigate to a simple page
                result = await client.call_tool("playwright", "navigate", {"url": "https://httpbin.org/html"})
                result_content = result.content[0]
                if hasattr(result_content, 'text'):
                    print(f"✅ Navigation result: {result_content.text[:100]}...")
                
                # Take a screenshot
                result = await client.call_tool("playwright", "screenshot", {})
                result_content = result.content[0]
                if hasattr(result_content, 'text'):
                    print(f"✅ Screenshot taken: {result_content.text[:100]}...")
                    
            except Exception as e:
                print(f"❌ Playwright test failed: {e}")
        
        # Test file operations
        print("\n📁 Testing file operations...")
        try:
            result = await client.call_tool("make-it-heavy", "list_directory", {"path": "."})
            result_content = result.content[0]
            if hasattr(result_content, 'text'):
                print(f"✅ Directory listing: {result_content.text[:200]}...")
        except Exception as e:
            print(f"❌ File operations test failed: {e}")
        
        print("\n🎉 Multi-MCP demonstration completed!")
        
    finally:
        # Clean up connections
        await client.close_all_connections()

if __name__ == "__main__":
    asyncio.run(demo_multi_mcp())
