from .base_tool import BaseTool
import os
import shutil

class CreateDirectoryTool(BaseTool):
    def __init__(self, config: dict):
        self.config = config

    @property
    def name(self) -> str:
        return "create_directory"

    @property
    def description(self) -> str:
        return "Create a new directory (folder) at the specified path. Can create nested directories if parent directories don't exist."

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "The directory path to create"
                },
                "parents": {
                    "type": "boolean",
                    "description": "Whether to create parent directories if they don't exist (default: True)",
                    "default": True
                }
            },
            "required": ["path"]
        }

    def execute(self, path: str, parents: bool = True) -> dict:
        try:
            # Get absolute path
            abs_path = os.path.abspath(path)

            # Check if directory already exists
            if os.path.exists(abs_path):
                if os.path.isdir(abs_path):
                    return {
                        "path": abs_path,
                        "success": True,
                        "message": f"Directory already exists: {path}",
                        "already_existed": True
                    }
                else:
                    return {"error": f"Path exists but is not a directory: {path}"}

            # Create directory
            if parents:
                os.makedirs(abs_path, exist_ok=True)
            else:
                os.mkdir(abs_path)

            return {
                "path": abs_path,
                "success": True,
                "message": f"Successfully created directory: {path}",
                "already_existed": False
            }

        except PermissionError:
            return {"error": f"Permission denied creating directory: {path}"}
        except FileNotFoundError:
            return {"error": f"Parent directory does not exist: {path} (use parents=True to create parent directories)"}
        except OSError as e:
            return {"error": f"OS error creating directory: {str(e)}"}
        except Exception as e:
            return {"error": f"Failed to create directory: {str(e)}"}