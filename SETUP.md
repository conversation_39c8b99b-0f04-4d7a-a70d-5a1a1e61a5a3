# Make It Heavy - REST API & Frontend Setup

This project now includes both a REST API backend and a React Next.js frontend for the Make It Heavy multi-agent AI system.

## Architecture

- **Backend**: FastAPI REST API (`/api/`) that exposes the Make It Heavy functionality
- **Frontend**: React Next.js TypeScript application (`/frontend/`) with modern dark UI
- **Original CLI**: Python CLI tools (`main.py`, `make_it_heavy.py`) still available

## Quick Start

### 1. Configure API Key

First, make sure you have your OpenRouter API key configured in `config.yaml`:

```yaml
openrouter:
  api_key: "YOUR_OPENROUTER_API_KEY_HERE"
  base_url: "https://openrouter.ai/api/v1"
  model: "openai/gpt-4o-mini"
```

### 2. Start the Backend API

```bash
# Install Python dependencies (if not already installed)
pip install -r requirements.txt

# Install API-specific dependencies
pip install -r api/requirements.txt

# Start the API server
cd api
python start.py
```

The API will be available at:
- **API Base**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

### 3. Start the Frontend

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start the development server
npm run dev
```

The frontend will be available at:
- **Frontend**: http://localhost:3000

## API Endpoints

### Core Endpoints

- `POST /query` - Create a new analysis task
- `GET /task/{task_id}` - Get task status and results
- `GET /task/{task_id}/stream` - Stream real-time progress updates
- `GET /tasks` - List all tasks
- `DELETE /task/{task_id}` - Delete a task
- `GET /health` - Health check

### Example API Usage

```bash
# Create a new query
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "Analyze the impact of AI on software development", "mode": "heavy"}'

# Check task status
curl "http://localhost:8000/task/{task_id}"

# Stream progress (Server-Sent Events)
curl "http://localhost:8000/task/{task_id}/stream"
```

## Frontend Features

### Modern Dark UI
- **Dark Theme**: Professional dark interface with blue accents
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Real-time Updates**: Live progress tracking with Server-Sent Events

### Analysis Modes
- **Single Agent**: Fast analysis with one AI agent
- **Heavy Mode**: Comprehensive multi-agent analysis (like Grok Heavy)

### Key Components
- **Query Interface**: Large textarea for complex queries
- **Mode Selection**: Toggle between single and heavy mode
- **Progress Tracking**: Real-time progress with visual indicators
- **Results Display**: Formatted results with syntax highlighting
- **Task History**: View recent analysis tasks

## Development

### Backend Development

```bash
# Run API in development mode with auto-reload
cd api
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development

```bash
# Run frontend in development mode
cd frontend
npm run dev
```

### Project Structure

```
make-it-heavy/
├── api/                    # FastAPI backend
│   ├── main.py            # API server
│   ├── requirements.txt   # Python dependencies
│   └── start.py          # Startup script
├── frontend/              # Next.js frontend
│   ├── src/
│   │   ├── app/          # App router pages
│   │   └── lib/          # Utilities and API client
│   ├── package.json      # Node dependencies
│   └── tailwind.config.js # Tailwind configuration
├── tools/                 # Original tool system
├── agent.py              # Core agent implementation
├── orchestrator.py       # Multi-agent orchestration
├── config.yaml           # Configuration
└── README.md             # Original documentation
```

## Configuration

### Environment Variables

Create a `.env.local` file in the frontend directory:

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### API Configuration

The API inherits configuration from the main `config.yaml` file:

```yaml
# OpenRouter API settings
openrouter:
  api_key: "YOUR_API_KEY"
  base_url: "https://openrouter.ai/api/v1"
  model: "openai/gpt-4o-mini"

# Agent settings
agent:
  max_iterations: 10

# Orchestrator settings
orchestrator:
  parallel_agents: 4
  task_timeout: 300
```

## Deployment

### Backend Deployment

The FastAPI backend can be deployed using:

```bash
# Using uvicorn directly
uvicorn api.main:app --host 0.0.0.0 --port 8000

# Using gunicorn for production
gunicorn api.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Frontend Deployment

```bash
# Build for production
cd frontend
npm run build

# Start production server
npm start
```

## Troubleshooting

### Common Issues

1. **API Connection Error**: Make sure the backend is running on port 8000
2. **CORS Issues**: The API is configured to allow requests from localhost:3000
3. **Missing Dependencies**: Run `pip install -r requirements.txt` and `npm install`
4. **Config Issues**: Ensure `config.yaml` has a valid OpenRouter API key

### Debug Mode

Enable debug logging by setting environment variables:

```bash
# Backend debug
export PYTHONPATH=.
export DEBUG=1

# Frontend debug
export NODE_ENV=development
```

## Original CLI Usage

The original CLI tools are still available:

```bash
# Single agent mode
python main.py

# Heavy mode (multi-agent)
python make_it_heavy.py
```