#!/usr/bin/env python3
"""
Test MCP integration with our FastAPI backend
"""

import asyncio
import requests
from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

async def test_mcp_api_integration():
    """Test MCP integration with our API"""
    
    print("🚀 Testing MCP integration with FastAPI backend...")
    
    # Test 1: Check if API is running
    print("\n📡 Testing API health...")
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ API is healthy:", response.json())
        else:
            print("❌ API health check failed")
            return
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return
    
    # Test 2: Check MCP info endpoint
    print("\n📋 Testing MCP info endpoint...")
    try:
        response = requests.get("http://localhost:8000/mcp/info")
        if response.status_code == 200:
            print("✅ MCP info endpoint working:", response.json())
        else:
            print(f"❌ MCP info endpoint failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ MCP info endpoint error: {e}")
    
    # Test 3: Test MCP protocol connection
    print("\n🔌 Testing MCP protocol connection...")
    try:
        async with streamablehttp_client("http://localhost:8000/mcp") as (read, write, _):
            async with ClientSession(read, write) as session:
                # Initialize the connection
                await session.initialize()
                print("✅ MCP protocol connection established!")
                
                # Test tool discovery
                tools = await session.list_tools()
                print(f"✅ Found {len(tools.tools)} MCP tools:")
                for tool in tools.tools[:5]:  # Show first 5 tools
                    print(f"   - {tool.name}: {tool.description}")
                if len(tools.tools) > 5:
                    print(f"   ... and {len(tools.tools) - 5} more tools")
                
                # Test resource discovery
                resources = await session.list_resources()
                print(f"✅ Found {len(resources.resources)} MCP resources:")
                for resource in resources.resources:
                    print(f"   - {resource.uri}: {resource.name}")
                
                # Test prompt discovery
                prompts = await session.list_prompts()
                print(f"✅ Found {len(prompts.prompts)} MCP prompts:")
                for prompt in prompts.prompts:
                    print(f"   - {prompt.name}: {prompt.description}")
                
                # Test calling a tool
                print("\n🛠️ Testing MCP tool execution...")
                try:
                    result = await session.call_tool("calculate", arguments={"expression": "5 * 8"})
                    result_content = result.content[0]
                    if hasattr(result_content, 'text'):
                        print(f"✅ Tool execution successful: {result_content.text}")
                except Exception as e:
                    print(f"❌ Tool execution failed: {e}")
                
                # Test reading a resource
                print("\n📖 Testing MCP resource reading...")
                try:
                    resource_content = await session.read_resource("api://system/status")
                    content_block = resource_content.contents[0]
                    if hasattr(content_block, 'text'):
                        print("✅ Resource reading successful:")
                        print(content_block.text[:200] + "..." if len(content_block.text) > 200 else content_block.text)
                except Exception as e:
                    print(f"❌ Resource reading failed: {e}")
                
                print("\n🎉 MCP integration test completed successfully!")
                
    except Exception as e:
        print(f"❌ MCP protocol connection failed: {e}")
        import traceback
        traceback.print_exc()

def test_api_endpoints():
    """Test regular API endpoints"""
    print("\n🌐 Testing regular API endpoints...")
    
    # Test health endpoint
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"Health endpoint: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"Health endpoint error: {e}")
    
    # Test docs endpoint
    try:
        response = requests.get("http://localhost:8000/docs")
        print(f"Docs endpoint: {response.status_code} - Available")
    except Exception as e:
        print(f"Docs endpoint error: {e}")

if __name__ == "__main__":
    # Test regular API first
    test_api_endpoints()
    
    # Test MCP integration
    asyncio.run(test_mcp_api_integration())
