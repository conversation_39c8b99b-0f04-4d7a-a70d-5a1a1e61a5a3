"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f1bef411af83\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcR3VlcnJcXERvd25sb2Fkc1xcbWFrZS1pdC1oZWF2eS1tYWluXFxtYWtlLWl0LWhlYXZ5LW1haW5cXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjFiZWY0MTFhZjgzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./mcp-library-tab.tsx":
/*!*****************************!*\
  !*** ./mcp-library-tab.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpLibraryTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,CheckCircle,Code,Download,FileText,Globe,Package,Search,Settings,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst getInitialMcpServers = ()=>[\n        {\n            id: \"make-it-heavy\",\n            name: \"Make It Heavy Agent\",\n            description: \"Our comprehensive agent system with file operations, terminal commands, web search, calculations, and system management capabilities.\",\n            author: \"Make It Heavy\",\n            version: \"1.0.0\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 59,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 1,\n            tags: [\n                \"File Operations\",\n                \"Terminal\",\n                \"Web Search\",\n                \"Calculations\"\n            ],\n            status: \"installed\",\n            gradient: \"from-blue-500 to-cyan-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"playwright\",\n            name: \"Playwright MCP\",\n            description: \"Professional web automation and scraping with Playwright. Navigate websites, take screenshots, extract content, and automate browser interactions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 75,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 61200,\n            tags: [\n                \"Playwright\",\n                \"Web Automation\",\n                \"Screenshots\",\n                \"Browser\"\n            ],\n            status: \"installed\",\n            gradient: \"from-green-500 to-emerald-500\",\n            lastUpdated: \"Active\"\n        },\n        {\n            id: \"filesystem\",\n            name: \"Filesystem MCP\",\n            description: \"Secure file operations with configurable access controls. Read, write, create, delete, and manage files and directories with advanced permissions.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 91,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.9,\n            downloads: 45300,\n            tags: [\n                \"Files\",\n                \"Directories\",\n                \"Security\",\n                \"Permissions\"\n            ],\n            status: \"available\",\n            gradient: \"from-purple-500 to-pink-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"git\",\n            name: \"Git MCP\",\n            description: \"Tools to read, search, and manipulate Git repositories. Manage commits, branches, and repository operations through MCP.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Development\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 107,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.8,\n            downloads: 32100,\n            tags: [\n                \"Git\",\n                \"Version Control\",\n                \"Repository\",\n                \"Commits\"\n            ],\n            status: \"available\",\n            gradient: \"from-orange-500 to-red-500\",\n            lastUpdated: \"3 days ago\"\n        },\n        {\n            id: \"memory\",\n            name: \"Memory MCP\",\n            description: \"Knowledge graph-based persistent memory system. Store and retrieve information across conversations with intelligent context management.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"AI\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 123,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.7,\n            downloads: 28900,\n            tags: [\n                \"Memory\",\n                \"Knowledge Graph\",\n                \"Context\",\n                \"AI\"\n            ],\n            status: \"available\",\n            gradient: \"from-yellow-500 to-orange-500\",\n            lastUpdated: \"1 week ago\"\n        },\n        {\n            id: \"fetch\",\n            name: \"Fetch MCP\",\n            description: \"Web content fetching and conversion for efficient LLM usage. Convert web pages to markdown and extract structured content.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Web\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 139,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.6,\n            downloads: 19800,\n            tags: [\n                \"Web Fetch\",\n                \"Markdown\",\n                \"Content\",\n                \"Conversion\"\n            ],\n            status: \"available\",\n            gradient: \"from-indigo-500 to-purple-500\",\n            lastUpdated: \"2 days ago\"\n        },\n        {\n            id: \"time\",\n            name: \"Time MCP\",\n            description: \"Time and timezone conversion capabilities. Handle dates, times, and timezone operations with precision.\",\n            author: \"Anthropic\",\n            version: \"latest\",\n            category: \"Utility\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 155,\n                columnNumber: 11\n            }, undefined),\n            rating: 4.5,\n            downloads: 15600,\n            tags: [\n                \"Time\",\n                \"Timezone\",\n                \"Date\",\n                \"Conversion\"\n            ],\n            status: \"available\",\n            gradient: \"from-cyan-500 to-blue-500\",\n            lastUpdated: \"1 week ago\"\n        }\n    ];\nconst categories = [\n    \"All\",\n    \"Development\",\n    \"Web\",\n    \"AI\",\n    \"Utility\",\n    \"Database\",\n    \"Communication\"\n];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"bg-green-500\";\n        case \"updating\":\n            return \"bg-yellow-500\";\n        case \"available\":\n            return \"bg-gray-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n};\nconst getStatusText = (status)=>{\n    switch(status){\n        case \"installed\":\n            return \"Installed\";\n        case \"updating\":\n            return \"Updating\";\n        case \"available\":\n            return \"Available\";\n        default:\n            return \"Unknown\";\n    }\n};\nfunction McpLibraryTab() {\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const filteredServers = mcpServers.filter((server)=>{\n        const matchesSearch = server.name.toLowerCase().includes(searchQuery.toLowerCase()) || server.description.toLowerCase().includes(searchQuery.toLowerCase()) || server.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));\n        const matchesCategory = selectedCategory === \"All\" || server.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleInstall = (serverId, serverName)=>{\n        console.log(\"Installing MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for installation logic\n    };\n    const handleUninstall = (serverId, serverName)=>{\n        console.log(\"Uninstalling MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for uninstallation logic\n    };\n    const handleServerSettings = (serverId, serverName)=>{\n        console.log(\"Opening settings for MCP server: \".concat(serverName, \" (ID: \").concat(serverId, \")\"));\n    // Placeholder for settings logic\n    };\n    const getStatusColorNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"bg-green-500\";\n            case \"updating\":\n                return \"bg-yellow-500\";\n            case \"available\":\n                return \"bg-neutral-500\";\n            default:\n                return \"bg-neutral-500\";\n        }\n    };\n    const getStatusTextNew = (status)=>{\n        switch(status){\n            case \"installed\":\n                return \"Installed\";\n            case \"updating\":\n                return \"Updating\";\n            case \"available\":\n                return \"Available\";\n            default:\n                return \"Unknown\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-neutral-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"MCP Library\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400\",\n                                        children: \"Model Context Protocol servers and integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"secondary\",\n                                className: \"bg-gray-800 text-gray-300\",\n                                children: [\n                                    filteredServers.length,\n                                    \" of \",\n                                    mcpServers.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search MCP servers...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"\".concat(selectedCategory === category ? \"border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300\" : \"text-gray-400 hover:text-white hover:bg-gray-800/50\"),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                        children: filteredServers.map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200\",\n                                children: [\n                                    server.status === \"installed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            handleServerSettings(server.id, server.name);\n                                        },\n                                        className: \"absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10\",\n                                        title: \"Settings for \".concat(server.name),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(server.gradient, \" flex items-center justify-center text-white flex-shrink-0\"),\n                                                children: server.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0 pr-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white truncate\",\n                                                                children: server.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(getStatusColorNew(server.status))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-neutral-400\",\n                                                                        children: getStatusTextNew(server.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-neutral-400\",\n                                                                children: server.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"v\",\n                                                                    server.version\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"by \",\n                                                                    server.author\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 fill-yellow-500 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: server.downloads.toLocaleString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Updated \",\n                                                                    server.lastUpdated\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400 mb-4 leading-relaxed\",\n                                        children: server.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: server.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: server.status === \"installed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleUninstall(server.id, server.name),\n                                            variant: \"outline\",\n                                            className: \"flex-1 border-neutral-700 text-neutral-300 hover:bg-neutral-800 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Installed\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this) : server.status === \"updating\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            disabled: true,\n                                            className: \"flex-1 bg-yellow-600/20 text-yellow-400 border border-yellow-600/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>handleInstall(server.id, server.name),\n                                            className: \"flex-1 bg-blue-gradient-hover text-white shadow-lg border-0 transition-all duration-200 group-hover:shadow-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Install\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, server.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    filteredServers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_CheckCircle_Code_Download_FileText_Globe_Package_Search_Settings_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-400 mb-2\",\n                                    children: \"No MCP servers found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Try adjusting your search or filter criteria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\mcp-library-tab.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(McpLibraryTab, \"qPp0YF131Nuu/8g7tEzKW52PTM0=\");\n_c = McpLibraryTab;\nvar _c;\n$RefreshReg$(_c, \"McpLibraryTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./mcp-library-tab.tsx\n"));

/***/ })

});