"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./chat-interface.tsx":
/*!****************************!*\
  !*** ./chat-interface.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { mode = 'heavy', selectedAgent } = param;\n    _s();\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatMode, setIsChatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentTaskId, setCurrentTaskId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [apiStatus, setApiStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('checking');\n    const [currentMode, setCurrentMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mode);\n    // Update mode when prop changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            setCurrentMode(mode);\n        }\n    }[\"ChatInterface.useEffect\"], [\n        mode\n    ]);\n    // Save mode changes to localStorage\n    const handleModeChange = (newMode)=>{\n        setCurrentMode(newMode);\n        localStorage.setItem('chatMode', newMode);\n        if (newMode === 'heavy') {\n            localStorage.removeItem('selectedAgent');\n        }\n    };\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (scrollAreaRef.current) {\n            const scrollContainer = scrollAreaRef.current.querySelector(\"[data-radix-scroll-area-viewport]\");\n            if (scrollContainer) {\n                scrollContainer.scrollTop = scrollContainer.scrollHeight;\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    // Check API health on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkAPIHealth = {\n                \"ChatInterface.useEffect.checkAPIHealth\": async ()=>{\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.getHealth();\n                        setApiStatus('connected');\n                    } catch (error) {\n                        console.error('API health check failed:', error);\n                        setApiStatus('disconnected');\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkAPIHealth\"];\n            checkAPIHealth();\n            // Check health every 30 seconds\n            const interval = setInterval(checkAPIHealth, 30000);\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(interval)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Auto-resize textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (textareaRef.current) {\n                textareaRef.current.style.height = \"auto\";\n                textareaRef.current.style.height = \"\".concat(Math.min(textareaRef.current.scrollHeight, 120), \"px\");\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        input\n    ]);\n    const handleFileUpload = (event)=>{\n        const files = event.target.files;\n        if (files) {\n            const newFiles = Array.from(files).map((file)=>({\n                    file,\n                    id: Math.random().toString(36).substr(2, 9)\n                }));\n            setUploadedFiles((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n        }\n        // Reset the input so the same file can be selected again\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const removeFile = (fileId)=>{\n        setUploadedFiles((prev)=>prev.filter((f)=>f.id !== fileId));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const submitToAgent = async (userMessage, files)=>{\n        setIsTyping(true);\n        try {\n            // Create initial agent message placeholder\n            const agentMessageId = Date.now().toString() + \"-agent\";\n            const agentMessage = {\n                id: agentMessageId,\n                role: \"agent\",\n                content: \"Processing your request...\",\n                timestamp: new Date(),\n                status: \"processing\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    agentMessage\n                ]);\n            // Submit query to API\n            const task = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.submitQueryWithStream({\n                query: userMessage,\n                mode: currentMode\n            }, (update)=>{\n                // Update the agent message with progress\n                setMessages((prev)=>prev.map((msg)=>msg.id === agentMessageId ? {\n                            ...msg,\n                            content: update.partial_result || msg.content,\n                            status: update.status,\n                            progress: update.progress\n                        } : msg));\n            }, (error)=>{\n                // Handle error\n                setMessages((prev)=>prev.map((msg)=>msg.id === agentMessageId ? {\n                            ...msg,\n                            content: \"Error: \".concat(error.message),\n                            status: \"failed\",\n                            error: error.message\n                        } : msg));\n                setIsTyping(false);\n            }, ()=>{\n                // Handle completion\n                setIsTyping(false);\n                setCurrentTaskId(null);\n            });\n            setCurrentTaskId(task.task_id);\n        } catch (error) {\n            console.error('Failed to submit query:', error);\n            // Update with error message\n            setMessages((prev)=>prev.map((msg)=>msg.role === \"agent\" && msg.content === \"Processing your request...\" ? {\n                        ...msg,\n                        content: \"Sorry, I encountered an error: \".concat(error instanceof Error ? error.message : 'Unknown error'),\n                        status: \"failed\",\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    } : msg));\n            setIsTyping(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!input.trim() && uploadedFiles.length === 0) return;\n        // Check API connection\n        if (apiStatus === 'disconnected') {\n            // Try to reconnect\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.getHealth();\n                setApiStatus('connected');\n            } catch (error) {\n                console.error('API still disconnected:', error);\n                return;\n            }\n        }\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: input.trim(),\n            timestamp: new Date(),\n            files: uploadedFiles.map((f)=>f.file)\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const messageContent = input.trim();\n        const messageFiles = uploadedFiles.map((f)=>f.file);\n        setInput(\"\");\n        setUploadedFiles([]);\n        if (!isChatMode) {\n            setIsChatMode(true);\n        }\n        // Submit to real agent API\n        await submitToAgent(messageContent, messageFiles);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const ChatBubble = (param)=>{\n        let { message } = param;\n        const isUser = message.role === \"user\";\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3 mb-6 \".concat(isUser ? \"justify-end\" : \"justify-start\"),\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: message.status === \"processing\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4 text-white animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 15\n                    }, this) : message.status === \"failed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"AI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[70%] \".concat(isUser ? \"order-first\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-neutral-500\",\n                                    children: isUser ? \"You\" : \"AG3NT X\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-neutral-500\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                !isUser && message.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: message.status === \"completed\" ? \"default\" : message.status === \"failed\" ? \"destructive\" : \"secondary\",\n                                    className: \"text-xs\",\n                                    children: message.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            px-4 py-3 rounded-2xl text-sm leading-relaxed whitespace-pre-wrap\\n            \".concat(isUser ? \"bg-blue-500 text-white ml-auto rounded-br-md\" : \"bg-neutral-800 text-neutral-100 rounded-bl-md\", \"\\n          \"),\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this),\n                        message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 space-y-2\",\n                            children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\\n                  flex items-center gap-2 px-3 py-2 rounded-lg text-xs\\n                  \".concat(isUser ? \"bg-blue-700/50 text-blue-100\" : \"bg-neutral-700/50 text-neutral-300\", \"\\n                \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: file.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-500\",\n                                            children: [\n                                                \"(\",\n                                                formatFileSize(file.size),\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"U\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    };\n    const TypingIndicator = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3 mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"AI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 px-4 py-3 rounded-2xl rounded-bl-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"150ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"300ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 336,\n            columnNumber: 5\n        }, this);\n    const FileUploadArea = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: uploadedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3 space-y-2\",\n                children: uploadedFiles.map((uploadedFile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 px-3 py-2 bg-neutral-800/50 border border-neutral-700 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-300 truncate flex-1\",\n                                children: uploadedFile.file.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"(\",\n                                    formatFileSize(uploadedFile.file.size),\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"button\",\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>removeFile(uploadedFile.id),\n                                className: \"h-6 w-6 p-0 hover:bg-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, uploadedFile.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    if (!isChatMode) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 border-b border-neutral-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            currentMode === 'heavy' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium\",\n                                                children: currentMode === 'heavy' ? 'Multi-Agent Mode' : 'Single Agent Mode'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    selectedAgent && currentMode === 'single' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-400\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                className: \"bg-green-500/10 text-green-400 border-green-500/20\",\n                                                children: selectedAgent.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>handleModeChange(currentMode === 'heavy' ? 'single' : 'heavy'),\n                                    className: \"border-neutral-700 text-neutral-300 hover:bg-neutral-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Switch to \",\n                                        currentMode === 'heavy' ? 'Single' : 'Multi',\n                                        \"-Agent\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-2xl mx-auto px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl font-medium text-white mb-6 leading-tight\",\n                                children: \"How can I assist you?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-400 mb-12\",\n                                children: currentMode === 'heavy' ? 'Multi-agent mode: Deploy specialized agents working together for comprehensive analysis' : selectedAgent ? \"Single agent mode: Chat directly with \".concat(selectedAgent.name) : 'Single agent mode: Focused assistance from one specialized agent'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileUploadArea, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"textarea-container relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                ref: textareaRef,\n                                                value: input,\n                                                onChange: (e)=>setInput(e.target.value),\n                                                onKeyDown: handleKeyDown,\n                                                placeholder: \"Start a new conversation...\",\n                                                className: \"textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar\",\n                                                rows: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: fileInputRef,\n                                                type: \"file\",\n                                                multiple: true,\n                                                onChange: handleFileUpload,\n                                                className: \"hidden\",\n                                                accept: \"*/*\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    var _fileInputRef_current;\n                                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                },\n                                                className: \"absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10\",\n                                                title: \"Upload files\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                size: \"sm\",\n                                                className: \"absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10\",\n                                                disabled: !input.trim() && uploadedFiles.length === 0,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 380,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 px-4 border-b border-neutral-800 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\",\n                                children: currentMode === 'heavy' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-medium text-white\",\n                                        children: [\n                                            \"AG3NT X \",\n                                            currentMode === 'heavy' ? 'Multi-Agent' : 'Single Agent'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full \".concat(apiStatus === 'connected' ? 'bg-green-500' : apiStatus === 'disconnected' ? 'bg-red-500' : 'bg-yellow-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-neutral-500\",\n                                                children: apiStatus === 'connected' ? 'Connected' : apiStatus === 'disconnected' ? 'Disconnected' : 'Checking...'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, this),\n                                            selectedAgent && currentMode === 'single' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-600\",\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-400\",\n                                                        children: selectedAgent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>handleModeChange(currentMode === 'heavy' ? 'single' : 'heavy'),\n                            className: \"border-neutral-700 text-neutral-300 hover:bg-neutral-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                currentMode === 'heavy' ? 'Single' : 'Multi',\n                                \"-Agent\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, this),\n                    currentTaskId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"outline\",\n                        className: \"text-xs\",\n                        children: [\n                            \"Task: \",\n                            currentTaskId.slice(0, 8),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 478,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                ref: scrollAreaRef,\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-0\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatBubble, {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this)),\n                        isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 24\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-neutral-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileUploadArea, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"textarea-container relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    ref: textareaRef,\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"Type your message...\",\n                                    className: \"textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar\",\n                                    rows: 1,\n                                    disabled: isTyping\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    multiple: true,\n                                    onChange: handleFileUpload,\n                                    className: \"hidden\",\n                                    accept: \"*/*\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10\",\n                                    title: \"Upload files\",\n                                    disabled: isTyping,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    size: \"sm\",\n                                    className: \"absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10\",\n                                    disabled: !input.trim() && uploadedFiles.length === 0 || isTyping,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n        lineNumber: 476,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"qxKdfXZekVRHpbfKZwKlpCTE0dM=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./chat-interface.tsx\n"));

/***/ })

});