"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./chat-interface.tsx":
/*!****************************!*\
  !*** ./chat-interface.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Copy,Loader2,Paperclip,Send,Settings,ThumbsDown,ThumbsUp,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { mode = 'heavy', selectedAgent } = param;\n    _s();\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatMode, setIsChatMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadedFiles, setUploadedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentTaskId, setCurrentTaskId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [apiStatus, setApiStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('checking');\n    const [currentMode, setCurrentMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mode);\n    // Update mode when prop changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            setCurrentMode(mode);\n        }\n    }[\"ChatInterface.useEffect\"], [\n        mode\n    ]);\n    // Save mode changes to localStorage\n    const handleModeChange = (newMode)=>{\n        setCurrentMode(newMode);\n        localStorage.setItem('chatMode', newMode);\n        if (newMode === 'heavy') {\n            localStorage.removeItem('selectedAgent');\n        }\n    };\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (scrollAreaRef.current) {\n            const scrollContainer = scrollAreaRef.current.querySelector(\"[data-radix-scroll-area-viewport]\");\n            if (scrollContainer) {\n                scrollContainer.scrollTop = scrollContainer.scrollHeight;\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    // Check API health on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkAPIHealth = {\n                \"ChatInterface.useEffect.checkAPIHealth\": async ()=>{\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.getHealth();\n                        setApiStatus('connected');\n                    } catch (error) {\n                        console.error('API health check failed:', error);\n                        setApiStatus('disconnected');\n                    }\n                }\n            }[\"ChatInterface.useEffect.checkAPIHealth\"];\n            checkAPIHealth();\n            // Check health every 30 seconds\n            const interval = setInterval(checkAPIHealth, 30000);\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearInterval(interval)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Auto-resize textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (textareaRef.current) {\n                textareaRef.current.style.height = \"auto\";\n                textareaRef.current.style.height = \"\".concat(Math.min(textareaRef.current.scrollHeight, 120), \"px\");\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        input\n    ]);\n    const handleFileUpload = (event)=>{\n        const files = event.target.files;\n        if (files) {\n            const newFiles = Array.from(files).map((file)=>({\n                    file,\n                    id: Math.random().toString(36).substr(2, 9)\n                }));\n            setUploadedFiles((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n        }\n        // Reset the input so the same file can be selected again\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const removeFile = (fileId)=>{\n        setUploadedFiles((prev)=>prev.filter((f)=>f.id !== fileId));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const submitToAgent = async (userMessage, files)=>{\n        setIsTyping(true);\n        try {\n            // Create initial agent message placeholder\n            const agentMessageId = Date.now().toString() + \"-agent\";\n            const agentMessage = {\n                id: agentMessageId,\n                role: \"agent\",\n                content: \"Processing your request...\",\n                timestamp: new Date(),\n                status: \"processing\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    agentMessage\n                ]);\n            // Submit query to API\n            const task = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.submitQueryWithStream({\n                query: userMessage,\n                mode: currentMode\n            }, (update)=>{\n                // Update the agent message with progress\n                setMessages((prev)=>prev.map((msg)=>msg.id === agentMessageId ? {\n                            ...msg,\n                            content: update.partial_result || msg.content,\n                            status: update.status,\n                            progress: update.progress\n                        } : msg));\n            }, (error)=>{\n                // Handle error\n                setMessages((prev)=>prev.map((msg)=>msg.id === agentMessageId ? {\n                            ...msg,\n                            content: \"Error: \".concat(error.message),\n                            status: \"failed\",\n                            error: error.message\n                        } : msg));\n                setIsTyping(false);\n            }, ()=>{\n                // Handle completion\n                setIsTyping(false);\n                setCurrentTaskId(null);\n            });\n            setCurrentTaskId(task.task_id);\n        } catch (error) {\n            console.error('Failed to submit query:', error);\n            // Update with error message\n            setMessages((prev)=>prev.map((msg)=>msg.role === \"agent\" && msg.content === \"Processing your request...\" ? {\n                        ...msg,\n                        content: \"Sorry, I encountered an error: \".concat(error instanceof Error ? error.message : 'Unknown error'),\n                        status: \"failed\",\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    } : msg));\n            setIsTyping(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!input.trim() && uploadedFiles.length === 0) return;\n        // Check API connection\n        if (apiStatus === 'disconnected') {\n            // Try to reconnect\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_6__.apiClient.getHealth();\n                setApiStatus('connected');\n            } catch (error) {\n                console.error('API still disconnected:', error);\n                return;\n            }\n        }\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: input.trim(),\n            timestamp: new Date(),\n            files: uploadedFiles.map((f)=>f.file)\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const messageContent = input.trim();\n        const messageFiles = uploadedFiles.map((f)=>f.file);\n        setInput(\"\");\n        setUploadedFiles([]);\n        if (!isChatMode) {\n            setIsChatMode(true);\n        }\n        // Submit to real agent API\n        await submitToAgent(messageContent, messageFiles);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const ChatBubble = (param)=>{\n        let { message } = param;\n        const isUser = message.role === \"user\";\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3 mb-6 \".concat(isUser ? \"justify-end\" : \"justify-start\"),\n            children: [\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: message.status === \"processing\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4 text-white animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 15\n                    }, this) : message.status === \"failed\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4 text-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"AI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-[70%] \".concat(isUser ? \"order-first\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-neutral-500\",\n                                    children: isUser ? \"You\" : \"AG3NT X\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-neutral-500\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                !isUser && message.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: message.status === \"completed\" ? \"default\" : message.status === \"failed\" ? \"destructive\" : \"secondary\",\n                                    className: \"text-xs\",\n                                    children: message.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            px-4 py-3 rounded-2xl text-sm leading-relaxed whitespace-pre-wrap\\n            \".concat(isUser ? \"bg-blue-500 text-white ml-auto rounded-br-md\" : \"bg-neutral-800 text-neutral-100 rounded-bl-md\", \"\\n          \"),\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this),\n                        message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 space-y-2\",\n                            children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\\n                  flex items-center gap-2 px-3 py-2 rounded-lg text-xs\\n                  \".concat(isUser ? \"bg-blue-700/50 text-blue-100\" : \"bg-neutral-700/50 text-neutral-300\", \"\\n                \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: file.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-500\",\n                                            children: [\n                                                \"(\",\n                                                formatFileSize(file.size),\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-7 w-7 p-0 hover:bg-neutral-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-3 w-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"U\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    };\n    const TypingIndicator = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex gap-3 mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0 mt-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-white\",\n                        children: \"AI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800 px-4 py-3 rounded-2xl rounded-bl-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"0ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"150ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-gray-500 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: \"300ms\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 336,\n            columnNumber: 5\n        }, this);\n    const FileUploadArea = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: uploadedFiles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3 space-y-2\",\n                children: uploadedFiles.map((uploadedFile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 px-3 py-2 bg-neutral-800/50 border border-neutral-700 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-300 truncate flex-1\",\n                                children: uploadedFile.file.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    \"(\",\n                                    formatFileSize(uploadedFile.file.size),\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"button\",\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>removeFile(uploadedFile.id),\n                                className: \"h-6 w-6 p-0 hover:bg-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-3 w-3 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, uploadedFile.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    if (!isChatMode) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 border-b border-neutral-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            currentMode === 'heavy' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium\",\n                                                children: currentMode === 'heavy' ? 'Multi-Agent Mode' : 'Single Agent Mode'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    selectedAgent && currentMode === 'single' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-400\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                className: \"bg-green-500/10 text-green-400 border-green-500/20\",\n                                                children: selectedAgent.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>handleModeChange(currentMode === 'heavy' ? 'single' : 'heavy'),\n                                    className: \"border-neutral-700 text-neutral-300 hover:bg-neutral-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Switch to \",\n                                        currentMode === 'heavy' ? 'Single' : 'Multi',\n                                        \"-Agent\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-2xl mx-auto px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl font-medium text-white mb-6 leading-tight\",\n                                children: \"How can I assist you?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-400 mb-12\",\n                                children: currentMode === 'heavy' ? 'Multi-agent mode: Deploy specialized agents working together for comprehensive analysis' : selectedAgent ? \"Single agent mode: Chat directly with \".concat(selectedAgent.name) : 'Single agent mode: Focused assistance from one specialized agent'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileUploadArea, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"textarea-container relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                ref: textareaRef,\n                                                value: input,\n                                                onChange: (e)=>setInput(e.target.value),\n                                                onKeyDown: handleKeyDown,\n                                                placeholder: \"Start a new conversation...\",\n                                                className: \"textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar\",\n                                                rows: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: fileInputRef,\n                                                type: \"file\",\n                                                multiple: true,\n                                                onChange: handleFileUpload,\n                                                className: \"hidden\",\n                                                accept: \"*/*\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    var _fileInputRef_current;\n                                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                },\n                                                className: \"absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10\",\n                                                title: \"Upload files\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                size: \"sm\",\n                                                className: \"absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10\",\n                                                disabled: !input.trim() && uploadedFiles.length === 0,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n            lineNumber: 380,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 px-4 border-b border-neutral-800 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center\",\n                                children: currentMode === 'heavy' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-medium text-white\",\n                                        children: [\n                                            \"AG3NT X \",\n                                            currentMode === 'heavy' ? 'Multi-Agent' : 'Single Agent'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full \".concat(apiStatus === 'connected' ? 'bg-green-500' : apiStatus === 'disconnected' ? 'bg-red-500' : 'bg-yellow-500')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-neutral-500\",\n                                                children: apiStatus === 'connected' ? 'Connected' : apiStatus === 'disconnected' ? 'Disconnected' : 'Checking...'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 15\n                                            }, this),\n                                            selectedAgent && currentMode === 'single' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-600\",\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-400\",\n                                                        children: selectedAgent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>setCurrentMode(currentMode === 'heavy' ? 'single' : 'heavy'),\n                            className: \"border-neutral-700 text-neutral-300 hover:bg-neutral-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                currentMode === 'heavy' ? 'Single' : 'Multi',\n                                \"-Agent\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, this),\n                    currentTaskId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"outline\",\n                        className: \"text-xs\",\n                        children: [\n                            \"Task: \",\n                            currentTaskId.slice(0, 8),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 478,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n                ref: scrollAreaRef,\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-0\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatBubble, {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this)),\n                        isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingIndicator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 24\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-neutral-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileUploadArea, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"textarea-container relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    ref: textareaRef,\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    placeholder: \"Type your message...\",\n                                    className: \"textarea-with-button w-full min-h-[48px] max-h-[120px] px-4 py-3 pr-20 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-lg resize-none dark-scrollbar\",\n                                    rows: 1,\n                                    disabled: isTyping\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    multiple: true,\n                                    onChange: handleFileUpload,\n                                    className: \"hidden\",\n                                    accept: \"*/*\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        var _fileInputRef_current;\n                                        return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                    },\n                                    className: \"absolute right-12 bottom-2 h-8 w-8 p-0 bg-neutral-700 hover:bg-neutral-600 border-0 z-10\",\n                                    title: \"Upload files\",\n                                    disabled: isTyping,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    size: \"sm\",\n                                    className: \"absolute right-2 bottom-2 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 border-0 z-10\",\n                                    disabled: !input.trim() && uploadedFiles.length === 0 || isTyping,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Copy_Loader2_Paperclip_Send_Settings_ThumbsDown_ThumbsUp_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\make-it-heavy-main\\\\make-it-heavy-main\\\\frontend\\\\chat-interface.tsx\",\n        lineNumber: 476,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"qxKdfXZekVRHpbfKZwKlpCTE0dM=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./chat-interface.tsx\n"));

/***/ })

});