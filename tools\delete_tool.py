from .base_tool import BaseTool
import os
import shutil

class DeleteTool(BaseTool):
    def __init__(self, config: dict):
        self.config = config

    @property
    def name(self) -> str:
        return "delete"

    @property
    def description(self) -> str:
        return "Delete a file or directory. Use with caution as this operation cannot be undone. Can delete directories recursively."

    @property
    def parameters(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "The file or directory path to delete"
                },
                "recursive": {
                    "type": "boolean",
                    "description": "Whether to delete directories recursively (default: False for safety)",
                    "default": False
                },
                "force": {
                    "type": "boolean",
                    "description": "Force deletion even if file is read-only (default: False)",
                    "default": False
                }
            },
            "required": ["path"]
        }

    def execute(self, path: str, recursive: bool = False, force: bool = False) -> dict:
        try:
            # Get absolute path
            abs_path = os.path.abspath(path)

            # Check if path exists
            if not os.path.exists(abs_path):
                return {"error": f"Path does not exist: {path}"}

            # Safety check - prevent deletion of important system directories
            dangerous_paths = [
                os.path.expanduser("~"),  # Home directory
                "/",  # Root directory (Unix)
                "C:\\",  # Root directory (Windows)
                "C:\\Windows",  # Windows system directory
                "C:\\Program Files",  # Program Files
                "C:\\Program Files (x86)",  # Program Files x86
            ]

            if abs_path in dangerous_paths:
                return {"error": f"Cannot delete system directory: {path}"}

            # Determine if it's a file or directory
            if os.path.isfile(abs_path):
                # Delete file
                if force and not os.access(abs_path, os.W_OK):
                    os.chmod(abs_path, 0o777)
                os.remove(abs_path)
                return {
                    "path": abs_path,
                    "success": True,
                    "message": f"Successfully deleted file: {path}",
                    "type": "file"
                }

            elif os.path.isdir(abs_path):
                # Delete directory
                if not recursive:
                    # Check if directory is empty
                    if os.listdir(abs_path):
                        return {"error": f"Directory is not empty: {path} (use recursive=True to delete non-empty directories)"}
                    os.rmdir(abs_path)
                else:
                    # Delete recursively
                    shutil.rmtree(abs_path, ignore_errors=force)

                return {
                    "path": abs_path,
                    "success": True,
                    "message": f"Successfully deleted directory: {path}",
                    "type": "directory",
                    "recursive": recursive
                }

            else:
                return {"error": f"Path is neither a file nor a directory: {path}"}

        except PermissionError:
            return {"error": f"Permission denied deleting: {path}"}
        except OSError as e:
            return {"error": f"OS error deleting: {str(e)}"}
        except Exception as e:
            return {"error": f"Failed to delete: {str(e)}"}