@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply antialiased;
  }
}

@layer components {
  .nav-link {
    @apply flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/10;
  }
  
  .nav-link.active {
    @apply bg-white/10;
  }
  
  .suggestion-card {
    @apply relative overflow-hidden text-left p-4 rounded-xl bg-gradient-to-br from-[#141414] to-[#1A1A1A] hover:from-[#1A1A1A] hover:to-[#222] transition-all duration-300 border border-white/5 hover:border-white/10 shadow-lg hover:shadow-xl hover:-translate-y-0.5;
  }
  
  .integration-card {
    @apply flex items-center space-x-4 p-4 rounded-xl bg-gradient-to-br from-[#141414] to-[#1A1A1A] hover:from-[#1A1A1A] hover:to-[#222] transition-all duration-300 border border-white/5 hover:border-white/10 shadow-lg hover:shadow-xl hover:-translate-y-0.5;
  }
  
  .chat-input {
    @apply w-full bg-gradient-to-r from-[#141414] to-[#161616] rounded-xl px-4 py-3.5 pr-12 focus:outline-none focus:ring-2 focus:ring-white/20 border border-white/5 shadow-lg transition-all duration-200 text-white;
  }
}
