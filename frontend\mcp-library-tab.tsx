"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Database,
  Globe,
  FileText,
  Calendar,
  Mail,
  Code,
  Search,
  Download,
  CheckCircle,
  Star,
  Settings,
  Package,
  Plus,
  Terminal,
  Brain,
  Zap,
  ExternalLink,
  Loader2
} from "lucide-react"
import { apiClient, type MCPInfo } from "@/lib/api"

interface McpServer {
  id: string
  name: string
  description: string
  author: string
  version: string
  category: string
  icon: React.ReactNode
  rating: number
  downloads: number
  tags: string[]
  status: "installed" | "available" | "updating"
  gradient: string
  lastUpdated: string
}

const getInitialMcpServers = (): McpServer[] => [
  {
    id: "ag3nt-x",
    name: "AG3NT X Agent",
    description:
      "Our comprehensive agent system with file operations, terminal commands, web search, calculations, and system management capabilities.",
    author: "AG3NT X",
    version: "1.0.0",
    category: "Development",
    icon: <Brain className="h-5 w-5" />,
    rating: 4.9,
    downloads: 1,
    tags: ["File Operations", "Terminal", "Web Search", "Calculations"],
    status: "installed",
    gradient: "from-blue-500 to-cyan-500",
    lastUpdated: "Active",
  },
  {
    id: "playwright",
    name: "Playwright MCP",
    description:
      "Professional web automation and scraping with Playwright. Navigate websites, take screenshots, extract content, and automate browser interactions.",
    author: "Anthropic",
    version: "latest",
    category: "Web",
    icon: <Globe className="h-5 w-5" />,
    rating: 4.8,
    downloads: 61200,
    tags: ["Playwright", "Web Automation", "Screenshots", "Browser"],
    status: "installed",
    gradient: "from-green-500 to-emerald-500",
    lastUpdated: "Active",
  },
  {
    id: "filesystem",
    name: "Filesystem MCP",
    description:
      "Secure file operations with configurable access controls. Read, write, create, delete, and manage files and directories with advanced permissions.",
    author: "Anthropic",
    version: "latest",
    category: "Development",
    icon: <FileText className="h-5 w-5" />,
    rating: 4.9,
    downloads: 45300,
    tags: ["Files", "Directories", "Security", "Permissions"],
    status: "available",
    gradient: "from-purple-500 to-pink-500",
    lastUpdated: "1 week ago",
  },
  {
    id: "git",
    name: "Git MCP",
    description:
      "Tools to read, search, and manipulate Git repositories. Manage commits, branches, and repository operations through MCP.",
    author: "Anthropic",
    version: "latest",
    category: "Development",
    icon: <Code className="h-5 w-5" />,
    rating: 4.8,
    downloads: 32100,
    tags: ["Git", "Version Control", "Repository", "Commits"],
    status: "available",
    gradient: "from-orange-500 to-red-500",
    lastUpdated: "3 days ago",
  },
  {
    id: "memory",
    name: "Memory MCP",
    description:
      "Knowledge graph-based persistent memory system. Store and retrieve information across conversations with intelligent context management.",
    author: "Anthropic",
    version: "latest",
    category: "AI",
    icon: <Brain className="h-5 w-5" />,
    rating: 4.7,
    downloads: 28900,
    tags: ["Memory", "Knowledge Graph", "Context", "AI"],
    status: "available",
    gradient: "from-yellow-500 to-orange-500",
    lastUpdated: "1 week ago",
  },
  {
    id: "fetch",
    name: "Fetch MCP",
    description:
      "Web content fetching and conversion for efficient LLM usage. Convert web pages to markdown and extract structured content.",
    author: "Anthropic",
    version: "latest",
    category: "Web",
    icon: <Download className="h-5 w-5" />,
    rating: 4.6,
    downloads: 19800,
    tags: ["Web Fetch", "Markdown", "Content", "Conversion"],
    status: "available",
    gradient: "from-indigo-500 to-purple-500",
    lastUpdated: "2 days ago",
  },
  {
    id: "time",
    name: "Time MCP",
    description:
      "Time and timezone conversion capabilities. Handle dates, times, and timezone operations with precision.",
    author: "Anthropic",
    version: "latest",
    category: "Utility",
    icon: <Zap className="h-5 w-5" />,
    rating: 4.5,
    downloads: 15600,
    tags: ["Time", "Timezone", "Date", "Conversion"],
    status: "available",
    gradient: "from-cyan-500 to-blue-500",
    lastUpdated: "1 week ago",
  }
]

const categories = ["All", "Development", "Web", "AI", "Utility", "Database", "Communication"]

const getStatusColor = (status: string) => {
  switch (status) {
    case "installed":
      return "bg-green-500"
    case "updating":
      return "bg-yellow-500"
    case "available":
      return "bg-gray-500"
    default:
      return "bg-gray-500"
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case "installed":
      return "Installed"
    case "updating":
      return "Updating"
    case "available":
      return "Available"
    default:
      return "Unknown"
  }
}

export default function McpLibraryTab() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [mcpServers, setMcpServers] = useState<McpServer[]>(getInitialMcpServers())
  const [mcpInfo, setMcpInfo] = useState<MCPInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [addServerDialog, setAddServerDialog] = useState(false)
  const [newServer, setNewServer] = useState({
    name: "",
    command: "",
    args: "",
    description: "",
    category: "Development"
  })

  useEffect(() => {
    const loadMcpInfo = async () => {
      try {
        const info = await apiClient.getMCPInfo()
        setMcpInfo(info)
      } catch (error) {
        console.error('Failed to load MCP info:', error)
      } finally {
        setLoading(false)
      }
    }

    loadMcpInfo()
  }, [])

  const filteredServers = mcpServers.filter((server) => {
    const matchesSearch =
      server.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      server.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      server.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesCategory = selectedCategory === "All" || server.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const handleInstall = async (serverId: string, serverName: string) => {
    console.log(`Installing MCP server: ${serverName} (ID: ${serverId})`)

    // Update server status to installing
    setMcpServers(prev => prev.map(server =>
      server.id === serverId
        ? { ...server, status: "updating" as const }
        : server
    ))

    // Simulate installation process
    setTimeout(() => {
      setMcpServers(prev => prev.map(server =>
        server.id === serverId
          ? { ...server, status: "installed" as const, lastUpdated: "Just now" }
          : server
      ))
    }, 2000)
  }

  const handleUninstall = async (serverId: string, serverName: string) => {
    console.log(`Uninstalling MCP server: ${serverName} (ID: ${serverId})`)

    setMcpServers(prev => prev.map(server =>
      server.id === serverId
        ? { ...server, status: "available" as const, lastUpdated: "Just now" }
        : server
    ))
  }

  const handleServerSettings = (serverId: string, serverName: string) => {
    console.log(`Opening settings for MCP server: ${serverName} (ID: ${serverId})`)
  }

  const handleAddServer = () => {
    if (!newServer.name || !newServer.command) return

    const server: McpServer = {
      id: `custom-${Date.now()}`,
      name: newServer.name,
      description: newServer.description || `Custom MCP server: ${newServer.name}`,
      author: "Custom",
      version: "1.0.0",
      category: newServer.category,
      icon: <Package className="h-5 w-5" />,
      rating: 4.0,
      downloads: 0,
      tags: ["Custom", newServer.category],
      status: "available",
      gradient: "from-gray-500 to-gray-600",
      lastUpdated: "Just added"
    }

    setMcpServers(prev => [...prev, server])
    setNewServer({ name: "", command: "", args: "", description: "", category: "Development" })
    setAddServerDialog(false)
  }

  const getStatusColorNew = (status: string) => {
    switch (status) {
      case "installed":
        return "bg-green-500"
      case "updating":
        return "bg-yellow-500"
      case "available":
        return "bg-neutral-500"
      default:
        return "bg-neutral-500"
    }
  }

  const getStatusTextNew = (status: string) => {
    switch (status) {
      case "installed":
        return "Installed"
      case "updating":
        return "Updating"
      case "available":
        return "Available"
      default:
        return "Unknown"
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="px-6 py-4 border-b border-neutral-800">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-xl font-semibold text-white">MCP Library</h1>
            <p className="text-sm text-neutral-400">Model Context Protocol servers and integrations</p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="bg-gray-800 text-gray-300">
              {filteredServers.length} of {mcpServers.length}
            </Badge>
            {mcpInfo && (
              <Badge className={`${mcpInfo.mcp_enabled ? 'bg-green-500/10 text-green-400 border-green-500/20' : 'bg-red-500/10 text-red-400 border-red-500/20'}`}>
                MCP {mcpInfo.mcp_enabled ? 'Enabled' : 'Disabled'}
              </Badge>
            )}
            <Dialog open={addServerDialog} onOpenChange={setAddServerDialog}>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-blue-gradient-hover text-white shadow-lg">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Server
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-neutral-900 border-neutral-800">
                <DialogHeader>
                  <DialogTitle className="text-white">Add Custom MCP Server</DialogTitle>
                  <DialogDescription className="text-neutral-400">
                    Add a custom MCP server to your library. You can install community servers or create your own.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-white">Server Name</Label>
                    <Input
                      id="name"
                      value={newServer.name}
                      onChange={(e) => setNewServer(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., My Custom Server"
                      className="bg-neutral-800 border-neutral-700 text-white"
                    />
                  </div>
                  <div>
                    <Label htmlFor="command" className="text-white">Command</Label>
                    <Input
                      id="command"
                      value={newServer.command}
                      onChange={(e) => setNewServer(prev => ({ ...prev, command: e.target.value }))}
                      placeholder="e.g., npx, python, node"
                      className="bg-neutral-800 border-neutral-700 text-white"
                    />
                  </div>
                  <div>
                    <Label htmlFor="args" className="text-white">Arguments</Label>
                    <Input
                      id="args"
                      value={newServer.args}
                      onChange={(e) => setNewServer(prev => ({ ...prev, args: e.target.value }))}
                      placeholder="e.g., -y @my/mcp-server"
                      className="bg-neutral-800 border-neutral-700 text-white"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description" className="text-white">Description</Label>
                    <Textarea
                      id="description"
                      value={newServer.description}
                      onChange={(e) => setNewServer(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe what this server does..."
                      className="bg-neutral-800 border-neutral-700 text-white"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={() => setAddServerDialog(false)} className="flex-1">
                      Cancel
                    </Button>
                    <Button onClick={handleAddServer} className="flex-1 bg-blue-gradient-hover text-white">
                      Add Server
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search MCP servers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40"
            />
          </div>

          <div className="flex gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant="ghost"
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className={`${
                  selectedCategory === category
                    ? "border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
                    : "text-gray-400 hover:text-white hover:bg-gray-800/50"
                }`}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* MCP Servers Grid */}
      <ScrollArea className="flex-1 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {filteredServers.map((server) => (
            <div
              key={server.id}
              className="group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200"
            >
              {/* Settings Gear Icon */}
              {server.status === "installed" && (
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleServerSettings(server.id, server.name)
                  }}
                  className="absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10"
                  title={`Settings for ${server.name}`}
                >
                  <Settings className="h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300" />
                </button>
              )}

              {/* Server Header */}
              <div className="flex items-start gap-4 mb-4">
                <div
                  className={`w-12 h-12 rounded-xl bg-gradient-to-br ${server.gradient} flex items-center justify-center text-white flex-shrink-0`}
                >
                  {server.icon}
                </div>

                <div className="flex-1 min-w-0 pr-8">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-white truncate">{server.name}</h3>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${getStatusColorNew(server.status)}`} />
                      <span className="text-xs text-neutral-400">{getStatusTextNew(server.status)}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 mb-2">
                    <p className="text-sm text-neutral-400">{server.category}</p>
                    <span className="text-xs text-gray-500">•</span>
                    <p className="text-xs text-gray-500">v{server.version}</p>
                    <span className="text-xs text-gray-500">•</span>
                    <p className="text-xs text-gray-500">by {server.author}</p>
                  </div>

                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                      <span>{server.rating}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download className="h-3 w-3" />
                      <span>{server.downloads.toLocaleString()}</span>
                    </div>
                    <span>Updated {server.lastUpdated}</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-neutral-400 mb-4 leading-relaxed">{server.description}</p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {server.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* Action Button */}
              <div className="flex gap-2">
                {server.status === "installed" ? (
                  <Button
                    onClick={() => handleUninstall(server.id, server.name)}
                    variant="outline"
                    className="flex-1 border-neutral-700 text-neutral-300 hover:bg-neutral-800 hover:text-white"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Installed
                  </Button>
                ) : server.status === "updating" ? (
                  <Button disabled className="flex-1 bg-yellow-600/20 text-yellow-400 border border-yellow-600/30">
                    <Package className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </Button>
                ) : (
                  <Button
                    onClick={() => handleInstall(server.id, server.name)}
                    className={`flex-1 bg-blue-gradient-hover text-white shadow-lg border-0 transition-all duration-200 group-hover:shadow-lg`}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Install
                  </Button>
                )}
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
          ))}
        </div>

        {filteredServers.length === 0 && (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Package className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">No MCP servers found</h3>
              <p className="text-sm text-gray-500 mb-4">Try adjusting your search or filter criteria</p>
              <Button
                variant="outline"
                onClick={() => window.open('https://github.com/modelcontextprotocol/servers', '_blank')}
                className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Browse Community Servers
              </Button>
            </div>
          </div>
        )}

        {/* Community Resources */}
        <div className="mt-8 p-4 bg-neutral-800 rounded-lg border border-neutral-700">
          <h3 className="text-white font-medium mb-2">Discover More MCP Servers</h3>
          <p className="text-neutral-400 text-sm mb-3">
            Explore the growing ecosystem of Model Context Protocol servers from the community.
          </p>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('https://github.com/modelcontextprotocol/servers', '_blank')}
              className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Official Servers
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('https://mcpservers.org', '_blank')}
              className="border-green-500/20 text-green-400 hover:bg-green-500/10"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Community Directory
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('https://modelcontextprotocol.io', '_blank')}
              className="border-purple-500/20 text-purple-400 hover:bg-purple-500/10"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Documentation
            </Button>
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
