"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Database,
  Globe,
  FileText,
  Calendar,
  Mail,
  Code,
  Search,
  Download,
  CheckCircle,
  Star,
  Settings,
  Package,
} from "lucide-react"

interface McpServer {
  id: string
  name: string
  description: string
  author: string
  version: string
  category: string
  icon: React.ReactNode
  rating: number
  downloads: number
  tags: string[]
  status: "installed" | "available" | "updating"
  gradient: string
  lastUpdated: string
}

const mcpServers: McpServer[] = [
  {
    id: "1",
    name: "Database Connector",
    description:
      "Connect to various databases including PostgreSQL, MySQL, MongoDB, and SQLite. Execute queries and manage data seamlessly.",
    author: "Anthropic",
    version: "2.1.0",
    category: "Database",
    icon: <Database className="h-5 w-5" />,
    rating: 4.9,
    downloads: 25600,
    tags: ["SQL", "PostgreSQL", "MySQL", "MongoDB"],
    status: "installed",
    gradient: "from-blue-500 to-cyan-500",
    lastUpdated: "2 days ago",
  },
  {
    id: "2",
    name: "Web Scraper Pro",
    description:
      "Advanced web scraping capabilities with support for dynamic content, authentication, and rate limiting.",
    author: "WebTools Inc",
    version: "1.8.3",
    category: "Web",
    icon: <Globe className="h-5 w-5" />,
    rating: 4.7,
    downloads: 18900,
    tags: ["Scraping", "Web", "API", "Automation"],
    status: "available",
    gradient: "from-green-500 to-emerald-500",
    lastUpdated: "1 week ago",
  },
  {
    id: "3",
    name: "Document Parser",
    description:
      "Parse and extract content from PDFs, Word documents, spreadsheets, and other file formats with AI-powered analysis.",
    author: "DocAI Labs",
    version: "3.0.1",
    category: "Documents",
    icon: <FileText className="h-5 w-5" />,
    rating: 4.8,
    downloads: 12400,
    tags: ["PDF", "Word", "Excel", "OCR"],
    status: "installed",
    gradient: "from-purple-500 to-pink-500",
    lastUpdated: "3 days ago",
  },
  {
    id: "4",
    name: "Calendar Integration",
    description:
      "Integrate with Google Calendar, Outlook, and other calendar services. Schedule meetings and manage events.",
    author: "TimeSync",
    version: "1.5.2",
    category: "Productivity",
    icon: <Calendar className="h-5 w-5" />,
    rating: 4.6,
    downloads: 8700,
    tags: ["Calendar", "Google", "Outlook", "Scheduling"],
    status: "available",
    gradient: "from-orange-500 to-red-500",
    lastUpdated: "5 days ago",
  },
  {
    id: "5",
    name: "Email Assistant",
    description:
      "Send, receive, and manage emails across multiple providers. Smart filtering and automated responses included.",
    author: "MailBot Co",
    version: "2.3.0",
    category: "Communication",
    icon: <Mail className="h-5 w-5" />,
    rating: 4.5,
    downloads: 15200,
    tags: ["Email", "Gmail", "Outlook", "Automation"],
    status: "updating",
    gradient: "from-yellow-500 to-orange-500",
    lastUpdated: "1 day ago",
  },
  {
    id: "6",
    name: "Code Repository",
    description:
      "Connect to GitHub, GitLab, and Bitbucket. Manage repositories, create pull requests, and analyze code.",
    author: "DevTools Pro",
    version: "1.9.4",
    category: "Development",
    icon: <Code className="h-5 w-5" />,
    rating: 4.9,
    downloads: 22100,
    tags: ["GitHub", "GitLab", "Git", "Code"],
    status: "available",
    gradient: "from-indigo-500 to-purple-500",
    lastUpdated: "4 days ago",
  },
]

const categories = ["All", "Database", "Web", "Documents", "Productivity", "Communication", "Development"]

const getStatusColor = (status: string) => {
  switch (status) {
    case "installed":
      return "bg-green-500"
    case "updating":
      return "bg-yellow-500"
    case "available":
      return "bg-gray-500"
    default:
      return "bg-gray-500"
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case "installed":
      return "Installed"
    case "updating":
      return "Updating"
    case "available":
      return "Available"
    default:
      return "Unknown"
  }
}

export default function McpLibraryTab() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")

  const filteredServers = mcpServers.filter((server) => {
    const matchesSearch =
      server.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      server.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      server.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesCategory = selectedCategory === "All" || server.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const handleInstall = (serverId: string, serverName: string) => {
    console.log(`Installing MCP server: ${serverName} (ID: ${serverId})`)
    // Placeholder for installation logic
  }

  const handleUninstall = (serverId: string, serverName: string) => {
    console.log(`Uninstalling MCP server: ${serverName} (ID: ${serverId})`)
    // Placeholder for uninstallation logic
  }

  const handleServerSettings = (serverId: string, serverName: string) => {
    console.log(`Opening settings for MCP server: ${serverName} (ID: ${serverId})`)
    // Placeholder for settings logic
  }

  const getStatusColorNew = (status: string) => {
    switch (status) {
      case "installed":
        return "bg-green-500"
      case "updating":
        return "bg-yellow-500"
      case "available":
        return "bg-neutral-500"
      default:
        return "bg-neutral-500"
    }
  }

  const getStatusTextNew = (status: string) => {
    switch (status) {
      case "installed":
        return "Installed"
      case "updating":
        return "Updating"
      case "available":
        return "Available"
      default:
        return "Unknown"
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="px-6 py-4 border-b border-neutral-800">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-xl font-semibold text-white">MCP Library</h1>
            <p className="text-sm text-neutral-400">Model Context Protocol servers and integrations</p>
          </div>
          <Badge variant="secondary" className="bg-gray-800 text-gray-300">
            {filteredServers.length} of {mcpServers.length}
          </Badge>
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search MCP servers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-neutral-900 border-neutral-800 text-white placeholder:text-neutral-500 focus:border-blue-500/40"
            />
          </div>

          <div className="flex gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant="ghost"
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className={`${
                  selectedCategory === category
                    ? "border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
                    : "text-gray-400 hover:text-white hover:bg-gray-800/50"
                }`}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* MCP Servers Grid */}
      <ScrollArea className="flex-1 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {filteredServers.map((server) => (
            <div
              key={server.id}
              className="group relative bg-neutral-900 border border-neutral-800 rounded-xl card-hover-blue p-5 transition-all duration-200"
            >
              {/* Settings Gear Icon */}
              {server.status === "installed" && (
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleServerSettings(server.id, server.name)
                  }}
                  className="absolute top-4 right-4 w-7 h-7 rounded-lg bg-neutral-900/70 hover:bg-neutral-800/70 border border-neutral-700/50 hover:border-neutral-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 z-10"
                  title={`Settings for ${server.name}`}
                >
                  <Settings className="h-3.5 w-3.5 text-neutral-400 hover:text-neutral-300" />
                </button>
              )}

              {/* Server Header */}
              <div className="flex items-start gap-4 mb-4">
                <div
                  className={`w-12 h-12 rounded-xl bg-gradient-to-br ${server.gradient} flex items-center justify-center text-white flex-shrink-0`}
                >
                  {server.icon}
                </div>

                <div className="flex-1 min-w-0 pr-8">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-white truncate">{server.name}</h3>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${getStatusColorNew(server.status)}`} />
                      <span className="text-xs text-neutral-400">{getStatusTextNew(server.status)}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 mb-2">
                    <p className="text-sm text-neutral-400">{server.category}</p>
                    <span className="text-xs text-gray-500">•</span>
                    <p className="text-xs text-gray-500">v{server.version}</p>
                    <span className="text-xs text-gray-500">•</span>
                    <p className="text-xs text-gray-500">by {server.author}</p>
                  </div>

                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                      <span>{server.rating}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download className="h-3 w-3" />
                      <span>{server.downloads.toLocaleString()}</span>
                    </div>
                    <span>Updated {server.lastUpdated}</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-neutral-400 mb-4 leading-relaxed">{server.description}</p>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {server.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs px-2 py-1 hover:bg-gray-700"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* Action Button */}
              <div className="flex gap-2">
                {server.status === "installed" ? (
                  <Button
                    onClick={() => handleUninstall(server.id, server.name)}
                    variant="outline"
                    className="flex-1 border-neutral-700 text-neutral-300 hover:bg-neutral-800 hover:text-white"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Installed
                  </Button>
                ) : server.status === "updating" ? (
                  <Button disabled className="flex-1 bg-yellow-600/20 text-yellow-400 border border-yellow-600/30">
                    <Package className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </Button>
                ) : (
                  <Button
                    onClick={() => handleInstall(server.id, server.name)}
                    className={`flex-1 bg-blue-gradient-hover text-white shadow-lg border-0 transition-all duration-200 group-hover:shadow-lg`}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Install
                  </Button>
                )}
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
          ))}
        </div>

        {filteredServers.length === 0 && (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Package className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">No MCP servers found</h3>
              <p className="text-sm text-gray-500">Try adjusting your search or filter criteria</p>
            </div>
          </div>
        )}
      </ScrollArea>
    </div>
  )
}
