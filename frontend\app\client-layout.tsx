"use client"

import React from "react"

import type { ReactNode } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Home, Users, Settings, ChevronDown, Package, BarChart3 } from "lucide-react"
import AgentsTab from "../agents-tab"
import McpLibraryTab from "../mcp-library-tab"
import Dashboard from "@/components/dashboard"
import ChatInterface from "../chat-interface"

export default function ClientLayout({ children }: { children: ReactNode }) {
  const [activeTab, setActiveTab] = React.useState("home")
  const [selectedAgent, setSelectedAgent] = React.useState<{id: string, name: string} | null>(null)
  const [chatMode, setChatMode] = React.useState<'single' | 'heavy'>('heavy')

  React.useEffect(() => {
    // Listen for navigation events from agents tab
    const handleNavigateToChat = (event: CustomEvent) => {
      const { agentId, agent<PERSON><PERSON> } = event.detail
      setSelectedAgent({ id: agentId, name: agent<PERSON><PERSON> })
      setChatMode('single')
      setActiveTab('home')
    }

    // Load saved agent and mode from localStorage
    const savedAgent = localStorage.getItem('selectedAgent')
    const savedMode = localStorage.getItem('chatMode')

    if (savedAgent) {
      setSelectedAgent(JSON.parse(savedAgent))
    }
    if (savedMode) {
      setChatMode(savedMode as 'single' | 'heavy')
    }

    window.addEventListener('navigateToChat', handleNavigateToChat as EventListener)
    return () => {
      window.removeEventListener('navigateToChat', handleNavigateToChat as EventListener)
    }
  }, [])

  const renderContent = () => {
    switch (activeTab) {
      case "dashboard":
        return <Dashboard />
      case "agents":
        return <AgentsTab />
      case "mcp-library":
        return <McpLibraryTab />
      case "settings":
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <h2 className="text-2xl font-medium text-white mb-4">Settings</h2>
              <p className="text-neutral-400">Settings panel coming soon...</p>
            </div>
          </div>
        )
      default:
        return <ChatInterface mode={chatMode} selectedAgent={selectedAgent} />
    }
  }

  return (
    <div className="dark">
      <div className="flex h-screen bg-black text-white p-4 gap-4">
        {/* Sidebar */}
        <div className="w-64 bg-neutral-900 border border-neutral-800 rounded-xl">
          {/* Header */}
          <div className="h-16 p-4 border-b border-neutral-800 flex items-center">
            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold">AG3NT</span>
              <span className="text-red-500 font-bold">X</span>
              <div className="ml-auto flex items-center gap-2">
                <span className="text-xs bg-blue-500/10 px-2 py-1 rounded text-blue-400 border border-blue-500/20">MCP</span>
                <span className="text-xs text-neutral-500">v1.0</span>
              </div>
            </div>
          </div>

          <ScrollArea className="h-[calc(100vh-140px)]">
            <div className="p-4 space-y-6">
              {/* New Chat Button */}
              <Button
                onClick={() => {
                  setSelectedAgent(null)
                  setChatMode('heavy')
                  setActiveTab('home')
                  localStorage.removeItem('selectedAgent')
                  localStorage.removeItem('chatMode')
                }}
                className="w-full bg-blue-gradient-hover text-white shadow-lg"
              >
                New Multi-Agent Chat
              </Button>

              {/* Explore Section */}
              <div>
                <h3 className="text-xs font-medium text-neutral-500 mb-3 uppercase tracking-wider">Explore</h3>
                <nav className="space-y-1">
                  <Button
                    variant="ghost"
                    onClick={() => setActiveTab("home")}
                    className={`w-full justify-start h-9 px-3 ${
                      activeTab === "home"
                        ? "bg-blue-500/10 text-blue-400"
                        : "text-neutral-400 hover:text-white hover:bg-blue-500/10"
                    }`}
                  >
                    <Home className="mr-3 h-4 w-4" />
                    Chat
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => setActiveTab("dashboard")}
                    className={`w-full justify-start h-9 px-3 ${
                      activeTab === "dashboard"
                        ? "bg-blue-500/10 text-blue-400"
                        : "text-neutral-400 hover:text-white hover:bg-blue-500/10"
                    }`}
                  >
                    <BarChart3 className="mr-3 h-4 w-4" />
                    Dashboard
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => setActiveTab("agents")}
                    className={`w-full justify-start h-9 px-3 ${
                      activeTab === "agents"
                        ? "bg-blue-500/10 text-blue-400"
                        : "text-neutral-400 hover:text-white hover:bg-blue-500/10"
                    }`}
                  >
                    <Users className="mr-3 h-4 w-4" />
                    Agents
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => setActiveTab("mcp-library")}
                    className={`w-full justify-start h-9 px-3 ${
                      activeTab === "mcp-library"
                        ? "bg-blue-500/10 text-blue-400"
                        : "text-neutral-400 hover:text-white hover:bg-blue-500/10"
                    }`}
                  >
                    <Package className="mr-3 h-4 w-4" />
                    MCP Library
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => setActiveTab("settings")}
                    className={`w-full justify-start h-9 px-3 ${
                      activeTab === "settings"
                        ? "bg-blue-500/10 text-blue-400"
                        : "text-neutral-400 hover:text-white hover:bg-blue-500/10"
                    }`}
                  >
                    <Settings className="mr-3 h-4 w-4" />
                    Settings
                  </Button>
                </nav>
              </div>

              {/* Conversations Section */}
              <div>
                <h3 className="text-xs font-medium text-neutral-500 mb-3 uppercase tracking-wider">Conversations</h3>
                <p className="text-sm text-neutral-500">No conversations</p>
              </div>
            </div>
          </ScrollArea>

          {/* Bottom User Section */}
          <div className="absolute bottom-4 left-4 right-4 w-56 p-4 border-t border-neutral-800 rounded-b-xl bg-gradient-to-t from-[#2b2b2b]/80 to-transparent backdrop-blur-sm">
            <Button
              variant="ghost"
              className="w-full justify-start h-10 px-3 text-neutral-400 hover:text-white hover:bg-blue-500/10"
            >
              <div className="flex items-center gap-3 w-full">
                <div className="h-6 w-6 rounded-full bg-neutral-600 flex-shrink-0" />
                <div className="flex-1 text-left">
                  <div className="text-sm">@ag3nt_x</div>
                  <div className="text-xs text-neutral-500 truncate">AG3NT X Agent</div>
                </div>
                <ChevronDown className="h-4 w-4 flex-shrink-0" />
              </div>
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-neutral-950 rounded-xl">{renderContent()}</div>
      </div>
    </div>
  )
}
