/**
 * Simple Calendar TypeScript Module
 * Provides basic calendar functionality including date generation and display
 */

class Calendar {
    private currentDate: Date;

    constructor(date: Date = new Date()) {
        this.currentDate = new Date(date);
    }

    /**
     * Get the current date
     */
    public getCurrentDate(): Date {
        return new Date(this.currentDate);
    }

    /**
     * Set a new current date
     */
    public setDate(date: Date): void {
        this.currentDate = new Date(date);
    }

    /**
     * Get the first day of the current month
     */
    public getFirstDayOfMonth(): Date {
        return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
    }

    /**
     * Get the last day of the current month
     */
    public getLastDayOfMonth(): Date {
        return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);
    }

    /**
     * Get the number of days in the current month
     */
    public getDaysInMonth(): number {
        return this.getLastDayOfMonth().getDate();
    }

    /**
     * Get the day of week for the first day of month (0 = Sunday, 1 = Monday, etc.)
     */
    public getFirstDayOfWeek(): number {
        return this.getFirstDayOfMonth().getDay();
    }

    /**
     * Get month name
     */
    public getMonthName(): string {
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        return monthNames[this.currentDate.getMonth()];
    }

    /**
     * Navigate to next month
     */
    public nextMonth(): void {
        this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
    }

    /**
     * Navigate to previous month
     */
    public previousMonth(): void {
        this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
    }

    /**
     * Generate a calendar grid for the current month
     * Returns a 2D array of dates for calendar display
     */
    public generateCalendarGrid(): (Date | null)[][] {
        const firstDay = this.getFirstDayOfMonth();
        const daysInMonth = this.getDaysInMonth();
        const startDayOfWeek = this.getFirstDayOfWeek();
        
        const weeks: (Date | null)[][] = [];
        let currentWeek: (Date | null)[] = [];
        
        // Add null days for days before month starts
        for (let i = 0; i < startDayOfWeek; i++) {
            currentWeek.push(null);
        }
        
        // Fill in the actual days
        for (let day = 1; day <= daysInMonth; day++) {
            if (currentWeek.length === 7) {
                weeks.push(currentWeek);
                currentWeek = [];
            }
            currentWeek.push(new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day));
        }
        
        // Fill remaining days in last week
        while (currentWeek.length < 7) {
            currentWeek.push(null);
        }
        
        weeks.push(currentWeek);
        return weeks;
    }

    /**
     * Format date to YYYY-MM-DD string
     */
    public static formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    /**
     * Display calendar in text format
     */
    public displayCalendar(): string {
        const weeks = this.generateCalendarGrid();
        let calendarText = `${this.getMonthName()} ${this.currentDate.getFullYear()}\n`;
        calendarText += 'Mo Tu We Th Fr Sa Su\n';
        
        weeks.forEach(week => {
            const weekText = week.map(day => {
                if (day === null) return '  ';
                const dayStr = day.getDate().toString().padStart(2, ' ');
                return dayStr;
            }).join(' ');
            calendarText += weekText + '\n';
        });
        
        return calendarText;
    }
}

// Export for use in other modules
export { Calendar };

// Example usage
if (require.main === module) {
    const calendar = new Calendar();
    console.log("Current Calendar:");
    console.log(calendar.displayCalendar());
    
    console.log("\nNext Month:");
    calendar.nextMonth();
    console.log(calendar.displayCalendar());
    
    console.log("\nPrevious Month:");
    calendar.previousMonth();
    calendar.previousMonth();
    console.log(calendar.displayCalendar());
}